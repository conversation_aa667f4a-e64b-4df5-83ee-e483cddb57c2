#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test if AI is actually analyzing images or just using Excel data
"""

import sys
import os
import json
from pathlib import Path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_image_vs_excel_analysis():
    """Test whether AI analyzes images or just uses Excel data"""
    print("🧪 Image Analysis vs Excel Data Test")
    print("=" * 60)
    
    # Test configuration
    test_config = {
        "excel_file": "Test_description.xlsx",
        "image_dir": "New Product Picture",
        "image_column": "Product Picture",
        "test_row": 0
    }
    
    print("📋 Test Purpose:")
    print("   This test will help determine if the AI is actually analyzing")
    print("   the product images or just using Excel data to generate descriptions.")
    print()
    
    # Check if files exist
    excel_exists = os.path.exists(test_config["excel_file"])
    image_dir_exists = os.path.exists(test_config["image_dir"])
    
    if not excel_exists or not image_dir_exists:
        print("❌ Required files not found. Please ensure:")
        print(f"   - Excel file: {test_config['excel_file']}")
        print(f"   - Image directory: {test_config['image_dir']}")
        return
    
    try:
        # Initialize components
        from core.data_processor import ExcelProcessor, ImageProcessor
        from core.ai_models import AIModelManager
        from core.cost_calculator import CostCalculator
        from core.prompt_manager import PromptManager
        from config.settings import load_config
        
        config = load_config()
        
        # Load Excel data
        excel_processor = ExcelProcessor()
        excel_processor.load_excel(test_config["excel_file"])
        excel_processor.set_image_column(test_config["image_column"])
        
        # Load image processor
        image_processor = ImageProcessor(test_config["image_dir"])
        
        # Load AI manager
        cost_calculator = CostCalculator()
        ai_manager = AIModelManager(config, cost_calculator)
        
        # Load prompt manager
        prompt_manager = PromptManager(config.get('paths.prompts_dir'))
        
        print("✅ All components loaded successfully")
        
        # Get test data
        test_row = test_config["test_row"]
        product_data = excel_processor.get_row_data(test_row)
        product_name = product_data.get("Product Name", f"Product_{test_row}")
        
        print(f"\n📊 Test Data for Row {test_row}:")
        print(f"   Product Name: {product_name}")
        print(f"   Excel Data: {json.dumps(product_data, indent=2, ensure_ascii=False)}")
        
        # Get images
        image_filename = excel_processor.get_image_filename(test_row)
        images = image_processor.find_images(image_filename) if image_filename else []
        
        print(f"\n🖼️ Image Information:")
        print(f"   Image filename from Excel: '{image_filename}'")
        print(f"   Found {len(images)} images: {images}")
        
        if not images:
            print("❌ No images found - cannot test image analysis")
            return
        
        # Test 1: Generate description WITH images
        print(f"\n🧪 Test 1: AI Generation WITH Images")
        print("-" * 40)
        
        try:
            # Format prompt
            formatted_prompt = prompt_manager.format_prompt(
                "furniture",
                product_name=product_name,
                product_data=product_data
            )
            
            # Call AI with images
            result_with_images = ai_manager.generate_text(
                "openai-gpt4o-mini",  # Use vision-capable model
                formatted_prompt,
                images
            )
            
            if result_with_images.get('success'):
                content_with_images = result_with_images.get('content', '')
                print("✅ AI generation with images successful")
                print(f"📄 Content length: {len(content_with_images)} characters")
                
                # Check for image analysis section
                if "####" in content_with_images and "Image Analysis" in content_with_images:
                    print("✅ Image Analysis section found")
                    
                    # Extract image analysis section
                    lines = content_with_images.split('\n')
                    in_image_section = False
                    image_analysis = []
                    
                    for line in lines:
                        if "####" in line and "Image Analysis" in line:
                            in_image_section = True
                            continue
                        elif "####" in line and in_image_section:
                            break
                        elif in_image_section:
                            image_analysis.append(line)
                    
                    image_analysis_text = '\n'.join(image_analysis).strip()
                    print(f"🔍 Image Analysis Content:")
                    print(f"   {image_analysis_text[:200]}...")
                    
                    # Check if it's actually analyzing the image
                    image_indicators = [
                        "shows", "visible", "appears", "features", "displays",
                        "color", "material", "texture", "design", "shape",
                        "wooden", "metal", "fabric", "leather", "plastic"
                    ]
                    
                    found_indicators = [word for word in image_indicators if word.lower() in image_analysis_text.lower()]
                    
                    if found_indicators:
                        print(f"✅ Image analysis indicators found: {found_indicators}")
                        print("🎯 CONCLUSION: AI appears to be analyzing the actual image")
                    else:
                        print("❌ No clear image analysis indicators found")
                        print("🎯 CONCLUSION: AI might be using only Excel data")
                        
                else:
                    print("❌ No Image Analysis section found")
                    print("🎯 CONCLUSION: AI is not following image analysis instructions")
                    
            else:
                print(f"❌ AI generation failed: {result_with_images.get('error')}")
                
        except Exception as e:
            print(f"❌ Test 1 failed: {e}")
        
        # Test 2: Generate description WITHOUT images (for comparison)
        print(f"\n🧪 Test 2: AI Generation WITHOUT Images")
        print("-" * 40)
        
        try:
            result_without_images = ai_manager.generate_text(
                "openai-gpt4o-mini",
                formatted_prompt,
                []  # No images
            )
            
            if result_without_images.get('success'):
                content_without_images = result_without_images.get('content', '')
                print("✅ AI generation without images successful")
                print(f"📄 Content length: {len(content_without_images)} characters")
                
                # Compare content
                if 'content_with_images' in locals():
                    similarity = len(set(content_with_images.split()) & set(content_without_images.split()))
                    total_words = len(set(content_with_images.split()) | set(content_without_images.split()))
                    similarity_ratio = similarity / total_words if total_words > 0 else 0
                    
                    print(f"📊 Content Similarity: {similarity_ratio:.2%}")
                    
                    if similarity_ratio > 0.8:
                        print("⚠️ High similarity - AI might not be using image data effectively")
                    else:
                        print("✅ Low similarity - AI appears to be using image data")
                        
            else:
                print(f"❌ AI generation without images failed: {result_without_images.get('error')}")
                
        except Exception as e:
            print(f"❌ Test 2 failed: {e}")
        
        # Final analysis
        print(f"\n🎯 Final Analysis:")
        print(f"   1. Check if 'Image Analysis' section appears in output")
        print(f"   2. Look for specific visual descriptions (colors, materials, shapes)")
        print(f"   3. Compare content with/without images")
        print(f"   4. Verify AI model supports vision (GPT-4o, GPT-4o mini, Claude)")
        
        print(f"\n💡 To improve image analysis:")
        print(f"   1. Use vision-capable models (GPT-4o, GPT-4o mini)")
        print(f"   2. Ensure 'Enable Image Assistance' is checked")
        print(f"   3. Verify images are high quality and clear")
        print(f"   4. Check prompt includes image analysis instructions")
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")

if __name__ == "__main__":
    test_image_vs_excel_analysis()
