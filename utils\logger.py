#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Logging Utilities for AI Description Editor
日誌工具模組
"""

import os
import sys
from pathlib import Path
from loguru import logger
from datetime import datetime


def setup_logger(log_level: str = "INFO") -> object:
    """
    設定日誌系統
    
    Args:
        log_level: 日誌等級 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    
    Returns:
        logger: 配置好的日誌物件
    """
    # 移除預設的 handler
    logger.remove()
    
    # 取得專案根目錄
    project_root = Path(__file__).parent.parent
    logs_dir = project_root / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # 設定日誌格式
    log_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 控制台輸出
    logger.add(
        sys.stdout,
        format=log_format,
        level=log_level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 一般日誌檔案
    today = datetime.now().strftime("%Y%m%d")
    logger.add(
        logs_dir / f"app_{today}.log",
        format=log_format,
        level="DEBUG",
        rotation="1 day",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # 錯誤日誌檔案
    logger.add(
        logs_dir / f"error_{today}.log",
        format=log_format,
        level="ERROR",
        rotation="1 day",
        retention="90 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # API 使用統計日誌
    logger.add(
        logs_dir / "usage_stats.csv",
        format="{time:YYYY-MM-DD HH:mm:ss},{level},{message}",
        level="INFO",
        filter=lambda record: "API_USAGE" in record["message"],
        rotation="1 week",
        retention="1 year"
    )
    
    return logger


def log_api_usage(model: str, tokens_used: int, cost: float = 0.0):
    """
    記錄 API 使用統計
    
    Args:
        model: AI 模型名稱
        tokens_used: 使用的 token 數量
        cost: 費用
    """
    logger.info(f"API_USAGE,{model},{tokens_used},{cost}")


def log_processing_stats(
    total_items: int,
    processed_items: int,
    success_count: int,
    error_count: int,
    processing_time: float
):
    """
    記錄處理統計資訊
    
    Args:
        total_items: 總項目數
        processed_items: 已處理項目數
        success_count: 成功數量
        error_count: 錯誤數量
        processing_time: 處理時間（秒）
    """
    logger.info(
        f"PROCESSING_STATS,{total_items},{processed_items},"
        f"{success_count},{error_count},{processing_time:.2f}"
    )
