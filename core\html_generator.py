#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML Generation Module
HTML 生成模組 - 處理標準化 HTML 輸出和模板管理
"""

import re
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup
from loguru import logger


class HTMLGenerator:
    """HTML 生成器"""
    
    def __init__(self, template_config: Dict[str, Any] = None):
        self.template_config = template_config or self._get_default_template()
        self.required_structure = self._parse_template_structure()
    
    def _get_default_template(self) -> Dict[str, Any]:
        """取得預設模板配置"""
        return {
            "structure": [
                "h1:產品名稱",
                "h2:Description",
                "h3:Product Overview",
                "h3:Main Benefits",
                "h2:Ingredients",
                "h3:Active Ingredients",
                "h3:Free From / Allergy Info",
                "h2:How to Use",
                "h3:Dosage",
                "h3:Usage Warnings",
                "h2:Additional Information",
                "h3:Miscellaneous"
            ]
        }
    
    def _parse_template_structure(self) -> List[Dict[str, str]]:
        """解析模板結構"""
        structure = []
        
        for item in self.template_config.get("structure", []):
            if ":" in item:
                tag, title = item.split(":", 1)
                structure.append({
                    "tag": tag.strip(),
                    "title": title.strip()
                })
        
        return structure
    
    def validate_html_structure(self, html_content: str) -> Dict[str, Any]:
        """
        驗證 HTML 結構是否符合要求
        
        Args:
            html_content: HTML 內容
            
        Returns:
            Dict[str, Any]: 驗證結果
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            result = {
                "valid": True,
                "missing_elements": [],
                "extra_elements": [],
                "structure_issues": [],
                "warnings": []
            }
            
            # 檢查必要的結構元素
            for item in self.required_structure:
                tag = item["tag"]
                title = item["title"]
                
                # 尋找對應的標籤
                elements = soup.find_all(tag)
                found = False
                
                for element in elements:
                    if title in element.get_text():
                        found = True
                        break
                
                if not found:
                    result["missing_elements"].append(f"{tag}: {title}")
                    result["valid"] = False
            
            # 檢查是否有多餘的高層級標籤
            high_level_tags = soup.find_all(['h1', 'h2'])
            expected_h1_count = len([item for item in self.required_structure if item["tag"] == "h1"])
            expected_h2_count = len([item for item in self.required_structure if item["tag"] == "h2"])
            
            actual_h1_count = len(soup.find_all('h1'))
            actual_h2_count = len(soup.find_all('h2'))
            
            if actual_h1_count != expected_h1_count:
                result["warnings"].append(f"H1 標籤數量不符: 期望 {expected_h1_count}，實際 {actual_h1_count}")
            
            if actual_h2_count != expected_h2_count:
                result["warnings"].append(f"H2 標籤數量不符: 期望 {expected_h2_count}，實際 {actual_h2_count}")
            
            return result
            
        except Exception as e:
            logger.error(f"HTML 結構驗證失敗: {e}")
            return {
                "valid": False,
                "error": str(e),
                "missing_elements": [],
                "extra_elements": [],
                "structure_issues": [],
                "warnings": []
            }
    
    def clean_html_content(self, html_content: str) -> str:
        """
        清理 HTML 內容
        
        Args:
            html_content: 原始 HTML 內容
            
        Returns:
            str: 清理後的 HTML 內容
        """
        try:
            # 移除 AI 可能產生的多餘標記
            html_content = re.sub(r'```html\s*', '', html_content)
            html_content = re.sub(r'```\s*$', '', html_content)
            
            # 解析 HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除空的標籤
            for tag in soup.find_all():
                if not tag.get_text(strip=True) and not tag.find_all():
                    tag.decompose()
            
            # 格式化輸出
            formatted_html = soup.prettify()
            
            # 移除多餘的空行
            lines = formatted_html.split('\n')
            cleaned_lines = []
            
            for line in lines:
                stripped_line = line.strip()
                if stripped_line:
                    cleaned_lines.append(line)
                elif cleaned_lines and cleaned_lines[-1].strip():
                    cleaned_lines.append('')  # 保留一個空行
            
            return '\n'.join(cleaned_lines)
            
        except Exception as e:
            logger.error(f"HTML 清理失敗: {e}")
            return html_content
    
    def extract_product_name(self, html_content: str) -> str:
        """
        從 HTML 中提取產品名稱
        
        Args:
            html_content: HTML 內容
            
        Returns:
            str: 產品名稱
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            h1_tag = soup.find('h1')
            
            if h1_tag:
                return h1_tag.get_text(strip=True)
            
            return "未知產品"
            
        except Exception as e:
            logger.error(f"提取產品名稱失敗: {e}")
            return "未知產品"
    
    def generate_preview_html(self, html_content: str, include_styles: bool = True) -> str:
        """
        生成預覽用的完整 HTML 頁面
        
        Args:
            html_content: 產品描述 HTML 內容
            include_styles: 是否包含樣式
            
        Returns:
            str: 完整的 HTML 頁面
        """
        product_name = self.extract_product_name(html_content)
        
        styles = """
        <style>
            body {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                line-height: 1.6;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f9f9f9;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #2c3e50;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
                margin-bottom: 30px;
            }
            h2 {
                color: #34495e;
                margin-top: 30px;
                margin-bottom: 15px;
                border-left: 4px solid #3498db;
                padding-left: 15px;
            }
            h3 {
                color: #7f8c8d;
                margin-top: 20px;
                margin-bottom: 10px;
            }
            p {
                margin-bottom: 15px;
                text-align: justify;
            }
            ul, ol {
                margin-bottom: 15px;
                padding-left: 30px;
            }
            li {
                margin-bottom: 5px;
            }
            .highlight {
                background-color: #fff3cd;
                padding: 2px 4px;
                border-radius: 3px;
            }
        </style>
        """ if include_styles else ""
        
        full_html = f"""<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{product_name} - 產品描述預覽</title>
    {styles}
</head>
<body>
    <div class="container">
        {html_content}
    </div>
</body>
</html>"""
        
        return full_html
    
    def extract_sections(self, html_content: str) -> Dict[str, str]:
        """
        從 HTML 中提取各個區段的內容
        
        Args:
            html_content: HTML 內容
            
        Returns:
            Dict[str, str]: 各區段內容字典
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            sections = {}
            
            current_section = None
            current_content = []
            
            for element in soup.find_all(['h1', 'h2', 'h3', 'p', 'ul', 'ol']):
                if element.name in ['h1', 'h2', 'h3']:
                    # 儲存前一個區段
                    if current_section:
                        sections[current_section] = '\n'.join(current_content)
                    
                    # 開始新區段
                    current_section = element.get_text(strip=True)
                    current_content = []
                else:
                    # 加入內容到當前區段
                    if current_section:
                        current_content.append(element.get_text(strip=True))
            
            # 儲存最後一個區段
            if current_section:
                sections[current_section] = '\n'.join(current_content)
            
            return sections
            
        except Exception as e:
            logger.error(f"提取 HTML 區段失敗: {e}")
            return {}
    
    def get_template_structure(self) -> List[Dict[str, str]]:
        """取得模板結構"""
        return self.required_structure.copy()
    
    def update_template_structure(self, structure: List[str]):
        """
        更新模板結構
        
        Args:
            structure: 新的結構列表
        """
        self.template_config["structure"] = structure
        self.required_structure = self._parse_template_structure()
        logger.info("模板結構已更新")
