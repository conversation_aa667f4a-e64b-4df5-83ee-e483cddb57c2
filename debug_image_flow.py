#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive debug script for image processing flow
"""

import sys
import os
import pandas as pd
from pathlib import Path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.data_processor import ExcelProcessor, ImageProcessor

def debug_complete_image_flow():
    """Debug the complete image processing flow"""
    print("🔍 Complete Image Processing Flow Debug")
    print("=" * 60)
    
    # Step 1: Get Excel file
    excel_file = input("Enter your Excel file path: ").strip()
    if not excel_file or not os.path.exists(excel_file):
        print("❌ Excel file not found")
        return
    
    # Step 2: Get image directory
    image_dir = input("Enter your image directory path: ").strip()
    if not image_dir or not os.path.exists(image_dir):
        print("❌ Image directory not found")
        return
    
    # Step 3: Get image column name
    image_column = input("Enter your image column name: ").strip()
    if not image_column:
        print("❌ Image column name required")
        return
    
    print(f"\n📋 Configuration:")
    print(f"   Excel file: {excel_file}")
    print(f"   Image directory: {image_dir}")
    print(f"   Image column: {image_column}")
    
    # Step 4: Initialize processors
    try:
        excel_processor = ExcelProcessor()
        excel_processor.load_excel(excel_file)
        print("✅ Excel processor initialized")
        
        image_processor = ImageProcessor(image_dir)
        print("✅ Image processor initialized")
    except Exception as e:
        print(f"❌ Failed to initialize processors: {e}")
        return
    
    # Step 5: Check if image column exists
    columns = excel_processor.get_columns()
    print(f"\n📊 Available columns: {columns}")
    
    if image_column not in columns:
        print(f"❌ Image column '{image_column}' not found in Excel")
        print(f"   Available columns: {columns}")
        return
    
    # Step 6: Set image column
    excel_processor.set_image_column(image_column)
    print(f"✅ Image column set to: {image_column}")
    
    # Step 7: Test image processing for first few rows
    print(f"\n🔍 Testing image processing for first 5 rows:")
    data_length = len(excel_processor.data)
    test_rows = min(5, data_length)
    
    for row_index in range(test_rows):
        print(f"\n--- Row {row_index + 1} ---")
        
        # Get image filename from Excel
        image_filename = excel_processor.get_image_filename(row_index)
        print(f"   Image filename from Excel: '{image_filename}'")
        
        if not image_filename:
            print("   ❌ No image filename found")
            continue
        
        # Search for images
        found_images = image_processor.find_images(image_filename)
        print(f"   Found {len(found_images)} images:")
        
        for img_path in found_images:
            print(f"     - {img_path}")
            
            # Check if file exists
            if os.path.exists(img_path):
                file_size = os.path.getsize(img_path)
                print(f"       ✅ File exists ({file_size:,} bytes)")
            else:
                print(f"       ❌ File does not exist")
        
        if not found_images:
            print("   ❌ No images found")
            print(f"   🔍 Searching in directory: {image_dir}")
            
            # List all files in directory for debugging
            all_files = list(Path(image_dir).glob("*"))
            print(f"   📁 Directory contains {len(all_files)} files:")
            for f in all_files[:10]:  # Show first 10 files
                print(f"     - {f.name}")
            if len(all_files) > 10:
                print(f"     ... and {len(all_files) - 10} more files")
    
    # Step 8: Test image processing settings
    print(f"\n⚙️ Image Processing Settings Check:")
    
    # Check if enable_images would be True
    print(f"   Image processor exists: {image_processor is not None}")
    print(f"   Image column set: {excel_processor.image_column}")
    
    # Simulate the processing engine logic
    print(f"\n🔄 Simulating Processing Engine Logic:")
    row_index = 0
    enable_images = True
    
    if enable_images and image_processor:
        image_filename = excel_processor.get_image_filename(row_index)
        print(f"   enable_images: {enable_images}")
        print(f"   image_processor exists: {image_processor is not None}")
        print(f"   image_filename: '{image_filename}'")
        
        if image_filename:
            images = image_processor.find_images(image_filename)
            print(f"   Found images: {images}")
            
            if images:
                print("   ✅ Images would be passed to AI model")
            else:
                print("   ❌ No images would be passed to AI model")
        else:
            print("   ❌ No image filename, no images would be passed")
    else:
        print("   ❌ Images disabled or processor missing")
    
    print(f"\n🎯 Summary:")
    print(f"   Excel file: {'✅ Loaded' if excel_processor.data is not None else '❌ Failed'}")
    print(f"   Image directory: {'✅ Valid' if image_processor else '❌ Invalid'}")
    print(f"   Image column: {'✅ Found' if image_column in columns else '❌ Missing'}")
    
    # Final recommendations
    print(f"\n💡 Recommendations:")
    print(f"   1. Make sure 'Enable Image Assistance' is checked in the app")
    print(f"   2. Select image directory: {image_dir}")
    print(f"   3. Select image column: {image_column}")
    print(f"   4. Ensure image filenames in Excel match actual files")
    print(f"   5. Use GPT-4o or GPT-4o mini for image processing")

if __name__ == "__main__":
    debug_complete_image_flow()
