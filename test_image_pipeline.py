#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete image processing pipeline test
"""

import sys
import os
from pathlib import Path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_image_pipeline():
    """Test the complete image processing pipeline"""
    print("🧪 Complete Image Processing Pipeline Test")
    print("=" * 60)
    
    # Test configuration
    test_config = {
        "excel_file": "Test_description.xlsx",  # Your test file
        "image_dir": "New Product Picture",     # Your image directory
        "image_column": "Product Picture",      # Adjust this to your actual column name
        "test_row": 0                          # Test first row
    }
    
    print("📋 Test Configuration:")
    for key, value in test_config.items():
        print(f"   {key}: {value}")
    
    # Step 1: Check files exist
    print(f"\n📁 File System Check:")
    excel_exists = os.path.exists(test_config["excel_file"])
    image_dir_exists = os.path.exists(test_config["image_dir"])
    
    print(f"   Excel file: {'✅' if excel_exists else '❌'} {test_config['excel_file']}")
    print(f"   Image directory: {'✅' if image_dir_exists else '❌'} {test_config['image_dir']}")
    
    if not excel_exists or not image_dir_exists:
        print("❌ Required files/directories not found. Please update test_config above.")
        return
    
    # Step 2: Test Excel processing
    print(f"\n📊 Excel Processing Test:")
    try:
        from core.data_processor import ExcelProcessor
        excel_processor = ExcelProcessor()
        excel_processor.load_excel(test_config["excel_file"])
        
        columns = excel_processor.get_columns()
        print(f"   ✅ Excel loaded with {len(columns)} columns")
        print(f"   📋 Columns: {columns}")
        
        if test_config["image_column"] not in columns:
            print(f"   ❌ Image column '{test_config['image_column']}' not found")
            print(f"   💡 Available columns: {columns}")
            return
        
        excel_processor.set_image_column(test_config["image_column"])
        print(f"   ✅ Image column set to: {test_config['image_column']}")
        
    except Exception as e:
        print(f"   ❌ Excel processing failed: {e}")
        return
    
    # Step 3: Test image processing
    print(f"\n🖼️ Image Processing Test:")
    try:
        from core.data_processor import ImageProcessor
        image_processor = ImageProcessor(test_config["image_dir"])
        
        validation = image_processor.validate_images_directory()
        print(f"   Directory valid: {'✅' if validation['valid'] else '❌'}")
        print(f"   Total images: {validation['total_images']}")
        print(f"   Formats: {validation['supported_formats']}")
        
        if not validation['valid']:
            print(f"   ❌ Image directory validation failed")
            return
            
    except Exception as e:
        print(f"   ❌ Image processor initialization failed: {e}")
        return
    
    # Step 4: Test image filename extraction
    print(f"\n🔍 Image Filename Extraction Test:")
    try:
        test_row = test_config["test_row"]
        image_filename = excel_processor.get_image_filename(test_row)
        print(f"   Row {test_row} image filename: '{image_filename}'")
        
        if not image_filename:
            print(f"   ❌ No image filename found in row {test_row}")
            return
        
        found_images = image_processor.find_images(image_filename)
        print(f"   Found {len(found_images)} images:")
        for img in found_images:
            print(f"     - {img}")
            
        if not found_images:
            print(f"   ❌ No images found for filename '{image_filename}'")
            return
            
    except Exception as e:
        print(f"   ❌ Image filename extraction failed: {e}")
        return
    
    # Step 5: Test AI model initialization
    print(f"\n🤖 AI Model Test:")
    try:
        from core.ai_models import AIModelManager
        from core.cost_calculator import CostCalculator
        from config.settings import load_config
        
        config = load_config()
        cost_calculator = CostCalculator()
        ai_manager = AIModelManager(config, cost_calculator)
        
        available_models = ai_manager.get_available_models()
        print(f"   ✅ AI Manager initialized with {len(available_models)} models")
        
        # Test GPT-4o mini specifically
        if 'openai-gpt4o-mini' in available_models:
            print(f"   ✅ GPT-4o mini available")
        else:
            print(f"   ❌ GPT-4o mini not available")
            print(f"   Available models: {available_models}")
            
    except Exception as e:
        print(f"   ❌ AI model initialization failed: {e}")
        return
    
    # Step 6: Test complete processing engine
    print(f"\n⚙️ Processing Engine Test:")
    try:
        from core.processing_engine import ProcessingEngine
        from core.prompt_manager import PromptManager
        from core.keyword_manager import KeywordManager
        from core.html_generator import HTMLGenerator
        
        prompt_manager = PromptManager(config.get('paths.prompts_dir'))
        keyword_manager = KeywordManager(config.get('paths.categories_dir'))
        html_generator = HTMLGenerator(config.get('html_template'))
        
        processing_engine = ProcessingEngine(
            excel_processor=excel_processor,
            ai_manager=ai_manager,
            prompt_manager=prompt_manager,
            keyword_manager=keyword_manager,
            html_generator=html_generator,
            image_processor=image_processor  # This is the key!
        )
        
        print(f"   ✅ Processing engine created successfully")
        print(f"   ✅ Image processor attached: {processing_engine.image_processor is not None}")
        
    except Exception as e:
        print(f"   ❌ Processing engine creation failed: {e}")
        return
    
    # Step 7: Final summary
    print(f"\n🎯 Pipeline Test Summary:")
    print(f"   ✅ Excel file loaded and image column configured")
    print(f"   ✅ Image directory validated and processor created")
    print(f"   ✅ Image filename extraction working")
    print(f"   ✅ Images found for test filename")
    print(f"   ✅ AI models available including GPT-4o mini")
    print(f"   ✅ Processing engine created with image processor")
    
    print(f"\n💡 Next Steps:")
    print(f"   1. Run the application")
    print(f"   2. Load Excel file: {test_config['excel_file']}")
    print(f"   3. Select image directory: {test_config['image_dir']}")
    print(f"   4. Select image column: {test_config['image_column']}")
    print(f"   5. ✅ Check 'Enable Image Assistance'")
    print(f"   6. Select GPT-4o mini (should show ✅圖片理解)")
    print(f"   7. Process one item and check for detailed image analysis")
    
    print(f"\n🎉 Image processing pipeline is ready!")

if __name__ == "__main__":
    test_image_pipeline()
