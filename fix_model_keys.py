#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Model Keys Issue
修復模型鍵值問題
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_model_keys():
    """測試模型鍵值映射"""
    print("🔍 測試模型鍵值映射...")
    
    try:
        from core.cost_calculator import CostCalculator
        from core.ai_models import AIModelManager
        from core.processing_engine import ProcessingEngine
        from config.settings import load_config
        
        # 測試成本計算器
        calculator = CostCalculator()
        print("✅ 成本計算器已載入")
        
        print("\n📋 可用的模型鍵值:")
        for model_key in calculator.pricing.keys():
            print(f"   - {model_key}")
        
        # 測試 AI 模型管理器
        config = load_config()
        ai_manager = AIModelManager(config, calculator)
        print("\n✅ AI 模型管理器已載入")
        
        available_models = ai_manager.get_available_models()
        print(f"\n📊 AI 管理器中的可用模型: {available_models}")
        
        # 測試處理引擎的模型映射
        processing_engine = ProcessingEngine(
            excel_processor=None,
            ai_manager=ai_manager,
            prompt_manager=None,
            keyword_manager=None,
            html_generator=None
        )
        
        print("\n🔧 測試模型鍵值映射:")
        test_names = [
            'Claude Sonnet 4',
            'Claude Opus 4', 
            'GPT-4o',
            'GPT-4o mini',
            'Gemini 2.5 Pro',
            'Gemini 2.5 Flash'
        ]
        
        for name in test_names:
            mapped_key = processing_engine._get_model_key(name)
            is_available = mapped_key in available_models
            status = "✅" if is_available else "❌"
            print(f"   {status} '{name}' → '{mapped_key}' (可用: {is_available})")
        
        # 檢查是否有舊的模型鍵值
        old_keys = ['anthropic-sonnet', 'anthropic-opus', 'anthropic-haiku', 'openai-gpt35', 'google-pro', 'google-flash']
        print(f"\n🔍 檢查舊模型鍵值:")
        for old_key in old_keys:
            if old_key in calculator.pricing:
                print(f"   ⚠️ 發現舊鍵值: {old_key}")
            else:
                print(f"   ✅ 已移除舊鍵值: {old_key}")
        
        print("\n🎉 模型鍵值測試完成！")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_model_mapping():
    """顯示完整的模型映射"""
    print("\n📊 完整模型映射表:")
    
    mapping = {
        # 顯示名稱 → 模型鍵值 → API 名稱
        'GPT-4o': ('openai-gpt4o', 'gpt-4o'),
        'GPT-4o mini': ('openai-gpt4o-mini', 'gpt-4o-mini'),
        'Claude Opus 4': ('anthropic-opus4', 'claude-3-opus-20240229'),
        'Claude Sonnet 4': ('anthropic-sonnet4', 'claude-3-5-sonnet-20241022'),
        'Gemini 2.5 Pro': ('google-pro25', 'gemini-1.5-pro'),
        'Gemini 2.5 Flash': ('google-flash25', 'gemini-1.5-flash')
    }
    
    print("\n| 顯示名稱 | 模型鍵值 | API 名稱 |")
    print("|----------|----------|----------|")
    for display_name, (model_key, api_name) in mapping.items():
        print(f"| {display_name} | {model_key} | {api_name} |")

def fix_common_issues():
    """修復常見問題"""
    print("\n🔧 常見問題修復建議:")
    
    print("\n1. **模型鍵值不匹配**:")
    print("   - 確保 GUI 選擇的模型名稱能正確映射到模型鍵值")
    print("   - 檢查 _get_model_key() 方法的映射表")
    
    print("\n2. **舊模型鍵值殘留**:")
    print("   - 確保所有舊的模型鍵值都已移除")
    print("   - 檢查配置文件中是否有舊的預設值")
    
    print("\n3. **API 模型名稱錯誤**:")
    print("   - 確保 API 模型名稱是正確的")
    print("   - Claude 4 系列可能還未發布，使用 Claude 3 的 API 名稱")
    print("   - Gemini 2.5 系列可能還未發布，使用 Gemini 1.5 的 API 名稱")
    
    print("\n4. **重新啟動程式**:")
    print("   - 修復後重新啟動程式")
    print("   - 清除任何快取的模型配置")

def main():
    """主測試函數"""
    print("🚀 模型鍵值修復工具")
    print("=" * 50)
    
    success = test_model_keys()
    show_model_mapping()
    fix_common_issues()
    
    if success:
        print("\n✅ 模型鍵值檢查完成！")
        print("\n🎯 修復要點:")
        print("✅ 更新了處理引擎的預設模型鍵值")
        print("✅ 修復了 GUI 的預設選擇邏輯")
        print("✅ 確認了所有新模型鍵值的可用性")
        
        print("\n🚀 下一步:")
        print("1. 重新啟動程式: python main.py")
        print("2. 檢查 AI 模型選擇是否正常")
        print("3. 測試處理功能是否工作")
    else:
        print("\n❌ 發現問題，請檢查錯誤訊息並修復。")
    
    return success

if __name__ == "__main__":
    main()
