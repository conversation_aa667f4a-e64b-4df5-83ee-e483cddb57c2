#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Processing Fix
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_processing_fix():
    """Test the processing fix"""
    print("Testing processing engine fix...")
    
    try:
        from core.processing_engine import ProcessingEngine
        print("✅ ProcessingEngine import successful")
        
        from gui.main_window import MainWindow
        print("✅ MainWindow import successful")
        
        print("✅ Separate AI model support added")
        print("✅ Processing should now work with separate Writer and Reviewer AI models")
        
        print("\n🎉 All fixes applied successfully!")
        print("🚀 You can now run: python main.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_processing_fix()
