#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify the OpenAI model fix
"""

import sys
import os
import inspect
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.ai_models import OpenAIModel

def test_openai_model_fix():
    """Test that OpenAI model uses correct model names"""
    print("🔍 Testing OpenAI Model Fix...")
    
    # Check the source code of the generate_text method
    try:
        # Get the source code of the generate_text method
        source = inspect.getsource(OpenAIModel.generate_text)
        print("📝 Checking generate_text method source code...")
        
        # Check if the deprecated model is still in the code
        if "gpt-4-vision-preview" in source:
            print("❌ FOUND deprecated model 'gpt-4-vision-preview' in source code!")
            print("🔍 Source code contains:")
            lines = source.split('\n')
            for i, line in enumerate(lines):
                if "gpt-4-vision-preview" in line:
                    print(f"   Line {i+1}: {line.strip()}")
            return False
        else:
            print("✅ No deprecated model found in source code")
            
        # Check if the fix is in place
        if "model = self.model_name" in source:
            print("✅ Fix verified: Using self.model_name for image processing")
            return True
        else:
            print("❌ Fix not found: self.model_name assignment missing")
            return False
            
    except Exception as e:
        print(f"❌ Error checking source code: {e}")
        return False

def main():
    print("🚀 OpenAI Model Fix Verification")
    print("=" * 50)
    
    success = test_openai_model_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Model fix verification PASSED!")
        print("✅ The deprecated 'gpt-4-vision-preview' model is no longer used")
        print("✅ Current model names (gpt-4o, gpt-4o-mini) are used for image processing")
        print("🔄 Please restart the application to ensure the fix takes effect")
    else:
        print("❌ Model fix verification FAILED!")
        print("⚠️ The fix may not be working correctly")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
