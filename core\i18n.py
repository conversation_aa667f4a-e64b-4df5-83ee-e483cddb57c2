#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Internationalization Support
國際化支援
"""

from typing import Dict, Any


class I18n:
    """國際化管理器"""
    
    def __init__(self, language: str = "zh_TW"):
        self.language = language
        self.translations = {
            "zh_TW": {
                # 主視窗
                "main_window_title": "AI 商品描述優化系統 v2.0",
                "file_settings": "檔案設定",
                "excel_file": "Excel 檔案:",
                "select_excel": "選擇 Excel",
                "worksheet": "工作表:",
                "image_folder": "圖片資料夾:",
                "select_folder": "選擇資料夾",
                "seo_folder": "SEO 資料夾:",
                "enable_images": "啟用圖片輔助",
                
                # 欄位設定
                "column_settings": "欄位設定",
                "excluded_columns": "排除欄位（如 ID、SKU）:",
                "image_column": "圖片欄位:",
                "seo_column": "SEO 類型欄位:",
                "product_name_column": "產品名稱欄位:",
                
                # AI 設定
                "ai_settings": "AI 設定",
                "writer_ai_model": "Writer AI 模型:",
                "writer_prompt": "Writer Prompt:",
                "reviewer_ai_model": "Reviewer AI 模型:",
                "reviewer_prompt": "Reviewer Prompt:",
                
                # 處理範圍
                "processing_range": "處理範圍",
                "start_row": "起始列:",
                "end_row": "結束列:",
                
                # 按鈕
                "load_data": "載入資料",
                "start_processing": "開始處理",
                "review_only": "Review-Only 模式",
                "export_results": "匯出結果",
                "reset_results": "重置結果",
                "preview_seo": "預覽 SEO",
                
                # 標籤頁
                "html_preview": "HTML 預覽",
                "html_source": "HTML 原始碼",
                "data_table": "資料表格",
                "image_preview": "圖片預覽",
                "cost_calculation": "成本計算",
                "prompt_management": "Prompt 管理",
                
                # 成本計算
                "ai_cost_calculation": "AI 使用成本計算",
                "ai_model": "AI 模型",
                "usage_count": "使用次數",
                "total_tokens": "總 Token",
                "input_tokens": "輸入 Token",
                "output_tokens": "輸出 Token",
                "estimated_cost": "預估費用 (USD)",
                "total_summary": "總計",
                "total_requests": "總請求次數: {0}",
                "total_tokens_used": "總 Token 使用: {0}",
                "total_estimated_cost": "總預估費用: ${0}",
                "reset_statistics": "重置統計",
                "refresh_display": "刷新顯示",
                
                # 訊息
                "no_file_selected": "未選擇檔案",
                "no_folder_selected": "未選擇資料夾",
                "ready": "就緒",
                "processing": "處理中...",
                "completed": "完成",
                "error": "錯誤",
                "warning": "警告",
                "success": "成功",
                
                # 選單
                "file_menu": "檔案",
                "settings_menu": "設定",
                "language_menu": "語言",
                "api_keys_menu": "API 金鑰",
                "help_menu": "說明",
                
                # API 金鑰對話框
                "api_keys_title": "API 金鑰設定",
                "openai_api_key": "OpenAI API 金鑰:",
                "anthropic_api_key": "Anthropic API 金鑰:",
                "google_api_key": "Google API 金鑰:",
                "save": "儲存",
                "cancel": "取消",
                
                # SEO 相關
                "seo_preview_title": "SEO 關鍵字預覽",
                "seo_file_path": "檔案路徑:",
                "total_keywords": "總關鍵字數:",
                "preview_content": "預覽內容:",
                
                # 導航
                "previous": "上一個",
                "next": "下一個",
                "current_item": "當前項目: {0}/{1}",

                # 預覽語言
                "preview_language": "預覽語言:",
                "chinese_preview": "中文預覽",
                "english_preview": "英文預覽",
                "product_name_exists": "使用現有名稱: {0}",
                "product_name_created": "創建新名稱: {0}",
                "status_info": "狀態資訊",
            },
            
            "en_US": {
                # Main Window
                "main_window_title": "AI Product Description Optimizer v2.0",
                "file_settings": "File Settings",
                "excel_file": "Excel File:",
                "select_excel": "Select Excel",
                "worksheet": "Worksheet:",
                "image_folder": "Image Folder:",
                "select_folder": "Select Folder",
                "seo_folder": "SEO Folder:",
                "enable_images": "Enable Image Assistance",
                
                # Column Settings
                "column_settings": "Column Settings",
                "excluded_columns": "Excluded Columns (e.g., ID, SKU):",
                "image_column": "Image Column:",
                "seo_column": "SEO Type Column:",
                "product_name_column": "Product Name Column:",
                
                # AI Settings
                "ai_settings": "AI Settings",
                "writer_ai_model": "Writer AI Model:",
                "writer_prompt": "Writer Prompt:",
                "reviewer_ai_model": "Reviewer AI Model:",
                "reviewer_prompt": "Reviewer Prompt:",
                
                # Processing Range
                "processing_range": "Processing Range",
                "start_row": "Start Row:",
                "end_row": "End Row:",
                
                # Buttons
                "load_data": "Load Data",
                "start_processing": "Start Processing",
                "review_only": "Review-Only Mode",
                "export_results": "Export Results",
                "reset_results": "Reset Results",
                "preview_seo": "Preview SEO",
                
                # Tabs
                "html_preview": "HTML Preview",
                "html_source": "HTML Source",
                "data_table": "Data Table",
                "image_preview": "Image Preview",
                "cost_calculation": "Cost Calculation",
                "prompt_management": "Prompt Management",
                
                # Cost Calculation
                "ai_cost_calculation": "AI Usage Cost Calculation",
                "ai_model": "AI Model",
                "usage_count": "Usage Count",
                "total_tokens": "Total Tokens",
                "input_tokens": "Input Tokens",
                "output_tokens": "Output Tokens",
                "estimated_cost": "Estimated Cost (USD)",
                "total_summary": "Total Summary",
                "total_requests": "Total Requests: {0}",
                "total_tokens_used": "Total Tokens Used: {0}",
                "total_estimated_cost": "Total Estimated Cost: ${0}",
                "reset_statistics": "Reset Statistics",
                "refresh_display": "Refresh Display",
                
                # Messages
                "no_file_selected": "No File Selected",
                "no_folder_selected": "No Folder Selected",
                "ready": "Ready",
                "processing": "Processing...",
                "completed": "Completed",
                "error": "Error",
                "warning": "Warning",
                "success": "Success",
                
                # Menu
                "file_menu": "File",
                "settings_menu": "Settings",
                "language_menu": "Language",
                "api_keys_menu": "API Keys",
                "help_menu": "Help",
                
                # API Keys Dialog
                "api_keys_title": "API Keys Configuration",
                "openai_api_key": "OpenAI API Key:",
                "anthropic_api_key": "Anthropic API Key:",
                "google_api_key": "Google API Key:",
                "save": "Save",
                "cancel": "Cancel",
                
                # SEO Related
                "seo_preview_title": "SEO Keywords Preview",
                "seo_file_path": "File Path:",
                "total_keywords": "Total Keywords:",
                "preview_content": "Preview Content:",
                
                # Navigation
                "previous": "Previous",
                "next": "Next",
                "current_item": "Current Item: {0}/{1}",

                # Preview Language
                "preview_language": "Preview Language:",
                "chinese_preview": "Chinese Preview",
                "english_preview": "English Preview",
                "product_name_exists": "Using existing name: {0}",
                "product_name_created": "Created new name: {0}",
                "status_info": "Status Information",
            }
        }
    
    def set_language(self, language: str):
        """設定語言"""
        if language in self.translations:
            self.language = language
    
    def t(self, key: str, *args) -> str:
        """翻譯文字"""
        if self.language not in self.translations:
            return key
        
        translation = self.translations[self.language].get(key, key)
        
        # 支援格式化參數
        if args:
            try:
                return translation.format(*args)
            except:
                return translation
        
        return translation
    
    def get_available_languages(self) -> Dict[str, str]:
        """取得可用語言"""
        return {
            "zh_TW": "繁體中文",
            "en_US": "English"
        }
