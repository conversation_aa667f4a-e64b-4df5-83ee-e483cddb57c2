#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test New Features
測試新增功能
"""

import sys
import os
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_cost_calculator():
    """測試成本計算器"""
    print("🔍 測試成本計算器...")
    
    try:
        from core.cost_calculator import CostCalculator
        
        calculator = CostCalculator()
        
        # 測試成本計算
        cost1 = calculator.calculate_cost('openai', 1000, 500)
        cost2 = calculator.calculate_cost('anthropic', 1000, 500)
        cost3 = calculator.calculate_cost('google', 1000, 500)
        
        print(f"   - OpenAI (1000 input, 500 output): ${cost1:.6f}")
        print(f"   - Anthropic (1000 input, 500 output): ${cost2:.6f}")
        print(f"   - Google (1000 input, 500 output): ${cost3:.6f}")
        
        # 測試使用記錄
        calculator.record_usage('openai', 1000, 500)
        calculator.record_usage('anthropic', 800, 400)
        calculator.record_usage('google', 1200, 600)
        
        # 測試統計
        summary = calculator.get_usage_summary()
        print(f"   - 總請求: {summary['total_requests']}")
        print(f"   - 總 Token: {summary['total_tokens']}")
        print(f"   - 總成本: ${summary['total_cost']:.6f}")
        
        # 測試建議
        recommendations = calculator.get_recommendations()
        print(f"   - 建議數量: {len(recommendations)}")
        
        print("✅ 成本計算器測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 成本計算器測試失敗: {e}")
        return False


def test_gui_imports():
    """測試 GUI 導入"""
    print("\n🔍 測試 GUI 新功能導入...")
    
    try:
        from gui.main_window import MainWindow
        from config.settings import load_config
        
        # 檢查新方法是否存在
        required_methods = [
            'create_cost_calculator_widget',
            'create_prompt_manager_widget',
            'update_cost_statistics',
            'reset_cost_statistics',
            'new_writer_prompt',
            'edit_writer_prompt',
            'delete_writer_prompt',
            'save_prompt',
            'on_writer_ai_changed',
            'on_reviewer_ai_changed'
        ]
        
        for method in required_methods:
            if not hasattr(MainWindow, method):
                print(f"❌ 缺少方法: {method}")
                return False
        
        print("✅ GUI 新功能導入測試通過")
        return True
        
    except Exception as e:
        print(f"❌ GUI 導入測試失敗: {e}")
        return False


def test_prompt_management():
    """測試 Prompt 管理功能"""
    print("\n🔍 測試 Prompt 管理功能...")
    
    try:
        from core.prompt_manager import PromptManager
        
        # 建立測試用的 Prompt 管理器
        prompts_dir = project_root / "prompts"
        manager = PromptManager(str(prompts_dir))
        
        # 測試載入現有 Prompt
        writer_prompts = manager.get_writer_prompts()
        reviewer_prompts = manager.get_reviewer_prompts()
        
        print(f"   - Writer Prompts: {len(writer_prompts)}")
        print(f"   - Reviewer Prompts: {len(reviewer_prompts)}")
        
        # 測試新增 Prompt
        test_writer_content = "這是一個測試 Writer Prompt\n\n產品資料: {product_data}\n關鍵字: {keywords}"
        success = manager.add_writer_prompt("test_writer", test_writer_content)
        
        if success:
            print("   - 新增測試 Writer Prompt 成功")
            
            # 測試讀取
            content = manager.get_writer_prompt("test_writer")
            if content == test_writer_content:
                print("   - 讀取測試 Writer Prompt 成功")
            else:
                print("   - 讀取測試 Writer Prompt 失敗")
                return False
        else:
            print("   - 新增測試 Writer Prompt 失敗")
            return False
        
        print("✅ Prompt 管理功能測試通過")
        return True
        
    except Exception as e:
        print(f"❌ Prompt 管理測試失敗: {e}")
        return False


def test_model_pricing():
    """測試模型定價資訊"""
    print("\n🔍 測試模型定價資訊...")
    
    try:
        from core.cost_calculator import CostCalculator
        
        calculator = CostCalculator()
        
        print("   📊 AI 模型定價 (每 1K tokens):")
        for model, pricing in calculator.pricing.items():
            display_name = pricing.get('display_name', model)
            input_cost = pricing['input'] * 1000
            output_cost = pricing['output'] * 1000
            
            print(f"   - {display_name}:")
            print(f"     輸入: ${input_cost:.4f}")
            print(f"     輸出: ${output_cost:.4f}")
            
            if pricing.get('free_tier'):
                free_limit = pricing.get('free_limit', 0)
                print(f"     免費額度: {free_limit:,} tokens/月")
        
        # 測試經濟性比較
        test_tokens = (1000, 500)  # 1000 input, 500 output
        costs = {}
        
        for model in calculator.pricing.keys():
            cost = calculator.calculate_cost(model, test_tokens[0], test_tokens[1])
            costs[model] = cost
        
        print(f"\n   💰 成本比較 ({test_tokens[0]} input + {test_tokens[1]} output tokens):")
        sorted_costs = sorted(costs.items(), key=lambda x: x[1])
        
        for i, (model, cost) in enumerate(sorted_costs):
            display_name = calculator.pricing[model]['display_name']
            if i == 0:
                print(f"   🥇 {display_name}: ${cost:.6f} (最經濟)")
            elif i == 1:
                print(f"   🥈 {display_name}: ${cost:.6f}")
            else:
                print(f"   🥉 {display_name}: ${cost:.6f}")
        
        print("✅ 模型定價測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 模型定價測試失敗: {e}")
        return False


def create_feature_summary():
    """建立功能總結"""
    print("\n📋 建立新功能總結...")
    
    summary = """
# 🎉 AI 商品描述優化系統 - 新功能總結

## ✅ 已實現的新功能

### 1. 🤖 分離式 AI 模型選擇
- **Writer AI 模型**: 專門用於生成產品描述
- **Reviewer AI 模型**: 專門用於品質評估
- **推薦模型**: GPT-4o-mini (經濟實惠)
- **最新模型**: Claude-3.5-Sonnet
- **免費模型**: Google Gemini-Pro (有免費額度)

### 2. 💰 成本計算系統
- **即時成本追蹤**: 每次 API 呼叫的成本計算
- **模型比較**: 不同 AI 模型的成本對比
- **使用統計**: 詳細的 Token 使用和費用統計
- **成本優化建議**: 智能推薦更經濟的使用方式
- **免費額度監控**: Google Gemini 免費額度追蹤

### 3. 📝 Prompt 管理系統
- **視覺化管理**: 在 GUI 中直接管理 Prompt
- **新增功能**: 建立新的 Writer/Reviewer Prompt
- **編輯功能**: 修改現有 Prompt 內容
- **刪除功能**: 移除不需要的 Prompt
- **即時預覽**: 選擇 Prompt 時即時顯示內容

### 4. 🖥️ 分割式 HTML 預覽
- **上方**: HTML 渲染預覽
- **下方**: Reviewer 評估結果
- **同步顯示**: 點擊表格同時顯示兩者
- **美觀介面**: 專業的分割視窗設計

### 5. 📊 智能處理範圍
- **自動設定**: 載入資料後自動設定為全部行數
- **彈性調整**: 仍可手動調整處理範圍
- **效率提升**: 無需手動計算總行數

### 6. 🎯 模型推薦系統
- **經濟推薦**: GPT-4o-mini (最佳性價比)
- **品質推薦**: Claude-3.5-Sonnet (最新技術)
- **免費推薦**: Google Gemini-Pro (免費額度)
- **智能標記**: 在下拉選單中顯示推薦資訊

## 💡 使用建議

### 成本優化策略
1. **日常使用**: GPT-4o-mini (經濟實惠)
2. **高品質需求**: Claude-3.5-Sonnet
3. **測試階段**: Google Gemini-Pro (免費額度)

### Prompt 管理最佳實踐
1. **分類管理**: 按產品類型建立專用 Prompt
2. **版本控制**: 保留有效的 Prompt 版本
3. **測試驗證**: 新 Prompt 先小範圍測試

### 成本控制技巧
1. **監控使用量**: 定期查看成本統計
2. **合理選擇模型**: 根據需求選擇合適模型
3. **利用免費額度**: 優先使用 Google Gemini

## 🚀 立即體驗

啟動程式後，您將看到：
- 🤖 分離的 AI 模型選擇
- 💰 成本計算標籤頁
- 📝 Prompt 管理標籤頁
- 🖥️ 改進的 HTML 預覽
- 📊 自動處理範圍設定

所有新功能都已整合到現有介面中，提供更強大、更經濟、更易用的體驗！
"""
    
    summary_file = project_root / "NEW_FEATURES_SUMMARY.md"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print(f"✅ 功能總結已建立: {summary_file}")
    return True


def main():
    """主測試函數"""
    print("🚀 AI 商品描述優化系統 - 新功能測試")
    print("=" * 60)
    
    tests = [
        ("成本計算器", test_cost_calculator),
        ("GUI 新功能導入", test_gui_imports),
        ("Prompt 管理功能", test_prompt_management),
        ("模型定價資訊", test_model_pricing),
        ("功能總結建立", create_feature_summary),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 新功能測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有新功能測試通過！")
        print("\n📋 新功能清單:")
        print("1. ✅ 分離式 AI 模型選擇 (Writer + Reviewer)")
        print("2. ✅ 成本計算和優化建議")
        print("3. ✅ 視覺化 Prompt 管理")
        print("4. ✅ 分割式 HTML 預覽")
        print("5. ✅ 智能處理範圍設定")
        print("6. ✅ 模型推薦系統")
        print("\n🚀 現在可以啟動程式體驗新功能: python main.py")
    else:
        print("⚠️ 部分新功能測試失敗，請檢查錯誤訊息。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
