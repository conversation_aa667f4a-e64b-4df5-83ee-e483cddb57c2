/**
 * AI Description Review System - Frontend JavaScript
 */

class ReviewSystem {
    constructor() {
        this.currentIndex = 0;
        this.items = [];
        this.reviews = {};
        this.apiUrl = 'http://localhost:8080/api';
        
        this.initializeElements();
        this.bindEvents();
        this.loadData();
    }

    initializeElements() {
        // Navigation elements
        this.prevBtn = document.getElementById('prevBtn');
        this.nextBtn = document.getElementById('nextBtn');
        this.saveBtn = document.getElementById('saveBtn');
        
        // Content elements
        this.productName = document.getElementById('productName');
        this.productDetails = document.getElementById('productDetails');
        this.htmlContent = document.getElementById('htmlContent');
        this.reviewComment = document.getElementById('reviewComment');
        
        // Progress elements
        this.progressText = document.getElementById('progressText');
        this.progressFill = document.getElementById('progressFill');
        this.itemCounter = document.getElementById('itemCounter');
        
        // Status buttons
        this.statusButtons = document.querySelectorAll('.status-btn');
    }

    bindEvents() {
        // Navigation events
        this.prevBtn.addEventListener('click', () => this.previousItem());
        this.nextBtn.addEventListener('click', () => this.nextItem());
        this.saveBtn.addEventListener('click', () => this.saveReview());
        
        // Status button events
        this.statusButtons.forEach(btn => {
            btn.addEventListener('click', () => this.setStatus(btn.dataset.status));
        });
        
        // Auto-save on comment change
        this.reviewComment.addEventListener('input', () => {
            this.autoSaveReview();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'ArrowLeft':
                        e.preventDefault();
                        this.previousItem();
                        break;
                    case 'ArrowRight':
                        e.preventDefault();
                        this.nextItem();
                        break;
                    case 's':
                        e.preventDefault();
                        this.saveReview();
                        break;
                }
            }
        });
    }

    async loadData() {
        try {
            this.showLoading(true);
            
            // Load Excel data from backend
            const response = await fetch(`${this.apiUrl}/load-excel`);
            const data = await response.json();
            
            if (data.success) {
                this.items = data.items;
                this.reviews = data.reviews || {};
                this.displayCurrentItem();
                this.updateProgress();
            } else {
                this.showError(data.error || 'Failed to load data');
            }
        } catch (error) {
            this.showError(`Connection error: ${error.message}`);
        } finally {
            this.showLoading(false);
        }
    }

    displayCurrentItem() {
        if (this.items.length === 0) {
            this.showError('No items to review');
            return;
        }

        const item = this.items[this.currentIndex];
        const review = this.reviews[this.currentIndex] || {};

        // Update product information
        this.productName.textContent = item.productName || 'Unnamed Product';
        this.productDetails.textContent = `Excel Row: ${item.rowIndex + 1} | Category: ${item.category || 'General'}`;

        // Display HTML content
        this.htmlContent.innerHTML = item.htmlContent || '<p>No HTML content available</p>';

        // Load existing review
        this.reviewComment.value = review.comment || '';
        this.setStatus(review.status || 'pending');

        // Update navigation buttons
        this.prevBtn.disabled = this.currentIndex === 0;
        this.nextBtn.disabled = this.currentIndex === this.items.length - 1;
    }

    setStatus(status) {
        // Remove active class from all buttons
        this.statusButtons.forEach(btn => btn.classList.remove('active'));
        
        // Add active class to selected button
        const selectedBtn = document.querySelector(`[data-status="${status}"]`);
        if (selectedBtn) {
            selectedBtn.classList.add('active');
        }
        
        // Store status
        if (!this.reviews[this.currentIndex]) {
            this.reviews[this.currentIndex] = {};
        }
        this.reviews[this.currentIndex].status = status;
        
        // Auto-save
        this.autoSaveReview();
    }

    previousItem() {
        if (this.currentIndex > 0) {
            this.saveCurrentReview();
            this.currentIndex--;
            this.displayCurrentItem();
            this.updateProgress();
        }
    }

    nextItem() {
        if (this.currentIndex < this.items.length - 1) {
            this.saveCurrentReview();
            this.currentIndex++;
            this.displayCurrentItem();
            this.updateProgress();
        }
    }

    saveCurrentReview() {
        if (!this.reviews[this.currentIndex]) {
            this.reviews[this.currentIndex] = {};
        }
        
        this.reviews[this.currentIndex].comment = this.reviewComment.value;
        this.reviews[this.currentIndex].timestamp = new Date().toISOString();
    }

    async saveReview() {
        try {
            this.saveCurrentReview();
            this.saveBtn.disabled = true;
            this.saveBtn.textContent = '💾 Saving...';
            
            const response = await fetch(`${this.apiUrl}/save-review`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    index: this.currentIndex,
                    review: this.reviews[this.currentIndex]
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('Review saved successfully!', 'success');
            } else {
                this.showNotification(result.error || 'Failed to save review', 'error');
            }
        } catch (error) {
            this.showNotification(`Save error: ${error.message}`, 'error');
        } finally {
            this.saveBtn.disabled = false;
            this.saveBtn.textContent = '💾 Save Review';
        }
    }

    async autoSaveReview() {
        // Debounced auto-save
        clearTimeout(this.autoSaveTimeout);
        this.autoSaveTimeout = setTimeout(() => {
            this.saveCurrentReview();
        }, 1000);
    }

    updateProgress() {
        const progress = ((this.currentIndex + 1) / this.items.length) * 100;
        this.progressFill.style.width = `${progress}%`;
        
        const progressText = `Item ${this.currentIndex + 1} of ${this.items.length}`;
        this.progressText.textContent = progressText;
        this.itemCounter.textContent = progressText;
    }

    showLoading(show) {
        const loading = document.querySelector('.loading');
        if (loading) {
            loading.style.display = show ? 'block' : 'none';
        }
    }

    showError(message) {
        this.htmlContent.innerHTML = `
            <div class="error">
                <h3>❌ Error</h3>
                <p>${message}</p>
                <button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    🔄 Reload Page
                </button>
            </div>
        `;
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            ${type === 'success' ? 'background: #28a745;' : 'background: #dc3545;'}
        `;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize the review system when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.reviewSystem = new ReviewSystem();
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ReviewSystem;
}
