2025-08-01 15:48:15 | INFO     | __main__:test_logging:198 | 測試日誌訊息
2025-08-01 15:54:15 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 15:54:15 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 15:54:16 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 15:54:16 | WARNING  | core.ai_models:setup_models:279 | Anthropic 模型設定失敗: 請安裝 anthropic 套件: pip install anthropic
2025-08-01 15:54:16 | WARNING  | core.ai_models:setup_models:293 | Google 模型設定失敗: 請安裝 google-generativeai 套件: pip install google-generativeai
2025-08-01 15:54:16 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 15:54:16 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 15:54:16 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 15:54:16 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 15:54:16 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 15:54:16 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 15:54:16 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 15:54:16 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 15:54:16 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 15:54:16 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 15:54:18 | INFO     | gui.main_window:log_message:540 | 選擇 AI 模型: openai
2025-08-01 15:54:18 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 15:54:18 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 15:58:22 | INFO     | gui.main_window:log_message:540 | 選擇工作表: Sheet1
2025-08-01 15:58:22 | INFO     | gui.main_window:log_message:540 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 16:00:40 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 16:00:40 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 16:00:40 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 16:00:40 | WARNING  | core.ai_models:setup_models:279 | Anthropic 模型設定失敗: 請安裝 anthropic 套件: pip install anthropic
2025-08-01 16:00:40 | WARNING  | core.ai_models:setup_models:293 | Google 模型設定失敗: 請安裝 google-generativeai 套件: pip install google-generativeai
2025-08-01 16:00:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 16:00:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 16:00:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 16:00:40 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 16:00:40 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 16:00:40 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 16:00:40 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 16:00:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 16:00:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 16:00:40 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 16:00:41 | INFO     | gui.main_window:log_message:540 | 選擇 AI 模型: openai
2025-08-01 16:00:41 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 16:00:41 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 16:00:49 | INFO     | gui.main_window:log_message:540 | 選擇工作表: Sheet1
2025-08-01 16:00:49 | INFO     | gui.main_window:log_message:540 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 16:01:00 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 16:01:00 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 16:01:00 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 16:01:00 | INFO     | gui.main_window:log_message:540 | 資料載入成功: 4 行, 10 欄
2025-08-01 16:01:43 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-01 16:01:43 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image
2025-08-01 16:01:43 | INFO     | gui.main_window:log_message:540 | 開始處理 4 個項目...
2025-08-01 16:01:43 | INFO     | core.processing_engine:process_batch:312 | 開始批次處理: 行 0 到 3 (共 4 項)
2025-08-01 16:01:43 | INFO     | core.processing_engine:process_batch:315 | 處理項目 1/4 (行 0)
2025-08-01 16:01:43 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 4)
2025-08-01 16:01:43 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['服用', '健康', '美國', '每日', '維生素C']
2025-08-01 16:02:15 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1477,0.0
2025-08-01 16:02:15 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1477, 時間: 32.16s
2025-08-01 16:02:17 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1231,0.0
2025-08-01 16:02:17 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1231, 時間: 1.87s
2025-08-01 16:02:17 | INFO     | core.processing_engine:process_batch:330 | 項目處理成功: PROD002 (34.03s)
2025-08-01 16:02:17 | INFO     | core.processing_engine:process_batch:315 | 處理項目 2/4 (行 1)
2025-08-01 16:02:17 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 12)
2025-08-01 16:02:17 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['休閒椅', '客廳', '臥室', '風格', '材質']
2025-08-01 16:02:44 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1617,0.0
2025-08-01 16:02:44 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1617, 時間: 26.83s
2025-08-01 16:02:46 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1376,0.0
2025-08-01 16:02:46 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1376, 時間: 2.32s
2025-08-01 16:02:46 | INFO     | core.processing_engine:process_batch:330 | 項目處理成功: PROD003 (29.16s)
2025-08-01 16:02:46 | INFO     | core.processing_engine:process_batch:315 | 處理項目 3/4 (行 2)
2025-08-01 16:02:46 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 7)
2025-08-01 16:02:46 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['辦公室', '辦公椅', '保固', '專業', '人體工學']
2025-08-01 16:03:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1680,0.0
2025-08-01 16:03:21 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1680, 時間: 34.95s
2025-08-01 16:03:23 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1429,0.0
2025-08-01 16:03:23 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1429, 時間: 2.25s
2025-08-01 16:03:23 | INFO     | core.processing_engine:process_batch:330 | 項目處理成功: PROD004 (37.21s)
2025-08-01 16:03:23 | INFO     | core.processing_engine:process_batch:315 | 處理項目 4/4 (行 3)
2025-08-01 16:03:23 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 6)
2025-08-01 16:03:23 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['抵抗力', '餐後', '溫和', '天然', '每日']
2025-08-01 16:03:37 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1378,0.0
2025-08-01 16:03:37 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1378, 時間: 13.86s
2025-08-01 16:03:39 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1133,0.0
2025-08-01 16:03:39 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1133, 時間: 1.57s
2025-08-01 16:03:39 | INFO     | core.processing_engine:process_batch:330 | 項目處理成功: PROD005 (15.43s)
2025-08-01 16:03:39 | INFO     | core.processing_engine:process_batch:334 | 批次處理完成: 成功 4, 失敗 0
2025-08-01 16:03:39 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-01 16:03:39 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-01 16:03:39 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-01 16:03:39 | INFO     | gui.main_window:log_message:540 | 處理完成！成功: 4, 失敗: 0, 總 Token: 11321
2025-08-01 16:04:27 | INFO     | core.data_processor:save_excel:237 | 成功儲存檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/l.xlsx
2025-08-01 16:04:27 | INFO     | gui.main_window:log_message:540 | 結果已匯出到: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/l.xlsx
2025-08-01 16:48:05 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 16:48:05 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 16:48:05 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 16:48:05 | WARNING  | core.ai_models:setup_models:279 | Anthropic 模型設定失敗: 請安裝 anthropic 套件: pip install anthropic
2025-08-01 16:48:05 | WARNING  | core.ai_models:setup_models:293 | Google 模型設定失敗: 請安裝 google-generativeai 套件: pip install google-generativeai
2025-08-01 16:48:05 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 16:48:05 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 16:48:05 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 16:48:05 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 16:48:05 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 16:48:05 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 16:48:05 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 16:48:05 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 16:48:05 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 16:48:05 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 16:48:06 | INFO     | gui.main_window:log_message:605 | 選擇 AI 模型: openai
2025-08-01 16:48:06 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 16:48:06 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 16:48:16 | INFO     | gui.main_window:log_message:605 | 選擇工作表: Sheet1
2025-08-01 16:48:16 | INFO     | gui.main_window:log_message:605 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 16:48:22 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 16:48:22 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 16:48:22 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
