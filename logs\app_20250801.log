2025-08-01 15:48:15 | INFO     | __main__:test_logging:198 | 測試日誌訊息
2025-08-01 15:54:15 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 15:54:15 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 15:54:16 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 15:54:16 | WARNING  | core.ai_models:setup_models:279 | Anthropic 模型設定失敗: 請安裝 anthropic 套件: pip install anthropic
2025-08-01 15:54:16 | WARNING  | core.ai_models:setup_models:293 | Google 模型設定失敗: 請安裝 google-generativeai 套件: pip install google-generativeai
2025-08-01 15:54:16 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 15:54:16 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 15:54:16 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 15:54:16 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 15:54:16 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 15:54:16 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 15:54:16 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 15:54:16 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 15:54:16 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 15:54:16 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 15:54:18 | INFO     | gui.main_window:log_message:540 | 選擇 AI 模型: openai
2025-08-01 15:54:18 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 15:54:18 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 15:58:22 | INFO     | gui.main_window:log_message:540 | 選擇工作表: Sheet1
2025-08-01 15:58:22 | INFO     | gui.main_window:log_message:540 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 16:00:40 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 16:00:40 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 16:00:40 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 16:00:40 | WARNING  | core.ai_models:setup_models:279 | Anthropic 模型設定失敗: 請安裝 anthropic 套件: pip install anthropic
2025-08-01 16:00:40 | WARNING  | core.ai_models:setup_models:293 | Google 模型設定失敗: 請安裝 google-generativeai 套件: pip install google-generativeai
2025-08-01 16:00:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 16:00:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 16:00:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 16:00:40 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 16:00:40 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 16:00:40 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 16:00:40 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 16:00:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 16:00:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 16:00:40 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 16:00:41 | INFO     | gui.main_window:log_message:540 | 選擇 AI 模型: openai
2025-08-01 16:00:41 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 16:00:41 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 16:00:49 | INFO     | gui.main_window:log_message:540 | 選擇工作表: Sheet1
2025-08-01 16:00:49 | INFO     | gui.main_window:log_message:540 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 16:01:00 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 16:01:00 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 16:01:00 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 16:01:00 | INFO     | gui.main_window:log_message:540 | 資料載入成功: 4 行, 10 欄
2025-08-01 16:01:43 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-01 16:01:43 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image
2025-08-01 16:01:43 | INFO     | gui.main_window:log_message:540 | 開始處理 4 個項目...
2025-08-01 16:01:43 | INFO     | core.processing_engine:process_batch:312 | 開始批次處理: 行 0 到 3 (共 4 項)
2025-08-01 16:01:43 | INFO     | core.processing_engine:process_batch:315 | 處理項目 1/4 (行 0)
2025-08-01 16:01:43 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 4)
2025-08-01 16:01:43 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['服用', '健康', '美國', '每日', '維生素C']
2025-08-01 16:02:15 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1477,0.0
2025-08-01 16:02:15 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1477, 時間: 32.16s
2025-08-01 16:02:17 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1231,0.0
2025-08-01 16:02:17 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1231, 時間: 1.87s
2025-08-01 16:02:17 | INFO     | core.processing_engine:process_batch:330 | 項目處理成功: PROD002 (34.03s)
2025-08-01 16:02:17 | INFO     | core.processing_engine:process_batch:315 | 處理項目 2/4 (行 1)
2025-08-01 16:02:17 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 12)
2025-08-01 16:02:17 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['休閒椅', '客廳', '臥室', '風格', '材質']
2025-08-01 16:02:44 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1617,0.0
2025-08-01 16:02:44 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1617, 時間: 26.83s
2025-08-01 16:02:46 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1376,0.0
2025-08-01 16:02:46 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1376, 時間: 2.32s
2025-08-01 16:02:46 | INFO     | core.processing_engine:process_batch:330 | 項目處理成功: PROD003 (29.16s)
2025-08-01 16:02:46 | INFO     | core.processing_engine:process_batch:315 | 處理項目 3/4 (行 2)
2025-08-01 16:02:46 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 7)
2025-08-01 16:02:46 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['辦公室', '辦公椅', '保固', '專業', '人體工學']
2025-08-01 16:03:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1680,0.0
2025-08-01 16:03:21 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1680, 時間: 34.95s
2025-08-01 16:03:23 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1429,0.0
2025-08-01 16:03:23 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1429, 時間: 2.25s
2025-08-01 16:03:23 | INFO     | core.processing_engine:process_batch:330 | 項目處理成功: PROD004 (37.21s)
2025-08-01 16:03:23 | INFO     | core.processing_engine:process_batch:315 | 處理項目 4/4 (行 3)
2025-08-01 16:03:23 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 6)
2025-08-01 16:03:23 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['抵抗力', '餐後', '溫和', '天然', '每日']
2025-08-01 16:03:37 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1378,0.0
2025-08-01 16:03:37 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1378, 時間: 13.86s
2025-08-01 16:03:39 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1133,0.0
2025-08-01 16:03:39 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1133, 時間: 1.57s
2025-08-01 16:03:39 | INFO     | core.processing_engine:process_batch:330 | 項目處理成功: PROD005 (15.43s)
2025-08-01 16:03:39 | INFO     | core.processing_engine:process_batch:334 | 批次處理完成: 成功 4, 失敗 0
2025-08-01 16:03:39 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-01 16:03:39 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-01 16:03:39 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-01 16:03:39 | INFO     | gui.main_window:log_message:540 | 處理完成！成功: 4, 失敗: 0, 總 Token: 11321
2025-08-01 16:04:27 | INFO     | core.data_processor:save_excel:237 | 成功儲存檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/l.xlsx
2025-08-01 16:04:27 | INFO     | gui.main_window:log_message:540 | 結果已匯出到: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/l.xlsx
2025-08-01 16:48:05 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 16:48:05 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 16:48:05 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 16:48:05 | WARNING  | core.ai_models:setup_models:279 | Anthropic 模型設定失敗: 請安裝 anthropic 套件: pip install anthropic
2025-08-01 16:48:05 | WARNING  | core.ai_models:setup_models:293 | Google 模型設定失敗: 請安裝 google-generativeai 套件: pip install google-generativeai
2025-08-01 16:48:05 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 16:48:05 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 16:48:05 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 16:48:05 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 16:48:05 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 16:48:05 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 16:48:05 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 16:48:05 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 16:48:05 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 16:48:05 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 16:48:06 | INFO     | gui.main_window:log_message:605 | 選擇 AI 模型: openai
2025-08-01 16:48:06 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 16:48:06 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 16:48:16 | INFO     | gui.main_window:log_message:605 | 選擇工作表: Sheet1
2025-08-01 16:48:16 | INFO     | gui.main_window:log_message:605 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 16:48:22 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 16:48:22 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 16:48:22 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 16:52:01 | INFO     | __main__:test_logging:198 | 測試日誌訊息
2025-08-01 16:53:23 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 16:53:23 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 16:53:23 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 16:53:23 | INFO     | core.ai_models:setup_models:277 | Anthropic 模型已設定
2025-08-01 16:53:23 | INFO     | core.ai_models:setup_models:291 | Google 模型已設定
2025-08-01 16:53:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 16:53:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 16:53:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 16:53:23 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 16:53:23 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 16:53:23 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 16:53:23 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 16:53:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 16:53:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 16:53:23 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 16:53:24 | INFO     | gui.main_window:log_message:605 | 選擇 AI 模型: openai
2025-08-01 16:53:24 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 16:53:24 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 16:54:11 | INFO     | gui.main_window:log_message:605 | 選擇工作表: Sheet1
2025-08-01 16:54:11 | INFO     | gui.main_window:log_message:605 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 16:55:07 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 16:55:07 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 16:55:07 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 16:55:07 | INFO     | gui.main_window:log_message:605 | 資料載入成功: 4 行, 10 欄
2025-08-01 16:55:23 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['ID']
2025-08-01 16:55:23 | INFO     | gui.main_window:log_message:605 | 排除欄位: ID
2025-08-01 16:55:23 | INFO     | gui.main_window:log_message:605 | 開始處理 4 個項目...
2025-08-01 16:55:23 | INFO     | core.processing_engine:process_batch:370 | 開始批次處理: 行 0 到 3 (共 4 項)
2025-08-01 16:55:23 | INFO     | core.processing_engine:process_batch:373 | 處理項目 1/4 (行 0)
2025-08-01 16:55:23 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Immunity
2025-08-01 16:55:23 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 4)
2025-08-01 16:55:23 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['健康', '每日', '服用', '美國', '維生素C']
2025-08-01 16:55:36 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1106,0.0
2025-08-01 16:55:36 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1106, 時間: 12.72s
2025-08-01 16:55:39 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1082,0.0
2025-08-01 16:55:39 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1082, 時間: 2.59s
2025-08-01 16:55:39 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Immunity (15.31s)
2025-08-01 16:55:39 | INFO     | core.processing_engine:process_batch:373 | 處理項目 2/4 (行 1)
2025-08-01 16:55:39 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Furniture
2025-08-01 16:55:39 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 12)
2025-08-01 16:55:39 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['休閒椅', '北歐風格', '材質', '風格', '經典']
2025-08-01 16:55:56 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1081,0.0
2025-08-01 16:55:56 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1081, 時間: 17.10s
2025-08-01 16:55:57 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1062,0.0
2025-08-01 16:55:57 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1062, 時間: 1.43s
2025-08-01 16:55:57 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Furniture (18.53s)
2025-08-01 16:55:57 | INFO     | core.processing_engine:process_batch:373 | 處理項目 3/4 (行 2)
2025-08-01 16:55:57 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Furniture
2025-08-01 16:55:57 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 7)
2025-08-01 16:55:57 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['辦公椅', '辦公室', '安裝', '專業', '書房']
2025-08-01 16:56:20 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1031,0.0
2025-08-01 16:56:20 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1031, 時間: 22.36s
2025-08-01 16:56:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1028,0.0
2025-08-01 16:56:21 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1028, 時間: 1.73s
2025-08-01 16:56:21 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Furniture (24.09s)
2025-08-01 16:56:21 | INFO     | core.processing_engine:process_batch:373 | 處理項目 4/4 (行 3)
2025-08-01 16:56:21 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Immunity
2025-08-01 16:56:21 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 6)
2025-08-01 16:56:21 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['抵抗力', '澳洲', '每日', '維生素C', '長效']
2025-08-01 16:56:43 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1060,0.0
2025-08-01 16:56:43 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1060, 時間: 21.47s
2025-08-01 16:56:44 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1046,0.0
2025-08-01 16:56:44 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1046, 時間: 1.52s
2025-08-01 16:56:44 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Immunity (23.00s)
2025-08-01 16:56:44 | INFO     | core.processing_engine:process_batch:392 | 批次處理完成: 成功 4, 失敗 0
2025-08-01 16:56:44 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-01 16:56:44 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-01 16:56:44 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-01 16:56:44 | INFO     | gui.main_window:log_message:605 | 處理完成！成功: 4, 失敗: 0, 總 Token: 8496
2025-08-01 16:56:44 | INFO     | gui.main_window:log_message:605 | 自動顯示 Immunity 的預覽
2025-08-01 16:57:30 | INFO     | gui.main_window:log_message:605 | 顯示第 4 行的 HTML 預覽
2025-08-01 16:57:30 | INFO     | gui.main_window:log_message:605 | 顯示第 4 行的 HTML 預覽
2025-08-01 16:57:33 | INFO     | gui.main_window:log_message:605 | 顯示第 3 行的 HTML 預覽
2025-08-01 16:57:33 | INFO     | gui.main_window:log_message:605 | 顯示第 3 行的 HTML 預覽
2025-08-01 16:57:34 | INFO     | gui.main_window:log_message:605 | 顯示第 2 行的 HTML 預覽
2025-08-01 16:57:34 | INFO     | gui.main_window:log_message:605 | 顯示第 2 行的 HTML 預覽
2025-08-01 17:10:54 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 17:10:54 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 17:10:54 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 17:10:54 | INFO     | core.ai_models:setup_models:277 | Anthropic 模型已設定
2025-08-01 17:10:54 | INFO     | core.ai_models:setup_models:291 | Google 模型已設定
2025-08-01 17:10:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 17:10:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 17:10:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 17:10:54 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 17:10:54 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 17:10:54 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 17:10:54 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 17:10:54 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 17:10:54 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 17:10:54 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 17:10:55 | INFO     | gui.main_window:log_message:643 | 選擇 AI 模型: openai
2025-08-01 17:10:55 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 17:10:56 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 17:10:59 | INFO     | gui.main_window:log_message:643 | 選擇工作表: Sheet1
2025-08-01 17:10:59 | INFO     | gui.main_window:log_message:643 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 17:11:02 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 17:11:02 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 17:11:02 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 17:11:02 | INFO     | gui.main_window:log_message:643 | 資料載入成功: 4 行, 10 欄
2025-08-01 17:11:14 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 17:11:14 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 17:11:14 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 17:11:14 | INFO     | gui.main_window:log_message:643 | 資料載入成功: 4 行, 10 欄
2025-08-01 17:14:55 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['ID']
2025-08-01 17:14:55 | INFO     | gui.main_window:log_message:643 | 排除欄位: ID
2025-08-01 17:14:55 | INFO     | gui.main_window:log_message:643 | 開始處理 4 個項目...
2025-08-01 17:14:55 | INFO     | core.processing_engine:process_batch:370 | 開始批次處理: 行 0 到 3 (共 4 項)
2025-08-01 17:14:55 | INFO     | core.processing_engine:process_batch:373 | 處理項目 1/4 (行 0)
2025-08-01 17:14:55 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Immunity
2025-08-01 17:14:55 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 4)
2025-08-01 17:14:55 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['服用', '美國', '每日', '健康', '維生素C']
2025-08-01 17:15:18 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1100,0.0
2025-08-01 17:15:18 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1100, 時間: 22.94s
2025-08-01 17:15:20 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1077,0.0
2025-08-01 17:15:20 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1077, 時間: 1.31s
2025-08-01 17:15:20 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Immunity (24.25s)
2025-08-01 17:15:20 | INFO     | core.processing_engine:process_batch:373 | 處理項目 2/4 (行 1)
2025-08-01 17:15:20 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Furniture
2025-08-01 17:15:20 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 12)
2025-08-01 17:15:20 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['休閒椅', '舒適', '材質', '客廳', '人體工學']
2025-08-01 17:15:39 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1085,0.0
2025-08-01 17:15:39 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1085, 時間: 19.07s
2025-08-01 17:15:40 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1070,0.0
2025-08-01 17:15:40 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1070, 時間: 1.51s
2025-08-01 17:15:40 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Furniture (20.59s)
2025-08-01 17:15:40 | INFO     | core.processing_engine:process_batch:373 | 處理項目 3/4 (行 2)
2025-08-01 17:15:40 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Furniture
2025-08-01 17:15:40 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 7)
2025-08-01 17:15:40 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['辦公椅', '辦公室', '舒適', '人體工學', '書房']
2025-08-01 17:16:02 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1064,0.0
2025-08-01 17:16:02 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1064, 時間: 21.94s
2025-08-01 17:16:04 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1046,0.0
2025-08-01 17:16:04 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1046, 時間: 1.52s
2025-08-01 17:16:04 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Furniture (23.47s)
2025-08-01 17:16:04 | INFO     | core.processing_engine:process_batch:373 | 處理項目 4/4 (行 3)
2025-08-01 17:16:04 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Immunity
2025-08-01 17:16:04 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 6)
2025-08-01 17:16:04 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['抵抗力', '溫和', '長效', '餐後', '維生素C']
2025-08-01 17:16:16 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1017,0.0
2025-08-01 17:16:16 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1017, 時間: 12.39s
2025-08-01 17:16:18 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,998,0.0
2025-08-01 17:16:18 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 998, 時間: 1.59s
2025-08-01 17:16:18 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Immunity (13.98s)
2025-08-01 17:16:18 | INFO     | core.processing_engine:process_batch:392 | 批次處理完成: 成功 4, 失敗 0
2025-08-01 17:16:18 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-01 17:16:18 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-01 17:16:18 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-01 17:16:18 | INFO     | gui.main_window:log_message:643 | 處理完成！成功: 4, 失敗: 0, 總 Token: 8457
2025-08-01 17:16:18 | INFO     | gui.main_window:log_message:643 | 自動顯示 Immunity 的預覽
2025-08-01 17:16:30 | INFO     | gui.main_window:log_message:643 | 顯示第 3 行的 HTML 預覽
2025-08-01 17:16:30 | INFO     | gui.main_window:log_message:643 | 顯示第 3 行的 HTML 預覽
2025-08-01 17:16:30 | INFO     | gui.main_window:log_message:643 | 顯示第 3 行的 HTML 預覽
2025-08-01 18:02:03 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 18:02:03 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 18:02:04 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 18:02:04 | INFO     | core.ai_models:setup_models:277 | Anthropic 模型已設定
2025-08-01 18:02:04 | INFO     | core.ai_models:setup_models:291 | Google 模型已設定
2025-08-01 18:02:04 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 18:02:04 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 18:02:04 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 18:02:04 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: test_writer
2025-08-01 18:02:04 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 4 個 Writer Prompt
2025-08-01 18:02:04 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 18:02:04 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 18:02:04 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 18:02:04 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 18:02:04 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 18:02:04 | INFO     | gui.main_window:init_components:143 | 核心組件初始化完成
2025-08-01 18:02:06 | ERROR    | __main__:main:56 | 程式啟動失敗: name 'QLineEdit' is not defined
2025-08-01 18:05:00 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 18:05:00 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 18:05:00 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 18:05:00 | INFO     | core.ai_models:setup_models:277 | Anthropic 模型已設定
2025-08-01 18:05:00 | INFO     | core.ai_models:setup_models:291 | Google 模型已設定
2025-08-01 18:05:00 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 18:05:00 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 18:05:00 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 18:05:00 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: test_writer
2025-08-01 18:05:00 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 4 個 Writer Prompt
2025-08-01 18:05:00 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 18:05:00 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 18:05:00 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 18:05:00 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 18:05:00 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 18:05:00 | INFO     | gui.main_window:init_components:143 | 核心組件初始化完成
2025-08-01 18:05:01 | ERROR    | __main__:main:56 | 程式啟動失敗: name 'QLineEdit' is not defined
2025-08-01 18:07:48 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 18:07:48 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 18:07:48 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 18:07:49 | INFO     | core.ai_models:setup_models:277 | Anthropic 模型已設定
2025-08-01 18:07:49 | INFO     | core.ai_models:setup_models:291 | Google 模型已設定
2025-08-01 18:07:49 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 18:07:49 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 18:07:49 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 18:07:49 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: test_writer
2025-08-01 18:07:49 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 4 個 Writer Prompt
2025-08-01 18:07:49 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 18:07:49 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 18:07:49 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 18:07:49 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 18:07:49 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 18:07:49 | INFO     | gui.main_window:init_components:143 | 核心組件初始化完成
2025-08-01 18:07:49 | INFO     | gui.main_window:log_message:849 | 選擇 Writer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 18:07:49 | INFO     | gui.main_window:log_message:849 | 選擇 Reviewer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 18:07:49 | INFO     | gui.main_window:__init__:130 | 主視窗初始化完成
2025-08-01 18:07:50 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 18:42:57 | INFO     | gui.main_window:log_message:849 | 選擇工作表: Sheet1
2025-08-01 18:42:57 | INFO     | gui.main_window:log_message:849 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 18:43:00 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 18:43:00 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 18:43:00 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 18:43:00 | INFO     | gui.main_window:log_message:849 | 自動設定處理範圍: 1 到 4 行
2025-08-01 18:43:00 | INFO     | gui.main_window:log_message:849 | 資料載入成功: 4 行, 10 欄
2025-08-01 18:43:16 | INFO     | gui.main_window:log_message:849 | 選擇 Reviewer AI 模型: Anthropic Claude-3.5-Sonnet (最新)
2025-08-01 18:43:40 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['ID']
2025-08-01 18:43:40 | INFO     | gui.main_window:log_message:849 | 排除欄位: ID
2025-08-01 18:45:59 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 18:45:59 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 18:45:59 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 18:45:59 | INFO     | core.ai_models:setup_models:277 | Anthropic 模型已設定
2025-08-01 18:45:59 | INFO     | core.ai_models:setup_models:291 | Google 模型已設定
2025-08-01 18:45:59 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 18:45:59 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 18:45:59 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 18:45:59 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: test_writer
2025-08-01 18:45:59 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 4 個 Writer Prompt
2025-08-01 18:45:59 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 18:45:59 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 18:45:59 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 18:45:59 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 18:45:59 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 18:45:59 | INFO     | gui.main_window:init_components:143 | 核心組件初始化完成
2025-08-01 18:46:00 | INFO     | gui.main_window:log_message:849 | 選擇 Writer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 18:46:00 | INFO     | gui.main_window:log_message:849 | 選擇 Reviewer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 18:46:00 | INFO     | gui.main_window:__init__:130 | 主視窗初始化完成
2025-08-01 18:46:00 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 18:46:07 | INFO     | gui.main_window:log_message:849 | 選擇工作表: Sheet1
2025-08-01 18:46:07 | INFO     | gui.main_window:log_message:849 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 18:46:07 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 18:46:07 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 18:46:07 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 18:46:07 | INFO     | gui.main_window:log_message:849 | 自動設定處理範圍: 1 到 4 行
2025-08-01 18:46:07 | INFO     | gui.main_window:log_message:849 | 資料載入成功: 4 行, 10 欄
2025-08-01 18:46:20 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['ID']
2025-08-01 18:46:20 | INFO     | gui.main_window:log_message:849 | 排除欄位: ID
2025-08-01 18:56:59 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 18:56:59 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 18:56:59 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 18:56:59 | INFO     | core.ai_models:setup_models:277 | Anthropic 模型已設定
2025-08-01 18:56:59 | INFO     | core.ai_models:setup_models:291 | Google 模型已設定
2025-08-01 18:56:59 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 18:56:59 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 18:56:59 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 18:56:59 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: test_writer
2025-08-01 18:56:59 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 4 個 Writer Prompt
2025-08-01 18:56:59 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 18:56:59 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 18:56:59 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 18:56:59 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 18:56:59 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 18:56:59 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-01 18:57:00 | INFO     | gui.main_window:log_message:852 | 選擇 Writer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 18:57:00 | INFO     | gui.main_window:log_message:852 | 選擇 Reviewer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 18:57:00 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-01 18:57:00 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 18:57:06 | INFO     | gui.main_window:log_message:852 | 選擇工作表: Sheet1
2025-08-01 18:57:06 | INFO     | gui.main_window:log_message:852 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 18:57:09 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 18:57:09 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 18:57:09 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 18:57:09 | INFO     | gui.main_window:log_message:852 | 自動設定處理範圍: 1 到 4 行
2025-08-01 18:57:09 | INFO     | gui.main_window:log_message:852 | 資料載入成功: 4 行, 10 欄
2025-08-01 18:57:33 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['ID']
2025-08-01 18:57:33 | INFO     | gui.main_window:log_message:852 | 排除欄位: ID
2025-08-01 18:57:33 | INFO     | gui.main_window:log_message:852 | 開始處理 4 個項目...
2025-08-01 18:57:33 | INFO     | core.processing_engine:process_batch:374 | 開始批次處理: 行 0 到 3 (共 4 項)
2025-08-01 18:57:33 | INFO     | core.processing_engine:process_batch:375 | Writer AI: openai, Reviewer AI: openai
2025-08-01 18:57:33 | INFO     | core.processing_engine:process_batch:378 | 處理項目 1/4 (行 0)
2025-08-01 18:57:33 | INFO     | core.processing_engine:_generate_product_name:253 | AI 生成產品名稱: Immunity
2025-08-01 18:57:33 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 4)
2025-08-01 18:57:33 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['健康', '每日', '美國', '服用', '維生素C']
2025-08-01 18:57:54 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1252,0.0
2025-08-01 18:57:54 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1252, 時間: 21.48s
2025-08-01 18:57:56 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1192,0.0
2025-08-01 18:57:56 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1192, 時間: 1.57s
2025-08-01 18:57:56 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: Immunity (23.06s)
2025-08-01 18:57:56 | INFO     | core.processing_engine:process_batch:378 | 處理項目 2/4 (行 1)
2025-08-01 18:57:56 | INFO     | core.processing_engine:_generate_product_name:253 | AI 生成產品名稱: Furniture
2025-08-01 18:57:56 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 12)
2025-08-01 18:57:56 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['休閒椅', '耐用', '環保', '承重', '經典']
2025-08-01 18:58:15 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1247,0.0
2025-08-01 18:58:15 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1247, 時間: 19.04s
2025-08-01 18:58:16 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1172,0.0
2025-08-01 18:58:16 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1172, 時間: 1.38s
2025-08-01 18:58:16 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: Furniture (20.43s)
2025-08-01 18:58:16 | INFO     | core.processing_engine:process_batch:378 | 處理項目 3/4 (行 2)
2025-08-01 18:58:16 | INFO     | core.processing_engine:_generate_product_name:253 | AI 生成產品名稱: Furniture
2025-08-01 18:58:16 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 7)
2025-08-01 18:58:16 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['辦公室', '辦公椅', '專業', '保固', '書房']
2025-08-01 18:58:36 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1333,0.0
2025-08-01 18:58:36 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1333, 時間: 19.99s
2025-08-01 18:58:38 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1261,0.0
2025-08-01 18:58:38 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1261, 時間: 1.75s
2025-08-01 18:58:38 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: Furniture (21.74s)
2025-08-01 18:58:38 | INFO     | core.processing_engine:process_batch:378 | 處理項目 4/4 (行 3)
2025-08-01 18:58:38 | INFO     | core.processing_engine:_generate_product_name:253 | AI 生成產品名稱: Immunity
2025-08-01 18:58:38 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 6)
2025-08-01 18:58:38 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['抵抗力', '溫和', '天然', '餐後', '澳洲']
2025-08-01 18:59:11 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1402,0.0
2025-08-01 18:59:11 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1402, 時間: 33.05s
2025-08-01 18:59:14 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1337,0.0
2025-08-01 18:59:14 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1337, 時間: 2.53s
2025-08-01 18:59:14 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: Immunity (35.58s)
2025-08-01 18:59:14 | INFO     | core.processing_engine:process_batch:397 | 批次處理完成: 成功 4, 失敗 0
2025-08-01 18:59:14 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-01 18:59:14 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-01 18:59:14 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-01 18:59:14 | INFO     | gui.main_window:log_message:852 | 處理完成！成功: 4, 失敗: 0, 總 Token: 10196
2025-08-01 18:59:14 | INFO     | gui.main_window:log_message:852 | 自動顯示 Immunity 的預覽
2025-08-01 18:59:29 | INFO     | gui.main_window:log_message:852 | 顯示第 2 行的 HTML 預覽和 Reviewer 結果
2025-08-01 18:59:29 | INFO     | gui.main_window:log_message:852 | 顯示第 2 行的 HTML 預覽和 Reviewer 結果
2025-08-01 19:13:36 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 19:13:36 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 19:13:36 | INFO     | core.ai_models:setup_models:305 | OpenAI 模型已設定
2025-08-01 19:13:36 | INFO     | core.ai_models:setup_models:321 | Anthropic 模型已設定
2025-08-01 19:13:36 | INFO     | core.ai_models:setup_models:337 | Google 模型已設定
2025-08-01 19:13:36 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 19:13:36 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 19:13:36 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 19:13:36 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: test_writer
2025-08-01 19:13:36 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 4 個 Writer Prompt
2025-08-01 19:13:36 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 19:13:36 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 19:13:36 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 19:13:36 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 19:13:36 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 19:13:36 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-01 19:13:37 | INFO     | gui.main_window:log_message:865 | 選擇 Writer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 19:13:37 | INFO     | gui.main_window:log_message:865 | 選擇 Reviewer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 19:13:37 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-01 19:13:37 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 19:18:53 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 19:18:53 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 19:18:54 | INFO     | core.ai_models:setup_models:305 | OpenAI 模型已設定
2025-08-01 19:18:54 | INFO     | core.ai_models:setup_models:321 | Anthropic 模型已設定
2025-08-01 19:18:54 | INFO     | core.ai_models:setup_models:337 | Google 模型已設定
2025-08-01 19:18:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 19:18:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 19:18:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 19:18:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: test_writer
2025-08-01 19:18:54 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 4 個 Writer Prompt
2025-08-01 19:18:54 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 19:18:54 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 19:18:54 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 19:18:54 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 19:18:54 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 19:18:54 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-01 19:18:55 | INFO     | gui.main_window:log_message:865 | 選擇 Writer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 19:18:55 | INFO     | gui.main_window:log_message:865 | 選擇 Reviewer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 19:18:55 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-01 19:18:55 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 19:57:23 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 19:57:23 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 19:57:23 | INFO     | core.ai_models:setup_models:305 | OpenAI 模型已設定
2025-08-01 19:57:23 | INFO     | core.ai_models:setup_models:321 | Anthropic 模型已設定
2025-08-01 19:57:23 | INFO     | core.ai_models:setup_models:337 | Google 模型已設定
2025-08-01 19:57:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 19:57:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 19:57:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 19:57:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: test_writer
2025-08-01 19:57:23 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 4 個 Writer Prompt
2025-08-01 19:57:23 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 19:57:23 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 19:57:23 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 19:57:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 19:57:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 19:57:23 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-01 19:57:24 | INFO     | gui.main_window:log_message:865 | 選擇 Writer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 19:57:24 | INFO     | gui.main_window:log_message:865 | 選擇 Reviewer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 19:57:24 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-01 19:57:24 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 21:37:18 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 21:37:18 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 21:37:18 | INFO     | core.ai_models:setup_models:305 | OpenAI 模型已設定
2025-08-01 21:37:19 | INFO     | core.ai_models:setup_models:321 | Anthropic 模型已設定
2025-08-01 21:37:19 | INFO     | core.ai_models:setup_models:337 | Google 模型已設定
2025-08-01 21:37:19 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 21:37:19 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 21:37:19 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 21:37:19 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: test_writer
2025-08-01 21:37:19 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 4 個 Writer Prompt
2025-08-01 21:37:19 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 21:37:19 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 21:37:19 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 21:37:19 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 21:37:19 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 21:37:19 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-01 21:37:20 | INFO     | gui.main_window:log_message:865 | 選擇 Writer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 21:37:20 | INFO     | gui.main_window:log_message:865 | 選擇 Reviewer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
2025-08-01 21:37:20 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-01 21:37:20 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 21:37:30 | INFO     | gui.main_window:log_message:865 | 選擇工作表: Sheet1
2025-08-01 21:37:30 | INFO     | gui.main_window:log_message:865 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-01 21:37:38 | INFO     | gui.main_window:log_message:865 | 圖片資料夾警告: 圖片目錄中沒有找到支援的圖片檔案
2025-08-01 21:37:42 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-01 21:37:42 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-01 21:37:42 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-01 21:37:42 | INFO     | gui.main_window:log_message:865 | 自動設定處理範圍: 1 到 14 行
2025-08-01 21:37:42 | INFO     | gui.main_window:log_message:865 | 資料載入成功: 14 行, 7 欄
2025-08-01 21:37:42 | INFO     | gui.main_window:log_message:865 | 警告: 部分欄位有缺失值
2025-08-01 21:37:57 | INFO     | gui.main_window:log_message:865 | 選擇 Reviewer AI 模型: Google Gemini-Pro (免費額度)
2025-08-01 21:38:00 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-01 21:38:00 | INFO     | gui.main_window:log_message:865 | 排除欄位: 序号, 数量
2025-08-01 21:38:00 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-01 21:38:00 | INFO     | gui.main_window:log_message:865 | 開始處理 14 個項目...
2025-08-01 21:38:00 | INFO     | core.processing_engine:process_batch:374 | 開始批次處理: 行 0 到 13 (共 14 項)
2025-08-01 21:38:00 | INFO     | core.processing_engine:process_batch:375 | Writer AI: openai, Reviewer AI: google
2025-08-01 21:38:00 | INFO     | core.processing_engine:process_batch:378 | 處理項目 1/14 (行 0)
2025-08-01 21:38:00 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['認證', '天然', '均衡', '輕鬆', '快速']
2025-08-01 21:38:21 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 581 output = 1100 tokens, 成本: $0.000426
2025-08-01 21:38:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1100,0.0
2025-08-01 21:38:21 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1100, 時間: 20.79s
2025-08-01 21:38:21 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:38:21 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:38:21 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:38:21 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: A309... (21.25s)
2025-08-01 21:38:21 | INFO     | core.processing_engine:process_batch:378 | 處理項目 2/14 (行 1)
2025-08-01 21:38:21 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['認證', '天然', '均衡', '輕鬆', '快速']
2025-08-01 21:38:21 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_BD19B18174F845EA81C94203F1012FC2' 找到 0 張圖片
2025-08-01 21:38:40 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 551 output = 1070 tokens, 成本: $0.000408
2025-08-01 21:38:40 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1070,0.0
2025-08-01 21:38:40 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1070, 時間: 18.77s
2025-08-01 21:38:40 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:38:40 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:38:40 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:38:40 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: A2423... (19.02s)
2025-08-01 21:38:40 | INFO     | core.processing_engine:process_batch:378 | 處理項目 3/14 (行 2)
2025-08-01 21:38:40 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['認證', '天然', '均衡', '輕鬆', '快速']
2025-08-01 21:38:40 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_BB234940DB994A06BF882CFB01761806' 找到 0 張圖片
2025-08-01 21:39:04 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 570 output = 1089 tokens, 成本: $0.000420
2025-08-01 21:39:04 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1089,0.0
2025-08-01 21:39:04 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1089, 時間: 23.85s
2025-08-01 21:39:04 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:04 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:04 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:04 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: A2561-18... (24.10s)
2025-08-01 21:39:04 | INFO     | core.processing_engine:process_batch:378 | 處理項目 4/14 (行 3)
2025-08-01 21:39:04 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '認證', '天然', '均衡', '輕鬆']
2025-08-01 21:39:04 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_7790BC4249FE45D5AA00F5DF1E857F02' 找到 0 張圖片
2025-08-01 21:39:23 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 548 output = 1067 tokens, 成本: $0.000407
2025-08-01 21:39:23 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1067,0.0
2025-08-01 21:39:23 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1067, 時間: 18.57s
2025-08-01 21:39:23 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:23 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:23 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:23 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: A2368... (18.83s)
2025-08-01 21:39:23 | INFO     | core.processing_engine:process_batch:378 | 處理項目 5/14 (行 4)
2025-08-01 21:39:23 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['認證', '天然', '均衡', '輕鬆', '快速']
2025-08-01 21:39:23 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_4901209267F84A65B5B3C44C9FF10428' 找到 0 張圖片
2025-08-01 21:39:46 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 560 output = 1079 tokens, 成本: $0.000414
2025-08-01 21:39:46 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1079,0.0
2025-08-01 21:39:46 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1079, 時間: 23.06s
2025-08-01 21:39:47 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:47 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:47 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:39:47 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: A2556... (23.32s)
2025-08-01 21:39:47 | INFO     | core.processing_engine:process_batch:378 | 處理項目 6/14 (行 5)
2025-08-01 21:39:47 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '認證', '天然', '均衡', '輕鬆']
2025-08-01 21:39:47 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y168chair' 找到 0 張圖片
2025-08-01 21:40:02 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 501 output = 1020 tokens, 成本: $0.000378
2025-08-01 21:40:02 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1020,0.0
2025-08-01 21:40:02 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1020, 時間: 15.61s
2025-08-01 21:40:02 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:02 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:02 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:02 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: Y168... (15.86s)
2025-08-01 21:40:02 | INFO     | core.processing_engine:process_batch:378 | 處理項目 7/14 (行 6)
2025-08-01 21:40:02 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['認證', '天然', '均衡', '輕鬆', '快速']
2025-08-01 21:40:02 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y169chair' 找到 0 張圖片
2025-08-01 21:40:21 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 604 output = 1123 tokens, 成本: $0.000440
2025-08-01 21:40:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1123,0.0
2025-08-01 21:40:21 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1123, 時間: 18.71s
2025-08-01 21:40:21 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:21 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:21 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:21 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: Y169... (18.94s)
2025-08-01 21:40:21 | INFO     | core.processing_engine:process_batch:378 | 處理項目 8/14 (行 7)
2025-08-01 21:40:21 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['認證', '天然', '均衡', '輕鬆', '快速']
2025-08-01 21:40:21 | DEBUG    | core.data_processor:find_images:327 | 檔名 '24-48chair' 找到 0 張圖片
2025-08-01 21:40:39 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 557 output = 1076 tokens, 成本: $0.000412
2025-08-01 21:40:39 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1076,0.0
2025-08-01 21:40:39 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1076, 時間: 17.67s
2025-08-01 21:40:39 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:39 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:39 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:40:39 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: 24-48... (18.00s)
2025-08-01 21:40:39 | INFO     | core.processing_engine:process_batch:378 | 處理項目 9/14 (行 8)
2025-08-01 21:40:39 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '認證', '天然', '均衡', '輕鬆']
2025-08-01 21:40:39 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y172chair' 找到 0 張圖片
2025-08-01 21:41:00 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 598 output = 1117 tokens, 成本: $0.000437
2025-08-01 21:41:00 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1117,0.0
2025-08-01 21:41:00 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1117, 時間: 20.53s
2025-08-01 21:41:00 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:00 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:00 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:00 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: B2551... (20.77s)
2025-08-01 21:41:00 | INFO     | core.processing_engine:process_batch:378 | 處理項目 10/14 (行 9)
2025-08-01 21:41:00 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '認證', '天然', '均衡', '輕鬆']
2025-08-01 21:41:00 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y173chair' 找到 0 張圖片
2025-08-01 21:41:22 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 567 output = 1086 tokens, 成本: $0.000418
2025-08-01 21:41:22 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1086,0.0
2025-08-01 21:41:22 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1086, 時間: 21.76s
2025-08-01 21:41:22 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:22 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:22 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:22 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: B2550... (22.00s)
2025-08-01 21:41:22 | INFO     | core.processing_engine:process_batch:378 | 處理項目 11/14 (行 10)
2025-08-01 21:41:22 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['認證', '天然', '均衡', '輕鬆', '快速']
2025-08-01 21:41:22 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y174chair' 找到 0 張圖片
2025-08-01 21:41:41 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 510 output = 1029 tokens, 成本: $0.000384
2025-08-01 21:41:41 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1029,0.0
2025-08-01 21:41:41 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1029, 時間: 19.35s
2025-08-01 21:41:42 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:42 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:42 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:41:42 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: 24-26#... (19.64s)
2025-08-01 21:41:42 | INFO     | core.processing_engine:process_batch:378 | 處理項目 12/14 (行 11)
2025-08-01 21:41:42 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['認證', '天然', '均衡', '輕鬆', '快速']
2025-08-01 21:41:42 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y200chair' 找到 0 張圖片
2025-08-01 21:42:07 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 690 output = 1209 tokens, 成本: $0.000492
2025-08-01 21:42:07 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1209,0.0
2025-08-01 21:42:07 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1209, 時間: 25.31s
2025-08-01 21:42:07 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:07 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:07 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:07 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: Y200... (25.56s)
2025-08-01 21:42:07 | INFO     | core.processing_engine:process_batch:378 | 處理項目 13/14 (行 12)
2025-08-01 21:42:07 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '認證', '天然', '均衡', '輕鬆']
2025-08-01 21:42:07 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y176chair' 找到 0 張圖片
2025-08-01 21:42:23 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 464 output = 983 tokens, 成本: $0.000356
2025-08-01 21:42:23 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,983,0.0
2025-08-01 21:42:23 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 983, 時間: 15.89s
2025-08-01 21:42:23 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:23 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:23 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:23 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: Y-171... (16.12s)
2025-08-01 21:42:23 | INFO     | core.processing_engine:process_batch:378 | 處理項目 14/14 (行 13)
2025-08-01 21:42:23 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['認證', '天然', '均衡', '輕鬆', '快速']
2025-08-01 21:42:23 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y176chair' 找到 0 張圖片
2025-08-01 21:42:43 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 519 input + 517 output = 1036 tokens, 成本: $0.000388
2025-08-01 21:42:43 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1036,0.0
2025-08-01 21:42:43 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1036, 時間: 19.89s
2025-08-01 21:42:44 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:44 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: google, 錯誤: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:44 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-08-01 21:42:44 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: B6913... (20.16s)
2025-08-01 21:42:44 | INFO     | core.processing_engine:process_batch:397 | 批次處理完成: 成功 14, 失敗 0
2025-08-01 21:42:44 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-01 21:42:44 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-01 21:42:44 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-01 21:42:44 | INFO     | gui.main_window:log_message:865 | 處理完成！成功: 14, 失敗: 0, 總 Token: 15,084, 預估費用: $0.005781
2025-08-01 21:42:44 | INFO     | gui.main_window:log_message:865 | 自動顯示 A309... 的預覽
2025-08-01 21:44:38 | INFO     | gui.main_window:log_message:865 | 顯示第 4 行的 HTML 預覽和 Reviewer 結果
2025-08-01 21:44:38 | INFO     | gui.main_window:log_message:865 | 顯示第 4 行的 HTML 預覽和 Reviewer 結果
2025-08-01 22:10:05 | INFO     | gui.main_window:log_message:865 | 顯示第 11 行的 HTML 預覽和 Reviewer 結果
2025-08-01 22:10:05 | INFO     | gui.main_window:log_message:865 | 顯示第 11 行的 HTML 預覽和 Reviewer 結果
2025-08-01 22:15:44 | INFO     | core.prompt_manager:add_writer_prompt:201 | 新增 Writer Prompt: furniture
2025-08-01 22:15:44 | INFO     | gui.main_window:log_message:865 | 已儲存 Writer Prompt: furniture
2025-08-01 22:15:51 | INFO     | gui.main_window:log_message:865 | 選擇 Reviewer AI 模型: Anthropic Claude-3.5-Sonnet (最新)
2025-08-01 22:16:19 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-01 22:16:19 | INFO     | gui.main_window:log_message:865 | 排除欄位: 序号, 数量
2025-08-01 22:16:19 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-01 22:16:19 | INFO     | gui.main_window:log_message:865 | 開始處理 5 個項目...
2025-08-01 22:16:19 | INFO     | core.processing_engine:process_batch:374 | 開始批次處理: 行 0 到 4 (共 5 項)
2025-08-01 22:16:19 | INFO     | core.processing_engine:process_batch:375 | Writer AI: openai, Reviewer AI: anthropic
2025-08-01 22:16:19 | INFO     | core.processing_engine:process_batch:378 | 處理項目 1/5 (行 0)
2025-08-01 22:16:19 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 1)
2025-08-01 22:16:19 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['均衡', '認證', '天然', '輕鬆', '快速']
2025-08-01 22:16:43 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1148 input + 678 output = 1826 tokens, 成本: $0.000579
2025-08-01 22:16:43 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1826,0.0
2025-08-01 22:16:43 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1826, 時間: 24.59s
2025-08-01 22:16:44 | ERROR    | core.ai_models:generate_text:207 | Anthropic API 呼叫失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:16:44 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: anthropic, 錯誤: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:16:44 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:16:44 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: A309... (24.97s)
2025-08-01 22:16:44 | INFO     | core.processing_engine:process_batch:378 | 處理項目 2/5 (行 1)
2025-08-01 22:16:44 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 1)
2025-08-01 22:16:44 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['均衡', '認證', '天然', '輕鬆', '快速']
2025-08-01 22:16:44 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_BD19B18174F845EA81C94203F1012FC2' 找到 0 張圖片
2025-08-01 22:17:06 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1138 input + 694 output = 1832 tokens, 成本: $0.000587
2025-08-01 22:17:06 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1832,0.0
2025-08-01 22:17:06 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1832, 時間: 21.97s
2025-08-01 22:17:06 | ERROR    | core.ai_models:generate_text:207 | Anthropic API 呼叫失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:17:06 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: anthropic, 錯誤: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:17:06 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:17:06 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: A2423... (22.36s)
2025-08-01 22:17:06 | INFO     | core.processing_engine:process_batch:378 | 處理項目 3/5 (行 2)
2025-08-01 22:17:06 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 1)
2025-08-01 22:17:06 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['均衡', '認證', '天然', '輕鬆', '快速']
2025-08-01 22:17:06 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_BB234940DB994A06BF882CFB01761806' 找到 0 張圖片
2025-08-01 22:17:48 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1151 input + 905 output = 2056 tokens, 成本: $0.000716
2025-08-01 22:17:48 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,2056,0.0
2025-08-01 22:17:48 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 2056, 時間: 41.59s
2025-08-01 22:17:48 | ERROR    | core.ai_models:generate_text:207 | Anthropic API 呼叫失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:17:48 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: anthropic, 錯誤: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:17:48 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:17:48 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: A2561-18... (41.91s)
2025-08-01 22:17:48 | INFO     | core.processing_engine:process_batch:378 | 處理項目 4/5 (行 3)
2025-08-01 22:17:48 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 1)
2025-08-01 22:17:48 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '均衡', '認證', '天然', '輕鬆']
2025-08-01 22:17:48 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_7790BC4249FE45D5AA00F5DF1E857F02' 找到 0 張圖片
2025-08-01 22:18:15 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1146 input + 697 output = 1843 tokens, 成本: $0.000590
2025-08-01 22:18:15 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1843,0.0
2025-08-01 22:18:15 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1843, 時間: 26.82s
2025-08-01 22:18:15 | ERROR    | core.ai_models:generate_text:207 | Anthropic API 呼叫失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:18:15 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: anthropic, 錯誤: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:18:15 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:18:15 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: A2368... (27.17s)
2025-08-01 22:18:15 | INFO     | core.processing_engine:process_batch:378 | 處理項目 5/5 (行 4)
2025-08-01 22:18:15 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 1)
2025-08-01 22:18:15 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['均衡', '認證', '天然', '輕鬆', '快速']
2025-08-01 22:18:15 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_4901209267F84A65B5B3C44C9FF10428' 找到 0 張圖片
2025-08-01 22:18:30 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1152 input + 709 output = 1861 tokens, 成本: $0.000598
2025-08-01 22:18:30 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1861,0.0
2025-08-01 22:18:30 | INFO     | core.ai_models:generate_text:381 | AI 生成完成 - 模型: openai, Token: 1861, 時間: 15.08s
2025-08-01 22:18:31 | ERROR    | core.ai_models:generate_text:207 | Anthropic API 呼叫失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:18:31 | ERROR    | core.ai_models:generate_text:387 | AI 生成失敗 - 模型: anthropic, 錯誤: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:18:31 | WARNING  | core.processing_engine:process_single_item:151 | Reviewer 階段失敗: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-sonnet-20240229'}}
2025-08-01 22:18:31 | INFO     | core.processing_engine:process_batch:393 | 項目處理成功: A2556... (15.42s)
2025-08-01 22:18:31 | INFO     | core.processing_engine:process_batch:397 | 批次處理完成: 成功 5, 失敗 0
2025-08-01 22:18:31 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-01 22:18:31 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-01 22:18:31 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-01 22:18:31 | INFO     | gui.main_window:log_message:865 | 處理完成！成功: 5, 失敗: 0, 總 Token: 9,418, 預估費用: $0.008851
2025-08-01 22:18:31 | INFO     | gui.main_window:log_message:865 | 自動顯示 A309... 的預覽
