2025-08-01 15:48:15 | INFO     | __main__:test_logging:198 | 測試日誌訊息
2025-08-01 15:54:15 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 15:54:15 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 15:54:16 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 15:54:16 | WARNING  | core.ai_models:setup_models:279 | Anthropic 模型設定失敗: 請安裝 anthropic 套件: pip install anthropic
2025-08-01 15:54:16 | WARNING  | core.ai_models:setup_models:293 | Google 模型設定失敗: 請安裝 google-generativeai 套件: pip install google-generativeai
2025-08-01 15:54:16 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 15:54:16 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 15:54:16 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 15:54:16 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 15:54:16 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 15:54:16 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 15:54:16 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 15:54:16 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 15:54:16 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 15:54:16 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 15:54:18 | INFO     | gui.main_window:log_message:540 | 選擇 AI 模型: openai
2025-08-01 15:54:18 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 15:54:18 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 15:58:22 | INFO     | gui.main_window:log_message:540 | 選擇工作表: Sheet1
2025-08-01 15:58:22 | INFO     | gui.main_window:log_message:540 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 16:00:40 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 16:00:40 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 16:00:40 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 16:00:40 | WARNING  | core.ai_models:setup_models:279 | Anthropic 模型設定失敗: 請安裝 anthropic 套件: pip install anthropic
2025-08-01 16:00:40 | WARNING  | core.ai_models:setup_models:293 | Google 模型設定失敗: 請安裝 google-generativeai 套件: pip install google-generativeai
2025-08-01 16:00:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 16:00:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 16:00:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 16:00:40 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 16:00:40 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 16:00:40 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 16:00:40 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 16:00:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 16:00:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 16:00:40 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 16:00:41 | INFO     | gui.main_window:log_message:540 | 選擇 AI 模型: openai
2025-08-01 16:00:41 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 16:00:41 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 16:00:49 | INFO     | gui.main_window:log_message:540 | 選擇工作表: Sheet1
2025-08-01 16:00:49 | INFO     | gui.main_window:log_message:540 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 16:01:00 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 16:01:00 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 16:01:00 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 16:01:00 | INFO     | gui.main_window:log_message:540 | 資料載入成功: 4 行, 10 欄
2025-08-01 16:01:43 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-01 16:01:43 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image
2025-08-01 16:01:43 | INFO     | gui.main_window:log_message:540 | 開始處理 4 個項目...
2025-08-01 16:01:43 | INFO     | core.processing_engine:process_batch:312 | 開始批次處理: 行 0 到 3 (共 4 項)
2025-08-01 16:01:43 | INFO     | core.processing_engine:process_batch:315 | 處理項目 1/4 (行 0)
2025-08-01 16:01:43 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 4)
2025-08-01 16:01:43 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['服用', '健康', '美國', '每日', '維生素C']
2025-08-01 16:02:15 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1477,0.0
2025-08-01 16:02:15 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1477, 時間: 32.16s
2025-08-01 16:02:17 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1231,0.0
2025-08-01 16:02:17 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1231, 時間: 1.87s
2025-08-01 16:02:17 | INFO     | core.processing_engine:process_batch:330 | 項目處理成功: PROD002 (34.03s)
2025-08-01 16:02:17 | INFO     | core.processing_engine:process_batch:315 | 處理項目 2/4 (行 1)
2025-08-01 16:02:17 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 12)
2025-08-01 16:02:17 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['休閒椅', '客廳', '臥室', '風格', '材質']
2025-08-01 16:02:44 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1617,0.0
2025-08-01 16:02:44 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1617, 時間: 26.83s
2025-08-01 16:02:46 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1376,0.0
2025-08-01 16:02:46 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1376, 時間: 2.32s
2025-08-01 16:02:46 | INFO     | core.processing_engine:process_batch:330 | 項目處理成功: PROD003 (29.16s)
2025-08-01 16:02:46 | INFO     | core.processing_engine:process_batch:315 | 處理項目 3/4 (行 2)
2025-08-01 16:02:46 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 7)
2025-08-01 16:02:46 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['辦公室', '辦公椅', '保固', '專業', '人體工學']
2025-08-01 16:03:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1680,0.0
2025-08-01 16:03:21 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1680, 時間: 34.95s
2025-08-01 16:03:23 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1429,0.0
2025-08-01 16:03:23 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1429, 時間: 2.25s
2025-08-01 16:03:23 | INFO     | core.processing_engine:process_batch:330 | 項目處理成功: PROD004 (37.21s)
2025-08-01 16:03:23 | INFO     | core.processing_engine:process_batch:315 | 處理項目 4/4 (行 3)
2025-08-01 16:03:23 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 6)
2025-08-01 16:03:23 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['抵抗力', '餐後', '溫和', '天然', '每日']
2025-08-01 16:03:37 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1378,0.0
2025-08-01 16:03:37 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1378, 時間: 13.86s
2025-08-01 16:03:39 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1133,0.0
2025-08-01 16:03:39 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1133, 時間: 1.57s
2025-08-01 16:03:39 | INFO     | core.processing_engine:process_batch:330 | 項目處理成功: PROD005 (15.43s)
2025-08-01 16:03:39 | INFO     | core.processing_engine:process_batch:334 | 批次處理完成: 成功 4, 失敗 0
2025-08-01 16:03:39 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-01 16:03:39 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-01 16:03:39 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-01 16:03:39 | INFO     | gui.main_window:log_message:540 | 處理完成！成功: 4, 失敗: 0, 總 Token: 11321
2025-08-01 16:04:27 | INFO     | core.data_processor:save_excel:237 | 成功儲存檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/l.xlsx
2025-08-01 16:04:27 | INFO     | gui.main_window:log_message:540 | 結果已匯出到: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/l.xlsx
2025-08-01 16:48:05 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 16:48:05 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 16:48:05 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 16:48:05 | WARNING  | core.ai_models:setup_models:279 | Anthropic 模型設定失敗: 請安裝 anthropic 套件: pip install anthropic
2025-08-01 16:48:05 | WARNING  | core.ai_models:setup_models:293 | Google 模型設定失敗: 請安裝 google-generativeai 套件: pip install google-generativeai
2025-08-01 16:48:05 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 16:48:05 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 16:48:05 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 16:48:05 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 16:48:05 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 16:48:05 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 16:48:05 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 16:48:05 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 16:48:05 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 16:48:05 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 16:48:06 | INFO     | gui.main_window:log_message:605 | 選擇 AI 模型: openai
2025-08-01 16:48:06 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 16:48:06 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 16:48:16 | INFO     | gui.main_window:log_message:605 | 選擇工作表: Sheet1
2025-08-01 16:48:16 | INFO     | gui.main_window:log_message:605 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 16:48:22 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 16:48:22 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 16:48:22 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 16:52:01 | INFO     | __main__:test_logging:198 | 測試日誌訊息
2025-08-01 16:53:23 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 16:53:23 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 16:53:23 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 16:53:23 | INFO     | core.ai_models:setup_models:277 | Anthropic 模型已設定
2025-08-01 16:53:23 | INFO     | core.ai_models:setup_models:291 | Google 模型已設定
2025-08-01 16:53:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 16:53:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 16:53:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 16:53:23 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 16:53:23 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 16:53:23 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 16:53:23 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 16:53:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 16:53:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 16:53:23 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 16:53:24 | INFO     | gui.main_window:log_message:605 | 選擇 AI 模型: openai
2025-08-01 16:53:24 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 16:53:24 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 16:54:11 | INFO     | gui.main_window:log_message:605 | 選擇工作表: Sheet1
2025-08-01 16:54:11 | INFO     | gui.main_window:log_message:605 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 16:55:07 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 16:55:07 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 16:55:07 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 16:55:07 | INFO     | gui.main_window:log_message:605 | 資料載入成功: 4 行, 10 欄
2025-08-01 16:55:23 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['ID']
2025-08-01 16:55:23 | INFO     | gui.main_window:log_message:605 | 排除欄位: ID
2025-08-01 16:55:23 | INFO     | gui.main_window:log_message:605 | 開始處理 4 個項目...
2025-08-01 16:55:23 | INFO     | core.processing_engine:process_batch:370 | 開始批次處理: 行 0 到 3 (共 4 項)
2025-08-01 16:55:23 | INFO     | core.processing_engine:process_batch:373 | 處理項目 1/4 (行 0)
2025-08-01 16:55:23 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Immunity
2025-08-01 16:55:23 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 4)
2025-08-01 16:55:23 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['健康', '每日', '服用', '美國', '維生素C']
2025-08-01 16:55:36 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1106,0.0
2025-08-01 16:55:36 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1106, 時間: 12.72s
2025-08-01 16:55:39 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1082,0.0
2025-08-01 16:55:39 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1082, 時間: 2.59s
2025-08-01 16:55:39 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Immunity (15.31s)
2025-08-01 16:55:39 | INFO     | core.processing_engine:process_batch:373 | 處理項目 2/4 (行 1)
2025-08-01 16:55:39 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Furniture
2025-08-01 16:55:39 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 12)
2025-08-01 16:55:39 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['休閒椅', '北歐風格', '材質', '風格', '經典']
2025-08-01 16:55:56 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1081,0.0
2025-08-01 16:55:56 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1081, 時間: 17.10s
2025-08-01 16:55:57 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1062,0.0
2025-08-01 16:55:57 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1062, 時間: 1.43s
2025-08-01 16:55:57 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Furniture (18.53s)
2025-08-01 16:55:57 | INFO     | core.processing_engine:process_batch:373 | 處理項目 3/4 (行 2)
2025-08-01 16:55:57 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Furniture
2025-08-01 16:55:57 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 7)
2025-08-01 16:55:57 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['辦公椅', '辦公室', '安裝', '專業', '書房']
2025-08-01 16:56:20 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1031,0.0
2025-08-01 16:56:20 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1031, 時間: 22.36s
2025-08-01 16:56:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1028,0.0
2025-08-01 16:56:21 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1028, 時間: 1.73s
2025-08-01 16:56:21 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Furniture (24.09s)
2025-08-01 16:56:21 | INFO     | core.processing_engine:process_batch:373 | 處理項目 4/4 (行 3)
2025-08-01 16:56:21 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Immunity
2025-08-01 16:56:21 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 6)
2025-08-01 16:56:21 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['抵抗力', '澳洲', '每日', '維生素C', '長效']
2025-08-01 16:56:43 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1060,0.0
2025-08-01 16:56:43 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1060, 時間: 21.47s
2025-08-01 16:56:44 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1046,0.0
2025-08-01 16:56:44 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1046, 時間: 1.52s
2025-08-01 16:56:44 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Immunity (23.00s)
2025-08-01 16:56:44 | INFO     | core.processing_engine:process_batch:392 | 批次處理完成: 成功 4, 失敗 0
2025-08-01 16:56:44 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-01 16:56:44 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-01 16:56:44 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-01 16:56:44 | INFO     | gui.main_window:log_message:605 | 處理完成！成功: 4, 失敗: 0, 總 Token: 8496
2025-08-01 16:56:44 | INFO     | gui.main_window:log_message:605 | 自動顯示 Immunity 的預覽
2025-08-01 16:57:30 | INFO     | gui.main_window:log_message:605 | 顯示第 4 行的 HTML 預覽
2025-08-01 16:57:30 | INFO     | gui.main_window:log_message:605 | 顯示第 4 行的 HTML 預覽
2025-08-01 16:57:33 | INFO     | gui.main_window:log_message:605 | 顯示第 3 行的 HTML 預覽
2025-08-01 16:57:33 | INFO     | gui.main_window:log_message:605 | 顯示第 3 行的 HTML 預覽
2025-08-01 16:57:34 | INFO     | gui.main_window:log_message:605 | 顯示第 2 行的 HTML 預覽
2025-08-01 16:57:34 | INFO     | gui.main_window:log_message:605 | 顯示第 2 行的 HTML 預覽
2025-08-01 17:10:54 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 17:10:54 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 17:10:54 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 17:10:54 | INFO     | core.ai_models:setup_models:277 | Anthropic 模型已設定
2025-08-01 17:10:54 | INFO     | core.ai_models:setup_models:291 | Google 模型已設定
2025-08-01 17:10:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 17:10:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 17:10:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 17:10:54 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-01 17:10:54 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 17:10:54 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 17:10:54 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 17:10:54 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 17:10:54 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 17:10:54 | INFO     | gui.main_window:init_components:141 | 核心組件初始化完成
2025-08-01 17:10:55 | INFO     | gui.main_window:log_message:643 | 選擇 AI 模型: openai
2025-08-01 17:10:55 | INFO     | gui.main_window:__init__:128 | 主視窗初始化完成
2025-08-01 17:10:56 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-01 17:10:59 | INFO     | gui.main_window:log_message:643 | 選擇工作表: Sheet1
2025-08-01 17:10:59 | INFO     | gui.main_window:log_message:643 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-01 17:11:02 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 17:11:02 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 17:11:02 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 17:11:02 | INFO     | gui.main_window:log_message:643 | 資料載入成功: 4 行, 10 欄
2025-08-01 17:11:14 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/sample_products.xlsx
2025-08-01 17:11:14 | INFO     | core.data_processor:load_excel:46 | 成功載入 4 行資料，10 個欄位
2025-08-01 17:11:14 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['ID', 'Type', 'Band', 'Description', 'Main Ingredients', 'Benefits', 'Usage', 'Warnings', 'Country', 'Image']
2025-08-01 17:11:14 | INFO     | gui.main_window:log_message:643 | 資料載入成功: 4 行, 10 欄
2025-08-01 17:14:55 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['ID']
2025-08-01 17:14:55 | INFO     | gui.main_window:log_message:643 | 排除欄位: ID
2025-08-01 17:14:55 | INFO     | gui.main_window:log_message:643 | 開始處理 4 個項目...
2025-08-01 17:14:55 | INFO     | core.processing_engine:process_batch:370 | 開始批次處理: 行 0 到 3 (共 4 項)
2025-08-01 17:14:55 | INFO     | core.processing_engine:process_batch:373 | 處理項目 1/4 (行 0)
2025-08-01 17:14:55 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Immunity
2025-08-01 17:14:55 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 4)
2025-08-01 17:14:55 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['服用', '美國', '每日', '健康', '維生素C']
2025-08-01 17:15:18 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1100,0.0
2025-08-01 17:15:18 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1100, 時間: 22.94s
2025-08-01 17:15:20 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1077,0.0
2025-08-01 17:15:20 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1077, 時間: 1.31s
2025-08-01 17:15:20 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Immunity (24.25s)
2025-08-01 17:15:20 | INFO     | core.processing_engine:process_batch:373 | 處理項目 2/4 (行 1)
2025-08-01 17:15:20 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Furniture
2025-08-01 17:15:20 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 12)
2025-08-01 17:15:20 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['休閒椅', '舒適', '材質', '客廳', '人體工學']
2025-08-01 17:15:39 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1085,0.0
2025-08-01 17:15:39 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1085, 時間: 19.07s
2025-08-01 17:15:40 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1070,0.0
2025-08-01 17:15:40 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1070, 時間: 1.51s
2025-08-01 17:15:40 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Furniture (20.59s)
2025-08-01 17:15:40 | INFO     | core.processing_engine:process_batch:373 | 處理項目 3/4 (行 2)
2025-08-01 17:15:40 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Furniture
2025-08-01 17:15:40 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 7)
2025-08-01 17:15:40 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['辦公椅', '辦公室', '舒適', '人體工學', '書房']
2025-08-01 17:16:02 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1064,0.0
2025-08-01 17:16:02 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1064, 時間: 21.94s
2025-08-01 17:16:04 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1046,0.0
2025-08-01 17:16:04 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1046, 時間: 1.52s
2025-08-01 17:16:04 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Furniture (23.47s)
2025-08-01 17:16:04 | INFO     | core.processing_engine:process_batch:373 | 處理項目 4/4 (行 3)
2025-08-01 17:16:04 | INFO     | core.processing_engine:_generate_product_name:251 | AI 生成產品名稱: Immunity
2025-08-01 17:16:04 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 6)
2025-08-01 17:16:04 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['抵抗力', '溫和', '長效', '餐後', '維生素C']
2025-08-01 17:16:16 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,1017,0.0
2025-08-01 17:16:16 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 1017, 時間: 12.39s
2025-08-01 17:16:18 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai:gpt-4,998,0.0
2025-08-01 17:16:18 | INFO     | core.ai_models:generate_text:335 | AI 生成完成 - 模型: openai, Token: 998, 時間: 1.59s
2025-08-01 17:16:18 | INFO     | core.processing_engine:process_batch:388 | 項目處理成功: Immunity (13.98s)
2025-08-01 17:16:18 | INFO     | core.processing_engine:process_batch:392 | 批次處理完成: 成功 4, 失敗 0
2025-08-01 17:16:18 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-01 17:16:18 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-01 17:16:18 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-01 17:16:18 | INFO     | gui.main_window:log_message:643 | 處理完成！成功: 4, 失敗: 0, 總 Token: 8457
2025-08-01 17:16:18 | INFO     | gui.main_window:log_message:643 | 自動顯示 Immunity 的預覽
2025-08-01 17:16:30 | INFO     | gui.main_window:log_message:643 | 顯示第 3 行的 HTML 預覽
2025-08-01 17:16:30 | INFO     | gui.main_window:log_message:643 | 顯示第 3 行的 HTML 預覽
2025-08-01 17:16:30 | INFO     | gui.main_window:log_message:643 | 顯示第 3 行的 HTML 預覽
2025-08-01 18:02:03 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 18:02:03 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 18:02:04 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 18:02:04 | INFO     | core.ai_models:setup_models:277 | Anthropic 模型已設定
2025-08-01 18:02:04 | INFO     | core.ai_models:setup_models:291 | Google 模型已設定
2025-08-01 18:02:04 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 18:02:04 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 18:02:04 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 18:02:04 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: test_writer
2025-08-01 18:02:04 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 4 個 Writer Prompt
2025-08-01 18:02:04 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 18:02:04 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 18:02:04 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 18:02:04 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 18:02:04 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 18:02:04 | INFO     | gui.main_window:init_components:143 | 核心組件初始化完成
2025-08-01 18:02:06 | ERROR    | __main__:main:56 | 程式啟動失敗: name 'QLineEdit' is not defined
2025-08-01 18:05:00 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-01 18:05:00 | INFO     | __main__:main:35 | 配置載入完成
2025-08-01 18:05:00 | INFO     | core.ai_models:setup_models:263 | OpenAI 模型已設定
2025-08-01 18:05:00 | INFO     | core.ai_models:setup_models:277 | Anthropic 模型已設定
2025-08-01 18:05:00 | INFO     | core.ai_models:setup_models:291 | Google 模型已設定
2025-08-01 18:05:00 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-01 18:05:00 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-01 18:05:00 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-01 18:05:00 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: test_writer
2025-08-01 18:05:00 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 4 個 Writer Prompt
2025-08-01 18:05:00 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-01 18:05:00 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-01 18:05:00 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-01 18:05:00 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-01 18:05:00 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-01 18:05:00 | INFO     | gui.main_window:init_components:143 | 核心組件初始化完成
2025-08-01 18:05:01 | ERROR    | __main__:main:56 | 程式啟動失敗: name 'QLineEdit' is not defined
