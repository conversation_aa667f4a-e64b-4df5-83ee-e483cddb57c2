#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Updated Reviewer Prompt
測試更新的 Reviewer Prompt
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_reviewer_prompt():
    """測試新的 Reviewer Prompt"""
    print("🔍 測試更新的 Reviewer Prompt...")
    
    try:
        from core.prompt_manager import PromptManager
        from config.settings import load_config
        
        # 載入配置
        config = load_config()
        prompt_manager = PromptManager(config.get('paths.prompts_dir'))
        
        print("✅ Prompt 管理器已載入")
        
        # 測試資料
        test_html = """
        <h1>高品質維生素C膠囊</h1>
        <h2>產品特色</h2>
        <p>本產品含有高濃度維生素C，有助於維持免疫系統健康。</p>
        <h3>主要成分</h3>
        <ul>
            <li>維生素C 1000mg</li>
            <li>檸檬酸</li>
        </ul>
        <h2>使用方法</h2>
        <p>每日1-2粒，餐後服用。</p>
        """
        
        test_product_data = {
            "產品名稱": "維生素C膠囊",
            "品牌": "健康品牌",
            "規格": "60粒/瓶",
            "主要成分": "維生素C",
            "功效": "增強免疫力"
        }
        
        # 測試不同的 Writer Profile
        test_cases = [
            ("pharmacy", "藥局專業風格"),
            ("furniture", "家具設計風格"),
            ("default", "預設風格"),
            (None, "無指定風格")
        ]
        
        print("\n📊 測試不同 Writer Profile 的 Reviewer Prompt:")
        
        for writer_prompt, description in test_cases:
            print(f"\n🔧 測試 {description} (Writer Prompt: {writer_prompt}):")
            
            try:
                formatted_prompt = prompt_manager.format_reviewer_prompt(
                    "standard", test_html, test_product_data, writer_prompt
                )
                
                if formatted_prompt:
                    print("✅ Prompt 格式化成功")
                    
                    # 檢查是否包含 Writer Profile 資訊
                    if "expected_writer_profile" in formatted_prompt.lower() or (writer_prompt and writer_prompt in formatted_prompt):
                        print(f"✅ 包含 Writer Profile 資訊: {writer_prompt or 'default'}")
                    else:
                        print("⚠️ 未找到 Writer Profile 資訊")
                    
                    # 檢查是否包含新的評估標準
                    if "Writer Profile Adherence" in formatted_prompt:
                        print("✅ 包含新的評估標準")
                    else:
                        print("⚠️ 缺少新的評估標準")
                    
                    # 檢查是否包含表格格式檢查
                    if "Table Format" in formatted_prompt:
                        print("✅ 包含表格格式檢查")
                    else:
                        print("⚠️ 缺少表格格式檢查")
                    
                    # 顯示部分內容
                    lines = formatted_prompt.split('\n')
                    print(f"📝 Prompt 長度: {len(lines)} 行")
                    print(f"📝 前幾行預覽:")
                    for i, line in enumerate(lines[:3]):
                        if line.strip():
                            print(f"   {i+1}: {line.strip()}")
                    
                else:
                    print("❌ Prompt 格式化失敗")
                    
            except Exception as e:
                print(f"❌ 測試失敗: {e}")
        
        # 測試完整的 Prompt 內容
        print(f"\n📋 完整 Reviewer Prompt 內容:")
        full_prompt = prompt_manager.format_reviewer_prompt(
            "standard", test_html, test_product_data, "pharmacy"
        )
        
        if full_prompt:
            print("=" * 80)
            print(full_prompt)
            print("=" * 80)
        
        print("\n🎉 Reviewer Prompt 測試完成！")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_prompt_features():
    """顯示新 Prompt 的特色"""
    print("\n💡 新 Reviewer Prompt 特色:")
    
    print("\n✨ **新增評估標準**:")
    print("1. 📝 Writer Profile Adherence - 檢查是否符合指定的寫作風格")
    print("2. 📊 Table Format - 檢查 Shopify 風格表格格式")
    print("3. 🏗️ Structure Compliance - 更詳細的 HTML 結構檢查")
    
    print("\n🎯 **Writer Profile 範例**:")
    print("• Pharmacy: 臨床、安全、事實導向")
    print("• Furniture Shopify Designer: 優雅、生活風格導向、包含規格表")
    print("• Default: 通用風格")
    
    print("\n📏 **評估格式**:")
    print("• ✅ 優秀品質")
    print("• ⚠️ 輕微問題")
    print("• 🛠️ 需要大幅修改")
    print("• ❌ 無法使用")
    
    print("\n📝 **評估範例**:")
    print("• ✅ 內容完整優質")
    print("• ⚠️ 少了主成分段")
    print("• 🛠️ 表格樣式錯誤")
    print("• ❌ 與家具語氣不符")
    print("• ❌ 缺 h2/h3 結構")

def main():
    """主測試函數"""
    print("🚀 Reviewer Prompt 更新測試")
    print("=" * 60)
    
    success = test_reviewer_prompt()
    show_prompt_features()
    
    if success:
        print("\n✅ Reviewer Prompt 更新成功！")
        print("\n🎯 更新亮點:")
        print("✅ 新增 Writer Profile Adherence 評估")
        print("✅ 支援 Shopify 表格格式檢查")
        print("✅ 更詳細的結構合規性檢查")
        print("✅ 智能 Writer Profile 傳遞")
        print("✅ 向後相容舊版 Prompt")
        
        print("\n🚀 立即體驗:")
        print("1. 啟動程式: python main.py")
        print("2. 選擇不同的 Writer Prompt (pharmacy, furniture, default)")
        print("3. 處理產品，查看新的 Reviewer 評估")
        print("4. 觀察 Reviewer 如何評估 Writer Profile 符合度")
    else:
        print("\n❌ 測試過程中遇到問題，請檢查錯誤訊息。")
    
    return success

if __name__ == "__main__":
    main()
