# 🧠 AI 商品描述優化系統

一套專為藥妝/傢俱電商設計的圖形化 AI 工具，結合 Claude 與 ChatGPT，協助產出符合 SEO、結構化、圖片輔助理解的 HTML 商品描述。

## ✨ 主要功能

### 🔧 核心功能
- **Excel 檔案處理**: 支援 `.xlsx` 檔案，可選取處理範圍與欄位
- **多欄位內容整合**: 自動融合所有欄位資料，AI 智能理解語意
- **圖片輔助描述**: 支援多張圖片分析，提升描述準確性
- **SEO 關鍵字嵌入**: 智能選擇和自然嵌入關鍵字
- **標準化 HTML 輸出**: 固定結構，符合 SEO 最佳實踐

### 🤖 AI 整合
- **多模型支援**: Claude、ChatGPT、Gemini
- **雙重處理流程**: Writer + Reviewer 兩階段品質保證
- **Review-Only Redo**: 智能識別需改善項目並重新處理

### 🎨 使用者介面
- **PyQt5 圖形介面**: 直觀易用的操作介面
- **即時 HTML 預覽**: 所見即所得的預覽功能
- **批次處理**: 支援大量商品同時處理
- **進度追蹤**: 即時顯示處理進度和統計

## 🚀 快速開始

### 1. 安裝依賴
```bash
pip install -r requirements.txt
```

### 2. 設定 API 金鑰
編輯 `config/ai_keys.env` 檔案：
```env
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
```

### 3. 啟動程式
```bash
python main.py
```

## 📁 專案結構

```
AI_Description_Editor/
├── main.py                    # 主程式入口
├── requirements.txt           # 依賴套件
├── README.md                 # 說明文件
├── config/                   # 配置檔案
│   ├── settings.py          # 系統設定
│   └── ai_keys.env          # API 金鑰
├── core/                     # 核心模組
│   ├── data_processor.py    # 資料處理
│   ├── ai_models.py         # AI 模型整合
│   ├── prompt_manager.py    # Prompt 管理
│   ├── keyword_manager.py   # 關鍵字管理
│   ├── html_generator.py    # HTML 生成
│   ├── processing_engine.py # 處理引擎
│   └── review_engine.py     # 審查引擎
├── gui/                      # 圖形介面
│   └── main_window.py       # 主視窗
├── utils/                    # 工具模組
│   └── logger.py            # 日誌工具
├── prompts/                  # Prompt 模板
│   ├── writer/              # Writer Prompts
│   └── reviewer/            # Reviewer Prompts
├── categories/               # 關鍵字分類
│   ├── _general/            # 通用關鍵字
│   ├── Immunity/            # 免疫力產品
│   └── Furniture/           # 傢俱產品
├── Product Picture/          # 產品圖片
└── logs/                     # 日誌檔案
```

## 🔧 使用說明

### 1. 載入資料
1. 點擊「選擇 Excel」載入產品資料
2. 選擇工作表和處理範圍
3. 設定排除欄位（如 ID、SKU）
4. 可選擇圖片資料夾啟用圖片輔助

### 2. 設定 AI 參數
1. 選擇 AI 模型（Claude/ChatGPT/Gemini）
2. 選擇 Writer Prompt 風格
3. 選擇 Reviewer Prompt 標準

### 3. 開始處理
1. 點擊「載入資料」驗證設定
2. 點擊「開始處理」執行批次處理
3. 即時查看處理進度和結果

### 4. Review-Only 模式
1. 載入已處理的 Excel 檔案
2. 點擊「Review-Only 模式」
3. 系統自動識別需改善項目並重新處理

### 5. 匯出結果
點擊「匯出結果」儲存完整的處理結果

## 📋 HTML 輸出結構

系統產生的 HTML 遵循以下標準結構：

```html
<h1>產品名稱</h1>

<h2>Description</h2>
  <h3>Product Overview</h3>
  <h3>Main Benefits</h3>

<h2>Ingredients</h2>
  <h3>Active Ingredients</h3>
  <h3>Free From / Allergy Info</h3>

<h2>How to Use</h2>
  <h3>Dosage</h3>
  <h3>Usage Warnings</h3>

<h2>Additional Information</h2>
  <h3>Miscellaneous</h3>
```

## 🎯 關鍵字管理

### 關鍵字檔案結構
- `categories/_general/keywords.txt`: 通用關鍵字
- `categories/[分類]/keywords.txt`: 分類專用關鍵字

### 智能關鍵字選擇
系統會根據產品內容自動：
1. 推斷產品分類
2. 選擇相關關鍵字
3. 自然嵌入描述中

## 🔍 Prompt 管理

### Writer Prompts
位於 `prompts/writer/` 目錄，用於生成產品描述：
- 支援多種風格模板
- 可自訂新增 `.txt` 檔案
- 支援變數替換（產品資料、關鍵字）

### Reviewer Prompts
位於 `prompts/reviewer/` 目錄，用於品質審查：
- 15字內精簡評論
- 使用狀態指標（✅ ⚠️ 🛠️ ❌）
- 自動識別改善需求

## 📊 處理統計

系統會記錄詳細的處理統計：
- 處理成功/失敗數量
- Token 使用量統計
- 處理時間分析
- API 呼叫記錄

## 🛠️ 進階設定

### 自訂 Prompt
1. 在對應目錄新增 `.txt` 檔案
2. 使用變數 `{product_data}` 和 `{keywords}`
3. 重新啟動程式載入新 Prompt

### 新增產品分類
1. 在 `categories/` 建立新資料夾
2. 新增 `keywords.txt` 檔案
3. 每行一個關鍵字

### 調整 HTML 結構
修改 `config/settings.py` 中的 `html_template.structure` 設定

## 🐛 故障排除

### 常見問題
1. **API 金鑰錯誤**: 檢查 `config/ai_keys.env` 設定
2. **Excel 載入失敗**: 確認檔案格式為 `.xlsx`
3. **圖片載入問題**: 檢查圖片檔案路徑和格式
4. **處理失敗**: 查看 `logs/` 目錄的錯誤日誌

### 日誌檔案
- `logs/app_YYYYMMDD.log`: 一般日誌
- `logs/error_YYYYMMDD.log`: 錯誤日誌
- `logs/usage_stats.csv`: API 使用統計

## 📝 更新日誌

### v1.0.0 (2025-08-01)
- ✅ 完整的 AI 商品描述生成系統
- ✅ 支援多 AI 模型整合
- ✅ PyQt5 圖形使用者介面
- ✅ 雙重處理流程（Writer + Reviewer）
- ✅ Review-Only Redo 功能
- ✅ SEO 關鍵字智能嵌入
- ✅ 標準化 HTML 輸出

## 📄 授權

本專案採用 MIT 授權條款。

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request 來改善這個專案！

---

**開發者**: AI Assistant  
**版本**: v1.0.0  
**最後更新**: 2025-08-01
