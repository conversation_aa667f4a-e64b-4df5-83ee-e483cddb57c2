# 🚀 AI Description Editor - Advanced Product Description Optimization System

[![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg)](https://python.org)
[![PyQt5](https://img.shields.io/badge/PyQt5-5.15%2B-green.svg)](https://pypi.org/project/PyQt5/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)]()

A powerful, AI-driven system for generating and optimizing product descriptions with advanced cost tracking, multi-model support, and professional workflow management.

## 🎯 Key Features

### 🤖 **Advanced AI Model Management**
- **Separate AI Selection**: Independent Writer and Reviewer AI models
- **Multi-Provider Support**: OpenAI, Anthropic, Google Gemini
- **Smart Recommendations**: Economy and quality-based model suggestions
- **Real-time Switching**: Change models without restarting

### 💰 **Comprehensive Cost Tracking**
- **Real-time Monitoring**: Track token usage and costs as you work
- **Per-Model Breakdown**: Detailed statistics for each AI provider
- **Cost Optimization**: Smart recommendations for budget management
- **Free Tier Tracking**: Monitor Google Gemini's 1M token/month limit

### 📝 **Visual Prompt Management**
- **CRUD Operations**: Create, Read, Update, Delete prompts via GUI
- **Dual Categories**: Separate Writer and Reviewer prompt libraries
- **Real-time Editing**: Instant preview and validation
- **Template Library**: Pre-built prompts for different product types

### 🖥️ **Enhanced User Interface**
- **Split Preview**: HTML rendering above, reviewer feedback below
- **Tabbed Workflow**: Organized interface for all features
- **Auto-Detection**: Smart processing range configuration
- **Professional Design**: Modern, intuitive user experience

## 📊 Supported AI Models & Pricing

| Provider | Model | Input Cost* | Output Cost* | Best For |
|----------|-------|-------------|--------------|----------|
| 🥇 **OpenAI** | GPT-4o-mini | $0.15/1M | $0.60/1M | **Daily Use** (Recommended) |
| 🥈 **Anthropic** | Claude-3.5-Sonnet | $3.00/1M | $15.00/1M | **High Quality** |
| 🥉 **Google** | Gemini-Pro | $1.25/1M** | $3.75/1M** | **Testing** (Free Tier) |

*Costs per 1M tokens (2025 pricing)  
**Free tier: 1M tokens/month

## 🚀 Quick Start

### Prerequisites
```bash
# Python 3.8 or higher
python --version

# Required packages
pip install -r requirements.txt
```

### Installation
```bash
# Clone the repository
git clone https://github.com/your-username/AI_Description_Editor.git
cd AI_Description_Editor

# Install dependencies
pip install -r requirements.txt

# Configure API keys
cp config/config.example.yaml config/config.yaml
# Edit config.yaml with your API keys
```

### First Run
```bash
# Start the application
python main.py

# Follow the setup wizard:
# 1. Configure AI API keys
# 2. Load sample data (sample_products.xlsx)
# 3. Choose AI models (recommended: GPT-4o-mini for both)
# 4. Start processing!
```

## 📖 Detailed Usage Guide

### 🔧 **Initial Setup**

1. **Configure API Keys**
   ```yaml
   # config/config.yaml
   ai_models:
     openai:
       api_key: "your-openai-api-key"
       model: "gpt-4o-mini"
     anthropic:
       api_key: "your-anthropic-api-key"
       model: "claude-3-5-sonnet-20241022"
     google:
       api_key: "your-google-api-key"
       model: "gemini-pro"
   ```

2. **Prepare Your Data**
   - Excel format with product information
   - Required columns: Product Name, Description, Features
   - Optional: Images, Categories, Keywords

### 🎯 **Core Workflow**

#### **Step 1: Load Data**
```
File → Load Excel → Select your product file
✅ Auto-detects total rows for processing
✅ Validates data structure
✅ Shows preview in data table
```

#### **Step 2: Configure AI Models**
```
Writer AI Model: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
Reviewer AI Model: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
```

**Model Selection Guide:**
- **Economy Setup**: GPT-4o-mini for both (best value)
- **Quality Setup**: Claude-3.5-Sonnet for both (highest quality)
- **Testing Setup**: Gemini-Pro for both (free tier)
- **Hybrid Setup**: GPT-4o-mini (Writer) + Claude-3.5-Sonnet (Reviewer)

#### **Step 3: Select Prompts**
```
Writer Prompt: Choose from library or create custom
Reviewer Prompt: Select evaluation criteria
```

#### **Step 4: Process & Monitor**
```
✅ Real-time cost tracking
✅ Progress monitoring
✅ Quality assessment
✅ Automatic preview generation
```

### 💰 **Cost Management**

#### **Cost Calculator Tab**
- **Usage Statistics**: Requests, tokens, costs per model
- **Real-time Updates**: Live tracking during processing
- **Optimization Tips**: Smart recommendations
- **Reset/Export**: Manage your usage data

#### **Cost Optimization Strategies**

**For Small Batches (1-10 products):**
```
Recommended: GPT-4o-mini (Writer + Reviewer)
Expected Cost: $0.001 - $0.005
Processing Time: 2-5 minutes
```

**For Medium Batches (10-50 products):**
```
Economy: GPT-4o-mini (Writer + Reviewer)
Quality: GPT-4o-mini (Writer) + Claude-3.5-Sonnet (Reviewer)
Expected Cost: $0.005 - $0.025
Processing Time: 5-15 minutes
```

**For Large Batches (50+ products):**
```
Budget: Gemini-Pro (if within free tier)
Balanced: GPT-4o-mini (Writer + Reviewer)
Premium: Claude-3.5-Sonnet (Writer + Reviewer)
Expected Cost: $0.025 - $0.200+
Processing Time: 15+ minutes
```

### 📝 **Prompt Management**

#### **Creating Custom Prompts**

**Writer Prompt Example:**
```
You are an expert product description writer for e-commerce.

Product Information:
{product_data}

Keywords to include: {keywords}

Requirements:
- Write engaging, SEO-optimized HTML description
- Use proper HTML structure with headings
- Include key features and benefits
- Target length: 200-400 words
- Professional tone

Generate HTML description:
```

**Reviewer Prompt Example:**
```
Review this product description for quality and effectiveness.

Original Product: {product_data}
Generated Description: {html_content}

Evaluate:
1. ✅ SEO optimization
2. ✅ Readability and engagement
3. ✅ Accuracy and completeness
4. ✅ HTML structure quality

Provide brief feedback with status indicators (✅/⚠️/❌):
```

#### **Prompt Library Management**
- **Create**: New prompts for specific product categories
- **Edit**: Modify existing templates
- **View**: Preview prompt content and variables
- **Delete**: Remove unused prompts
- **Organize**: Separate Writer and Reviewer libraries

### 🖥️ **Interface Guide**

#### **Main Tabs**
1. **HTML Preview**: Split view with description and reviewer feedback
2. **HTML Source**: Raw HTML code for technical review
3. **Data Table**: Complete processing results and statistics
4. **Image Preview**: Visual content management
5. **Cost Calculation**: Real-time usage and cost tracking
6. **Prompt Management**: Visual prompt editing and organization

#### **Processing Controls**
- **Range Selection**: Auto-detected or manual configuration
- **Model Selection**: Independent Writer/Reviewer AI choices
- **Prompt Selection**: Choose from library or use custom
- **Image Processing**: Optional visual content integration
- **Batch Processing**: Efficient handling of large datasets

## 🔍 Advanced Features

### **Batch Processing**
- Process large datasets efficiently
- Automatic progress tracking
- Error handling and recovery
- Cost monitoring throughout

### **Quality Assurance**
- Dual-stage processing (Writer → Reviewer)
- Quality scoring and feedback
- Automatic HTML validation
- SEO optimization checking

### **Export Options**
- Excel with new columns (HTML Output, Review Notes, Keywords Used)
- HTML files for web deployment
- Cost reports for budget tracking
- Processing logs for analysis

### **Image Integration**
- Automatic image detection and processing
- Multi-format support (JPG, PNG, WebP)
- AI-powered image analysis
- Visual content optimization

## 📊 Performance & Scalability

### **Processing Speed**
- **Small batches (1-10)**: 30-60 seconds
- **Medium batches (10-50)**: 2-10 minutes  
- **Large batches (50+)**: 10+ minutes

### **Cost Efficiency**
- **GPT-4o-mini**: ~$0.0005 per product description
- **Claude-3.5-Sonnet**: ~$0.005 per product description
- **Gemini-Pro**: Free for up to 1M tokens/month

### **Quality Metrics**
- **SEO Score**: Automated keyword optimization
- **Readability**: Professional writing standards
- **Accuracy**: AI-powered fact checking
- **Completeness**: Feature coverage analysis

## 🛠️ Configuration

### **Advanced Settings**
```yaml
# config/config.yaml
processing:
  max_concurrent: 5
  timeout_seconds: 120
  retry_attempts: 3
  
quality:
  min_word_count: 150
  max_word_count: 500
  required_keywords: 3
  
cost_limits:
  daily_budget: 10.00
  per_request_limit: 0.10
  warning_threshold: 0.80
```

### **Prompt Templates**
```yaml
# Custom prompt variables
templates:
  variables:
    - product_data
    - keywords
    - category
    - target_audience
    - brand_voice
```

## 🔧 Troubleshooting

### **Common Issues**

**API Key Errors:**
```bash
# Check configuration
cat config/config.yaml

# Verify API key format
# OpenAI: sk-...
# Anthropic: sk-ant-...
# Google: AIza...
```

**Cost Tracking Not Showing:**
```bash
# Restart application
python main.py

# Check Cost Calculation tab
# Verify API calls are being made
# Look for cost updates in logs
```

**Processing Failures:**
```bash
# Check internet connection
# Verify API quotas and limits
# Review error logs in application
# Try smaller batch sizes
```

### **Performance Optimization**
- Use GPT-4o-mini for faster processing
- Process in smaller batches for better control
- Monitor cost limits to avoid overspending
- Use Gemini-Pro for testing (free tier)

## 📈 Roadmap

### **Upcoming Features**
- [ ] Bulk template management
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] API rate limiting controls
- [ ] Custom model fine-tuning
- [ ] Automated A/B testing
- [ ] Integration with e-commerce platforms
- [ ] Advanced SEO analysis tools

### **Version History**
- **v2.0.0** (2025-01): Major UI overhaul, cost tracking, multi-model support
- **v1.5.0** (2024-12): Enhanced prompt management, image processing
- **v1.0.0** (2024-11): Initial release with basic AI processing

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

### **Development Setup**
```bash
# Clone for development
git clone https://github.com/your-username/AI_Description_Editor.git
cd AI_Description_Editor

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate  # Windows

# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
python -m pytest tests/
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for GPT models and API
- Anthropic for Claude models
- Google for Gemini AI platform
- PyQt5 for the GUI framework
- The open-source community for inspiration and tools

## 📞 Support

- **Documentation**: [Wiki](https://github.com/your-username/AI_Description_Editor/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-username/AI_Description_Editor/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/AI_Description_Editor/discussions)
- **Email**: <EMAIL>

---

**Made with ❤️ for e-commerce professionals who demand quality and efficiency.**

*Last updated: January 2025*
