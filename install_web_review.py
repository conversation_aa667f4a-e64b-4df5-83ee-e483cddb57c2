#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Installation script for Web Review System
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages for web review system"""
    print("🌐 Installing Web Review System Dependencies")
    print("=" * 50)
    
    requirements_file = Path("web_review/requirements.txt")
    
    if not requirements_file.exists():
        print("❌ Requirements file not found")
        return False
    
    try:
        print("📦 Installing packages...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✅ All packages installed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        return False

def test_installation():
    """Test if all required packages are available"""
    print("\n🧪 Testing Installation...")
    
    required_packages = ["flask", "flask_cors", "pandas", "openpyxl"]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - Not installed")
            return False
    
    print("✅ All packages are working correctly!")
    return True

def main():
    """Main installation function"""
    print("🚀 Web Review System Setup")
    print("=" * 50)
    
    # Install requirements
    if not install_requirements():
        print("\n❌ Installation failed. Please check the error messages above.")
        return False
    
    # Test installation
    if not test_installation():
        print("\n❌ Some packages are not working correctly.")
        return False
    
    print("\n🎉 Web Review System is ready!")
    print("\n💡 How to use:")
    print("   1. Process some items in the main application")
    print("   2. Click the '🌐 Web 評審系統' button")
    print("   3. Or manually run: python web_review/launch_review.py")
    print("\n📚 See web_review/README.md for detailed instructions")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
