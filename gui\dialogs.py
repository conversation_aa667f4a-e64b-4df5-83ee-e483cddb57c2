#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dialog Classes
對話框類別
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
    QPushButton, QLineEdit, QTextEdit, QDialogButtonBox,
    QMessageBox, QScrollArea, QWidget
)
from PyQt5.QtCore import Qt
from typing import Dict, Any
from core.i18n import I18n


class APIKeysDialog(QDialog):
    """API 金鑰設定對話框"""
    
    def __init__(self, parent=None, i18n: I18n = None, current_keys: Dict[str, str] = None):
        super().__init__(parent)
        self.i18n = i18n or I18n()
        self.current_keys = current_keys or {}
        self.init_ui()
    
    def init_ui(self):
        """初始化 UI"""
        self.setWindowTitle(self.i18n.t("api_keys_title"))
        self.setModal(True)
        self.resize(500, 300)
        
        layout = QVBoxLayout(self)
        
        # API 金鑰輸入區域
        form_layout = QGridLayout()
        
        # OpenAI API 金鑰
        form_layout.addWidget(QLabel(self.i18n.t("openai_api_key")), 0, 0)
        self.openai_key_edit = QLineEdit()
        self.openai_key_edit.setEchoMode(QLineEdit.Password)
        self.openai_key_edit.setText(self.current_keys.get("openai", ""))
        form_layout.addWidget(self.openai_key_edit, 0, 1)
        
        # Anthropic API 金鑰
        form_layout.addWidget(QLabel(self.i18n.t("anthropic_api_key")), 1, 0)
        self.anthropic_key_edit = QLineEdit()
        self.anthropic_key_edit.setEchoMode(QLineEdit.Password)
        self.anthropic_key_edit.setText(self.current_keys.get("anthropic", ""))
        form_layout.addWidget(self.anthropic_key_edit, 1, 1)
        
        # Google API 金鑰
        form_layout.addWidget(QLabel(self.i18n.t("google_api_key")), 2, 0)
        self.google_key_edit = QLineEdit()
        self.google_key_edit.setEchoMode(QLineEdit.Password)
        self.google_key_edit.setText(self.current_keys.get("google", ""))
        form_layout.addWidget(self.google_key_edit, 2, 1)
        
        layout.addLayout(form_layout)
        
        # 按鈕
        button_box = QDialogButtonBox(
            QDialogButtonBox.Save | QDialogButtonBox.Cancel
        )
        button_box.button(QDialogButtonBox.Save).setText(self.i18n.t("save"))
        button_box.button(QDialogButtonBox.Cancel).setText(self.i18n.t("cancel"))
        
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        layout.addWidget(button_box)
    
    def get_api_keys(self) -> Dict[str, str]:
        """取得 API 金鑰"""
        return {
            "openai": self.openai_key_edit.text().strip(),
            "anthropic": self.anthropic_key_edit.text().strip(),
            "google": self.google_key_edit.text().strip()
        }


class SEOPreviewDialog(QDialog):
    """SEO 預覽對話框"""
    
    def __init__(self, parent=None, i18n: I18n = None, seo_data: Dict[str, Any] = None):
        super().__init__(parent)
        self.i18n = i18n or I18n()
        self.seo_data = seo_data or {}
        self.init_ui()
    
    def init_ui(self):
        """初始化 UI"""
        self.setWindowTitle(self.i18n.t("seo_preview_title"))
        self.setModal(True)
        self.resize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # 檔案資訊
        info_layout = QGridLayout()
        
        info_layout.addWidget(QLabel(self.i18n.t("seo_file_path")), 0, 0)
        file_path_label = QLabel(self.seo_data.get("file_path", "N/A"))
        file_path_label.setWordWrap(True)
        info_layout.addWidget(file_path_label, 0, 1)
        
        info_layout.addWidget(QLabel(self.i18n.t("total_keywords")), 1, 0)
        total_label = QLabel(str(self.seo_data.get("total_keywords", 0)))
        info_layout.addWidget(total_label, 1, 1)
        
        layout.addLayout(info_layout)
        
        # 預覽內容
        layout.addWidget(QLabel(self.i18n.t("preview_content")))
        
        preview_text = QTextEdit()
        preview_text.setReadOnly(True)
        preview_text.setPlainText(self.seo_data.get("preview", "無內容"))
        layout.addWidget(preview_text)
        
        # 關閉按鈕
        close_btn = QPushButton(self.i18n.t("cancel"))
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)


class ImagePreviewWidget(QWidget):
    """圖片預覽小工具"""
    
    def __init__(self, parent=None, i18n: I18n = None):
        super().__init__(parent)
        self.i18n = i18n or I18n()
        self.current_images = []
        self.current_index = 0
        self.init_ui()
    
    def init_ui(self):
        """初始化 UI"""
        layout = QVBoxLayout(self)
        
        # 導航按鈕
        nav_layout = QHBoxLayout()
        
        self.prev_btn = QPushButton(self.i18n.t("previous"))
        self.prev_btn.clicked.connect(self.show_previous)
        nav_layout.addWidget(self.prev_btn)
        
        self.current_label = QLabel(self.i18n.t("current_item", 0, 0))
        self.current_label.setAlignment(Qt.AlignCenter)
        nav_layout.addWidget(self.current_label)
        
        self.next_btn = QPushButton(self.i18n.t("next"))
        self.next_btn.clicked.connect(self.show_next)
        nav_layout.addWidget(self.next_btn)
        
        layout.addLayout(nav_layout)
        
        # 圖片顯示區域
        self.image_scroll = QScrollArea()
        self.image_label = QLabel("無圖片")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("QLabel { border: 1px solid gray; min-height: 200px; }")
        
        self.image_scroll.setWidget(self.image_label)
        self.image_scroll.setWidgetResizable(True)
        layout.addWidget(self.image_scroll)
        
        self.update_navigation()
    
    def set_images(self, images: list):
        """設定圖片列表"""
        self.current_images = images
        self.current_index = 0
        self.update_display()
        self.update_navigation()
    
    def show_previous(self):
        """顯示上一張圖片"""
        if self.current_index > 0:
            self.current_index -= 1
            self.update_display()
            self.update_navigation()
    
    def show_next(self):
        """顯示下一張圖片"""
        if self.current_index < len(self.current_images) - 1:
            self.current_index += 1
            self.update_display()
            self.update_navigation()
    
    def update_display(self):
        """更新顯示"""
        if not self.current_images:
            self.image_label.setText("無圖片")
            return
        
        # 這裡應該載入並顯示圖片
        # 暫時顯示檔案名稱
        current_image = self.current_images[self.current_index]
        self.image_label.setText(f"圖片: {current_image}")
    
    def update_navigation(self):
        """更新導航狀態"""
        total = len(self.current_images)
        current = self.current_index + 1 if total > 0 else 0
        
        self.current_label.setText(self.i18n.t("current_item", current, total))
        self.prev_btn.setEnabled(self.current_index > 0)
        self.next_btn.setEnabled(self.current_index < total - 1)


class HTMLPreviewWidget(QWidget):
    """HTML 預覽小工具"""
    
    def __init__(self, parent=None, i18n: I18n = None):
        super().__init__(parent)
        self.i18n = i18n or I18n()
        self.current_htmls = []
        self.current_index = 0
        self.init_ui()
    
    def init_ui(self):
        """初始化 UI"""
        layout = QVBoxLayout(self)
        
        # 導航按鈕
        nav_layout = QHBoxLayout()
        
        self.prev_btn = QPushButton(self.i18n.t("previous"))
        self.prev_btn.clicked.connect(self.show_previous)
        nav_layout.addWidget(self.prev_btn)
        
        self.current_label = QLabel(self.i18n.t("current_item", 0, 0))
        self.current_label.setAlignment(Qt.AlignCenter)
        nav_layout.addWidget(self.current_label)
        
        self.next_btn = QPushButton(self.i18n.t("next"))
        self.next_btn.clicked.connect(self.show_next)
        nav_layout.addWidget(self.next_btn)
        
        layout.addLayout(nav_layout)
        
        # HTML 顯示區域
        self.html_display = QTextEdit()
        self.html_display.setReadOnly(True)
        layout.addWidget(self.html_display)
        
        self.update_navigation()
    
    def set_htmls(self, htmls: list):
        """設定 HTML 列表"""
        self.current_htmls = htmls
        self.current_index = 0
        self.update_display()
        self.update_navigation()
    
    def show_previous(self):
        """顯示上一個 HTML"""
        if self.current_index > 0:
            self.current_index -= 1
            self.update_display()
            self.update_navigation()
    
    def show_next(self):
        """顯示下一個 HTML"""
        if self.current_index < len(self.current_htmls) - 1:
            self.current_index += 1
            self.update_display()
            self.update_navigation()
    
    def update_display(self):
        """更新顯示"""
        if not self.current_htmls:
            self.html_display.setHtml("<p>無 HTML 內容</p>")
            return
        
        current_html = self.current_htmls[self.current_index]
        self.html_display.setHtml(current_html)
    
    def update_navigation(self):
        """更新導航狀態"""
        total = len(self.current_htmls)
        current = self.current_index + 1 if total > 0 else 0
        
        self.current_label.setText(self.i18n.t("current_item", current, total))
        self.prev_btn.setEnabled(self.current_index > 0)
        self.next_btn.setEnabled(self.current_index < total - 1)
