#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Launch script for the AI Description Review System
"""

import os
import sys
import time
import threading
import webbrowser
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def find_excel_files():
    """Find Excel files in current directory"""
    current_dir = Path('.')
    excel_files = list(current_dir.glob('*.xlsx'))
    return [str(f) for f in excel_files if not f.name.startswith('~')]

def launch_review_system(excel_file=None, port=8080, auto_open=True):
    """Launch the review system"""
    print("🌐 AI Description Review System Launcher")
    print("=" * 50)
    
    # Check if we're in the web_review directory
    if Path('review_server.py').exists():
        # We're in the web_review directory
        server_script = 'review_server.py'
    else:
        # We're in the parent directory
        server_script = 'web_review/review_server.py'
        if not Path(server_script).exists():
            print("❌ Error: review_server.py not found")
            return False
    
    # Find Excel files if none specified
    if not excel_file:
        excel_files = find_excel_files()
        if not excel_files:
            print("❌ No Excel files found in current directory")
            print("💡 Please place your exported Excel file in the current directory")
            return False
        
        if len(excel_files) == 1:
            excel_file = excel_files[0]
            print(f"📁 Found Excel file: {excel_file}")
        else:
            print("📁 Multiple Excel files found:")
            for i, file in enumerate(excel_files, 1):
                print(f"   {i}. {file}")
            
            try:
                choice = input("\nSelect file number (or press Enter for first): ").strip()
                if choice:
                    excel_file = excel_files[int(choice) - 1]
                else:
                    excel_file = excel_files[0]
            except (ValueError, IndexError):
                excel_file = excel_files[0]
            
            print(f"✅ Selected: {excel_file}")
    
    # Check if Excel file exists
    if not Path(excel_file).exists():
        print(f"❌ Excel file not found: {excel_file}")
        return False
    
    # Import and start server
    try:
        from review_server import ReviewServer
        
        print(f"🚀 Starting server on port {port}...")
        server = ReviewServer(port=port)
        
        # Open browser after a short delay
        if auto_open:
            def open_browser():
                time.sleep(2)  # Wait for server to start
                url = f"http://localhost:{port}"
                print(f"🌐 Opening browser: {url}")
                webbrowser.open(url)
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
        
        print("💡 Instructions:")
        print("   - Use the web interface to review HTML descriptions")
        print("   - Click Approve/Reject/Pending for each item")
        print("   - Add comments in the text area")
        print("   - Use Next/Previous buttons or Ctrl+Arrow keys")
        print("   - Reviews are automatically saved to Excel")
        print("   - Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Start server
        server.run(debug=False)
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure Flask is installed: pip install flask flask-cors")
        return False
    except Exception as e:
        print(f"❌ Server error: {e}")
        return False

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Launch AI Description Review System')
    parser.add_argument('--excel', type=str, help='Path to Excel file')
    parser.add_argument('--port', type=int, default=8080, help='Server port')
    parser.add_argument('--no-browser', action='store_true', help='Don\'t auto-open browser')
    
    args = parser.parse_args()
    
    success = launch_review_system(
        excel_file=args.excel,
        port=args.port,
        auto_open=not args.no_browser
    )
    
    if not success:
        sys.exit(1)

if __name__ == '__main__':
    main()
