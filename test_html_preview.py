#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test HTML Preview Functionality
測試 HTML 預覽功能
"""

import sys
import os
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_html():
    """建立測試用的 HTML 內容"""
    test_html = """
<h1>Swisse Vitamin C 1000mg</h1>

<h2>Description</h2>
<h3>Product Overview</h3>
<p>Swisse Vitamin C 是一款<strong>高效維生素C補充劑</strong>，來自澳洲知名品牌 Swisse。每粒含有 1000mg 的維生素C，搭配生物類黃酮和玫瑰果萃取，提供全方位的<em>免疫支持</em>。</p>

<h3>Main Benefits</h3>
<ul>
    <li><strong>增強免疫系統</strong> - 提升身體自然防禦能力</li>
    <li><strong>強效抗氧化</strong> - 對抗自由基傷害</li>
    <li><strong>促進膠原蛋白合成</strong> - 維持肌膚健康</li>
    <li><strong>支持鐵質吸收</strong> - 改善營養吸收效率</li>
</ul>

<h2>Ingredients</h2>
<h3>Active Ingredients</h3>
<p>每粒含有：</p>
<ul>
    <li>維生素C (抗壞血酸) - 1000mg</li>
    <li>生物類黃酮 - 25mg</li>
    <li>玫瑰果萃取 - 5mg</li>
</ul>

<h3>Free From / Allergy Info</h3>
<p>本產品<strong>不含</strong>：人工色素、人工香料、防腐劑。適合素食者使用。如對任何成分過敏，請諮詢醫師。</p>

<h2>How to Use</h2>
<h3>Dosage</h3>
<p><strong>建議用量</strong>：成人每日1粒，餐後服用，或遵照醫師指示。</p>

<h3>Usage Warnings</h3>
<p><em>注意事項</em>：</p>
<ul>
    <li>孕婦、哺乳期婦女使用前請諮詢醫師</li>
    <li>請存放於陰涼乾燥處，避免陽光直射</li>
    <li>請勿超過建議劑量</li>
    <li>如有不適請停止使用並諮詢醫師</li>
</ul>

<h2>Additional Information</h2>
<h3>Miscellaneous</h3>
<p><strong>產地</strong>：澳洲<br>
<strong>包裝</strong>：60粒/瓶<br>
<strong>保存期限</strong>：請參考包裝標示<br>
<strong>認證</strong>：TGA認證，品質保證</p>
"""
    return test_html


def test_html_rendering():
    """測試 HTML 渲染功能"""
    print("🔍 測試 HTML 渲染功能...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QTextEdit, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt
        
        # 建立測試應用程式（不顯示視窗）
        app = QApplication([])
        
        # 建立 QTextEdit 並測試 HTML 渲染
        text_edit = QTextEdit()
        text_edit.setAcceptRichText(True)
        
        # 測試 HTML 內容
        test_html = create_test_html()
        
        # 設定 HTML 內容
        text_edit.setHtml(test_html)
        
        # 檢查是否正確設定
        html_content = text_edit.toHtml()
        plain_content = text_edit.toPlainText()
        
        print("✅ HTML 渲染測試成功")
        print(f"   - HTML 長度: {len(html_content)} 字符")
        print(f"   - 純文字長度: {len(plain_content)} 字符")
        print(f"   - 包含樣式: {'style' in html_content.lower()}")
        
        # 檢查關鍵內容
        key_elements = ["Swisse Vitamin C", "Description", "Ingredients", "How to Use"]
        found_elements = [elem for elem in key_elements if elem in plain_content]
        print(f"   - 找到關鍵元素: {len(found_elements)}/{len(key_elements)}")
        
        return True
        
    except Exception as e:
        print(f"❌ HTML 渲染測試失敗: {e}")
        return False


def test_html_generator():
    """測試 HTML 生成器的預覽功能"""
    print("\n🔍 測試 HTML 生成器預覽...")
    
    try:
        from core.html_generator import HTMLGenerator
        
        generator = HTMLGenerator()
        test_html = create_test_html()
        
        # 生成預覽 HTML
        preview_html = generator.generate_preview_html(test_html)
        
        # 檢查預覽 HTML
        checks = [
            "<!DOCTYPE html>" in preview_html,
            "Microsoft YaHei" in preview_html,
            "container" in preview_html,
            "Swisse Vitamin C" in preview_html,
            "preview-header" in preview_html,
            "style>" in preview_html
        ]
        
        if all(checks):
            print("✅ HTML 生成器預覽功能正常")
            
            # 儲存測試檔案
            test_file = project_root / "test_rendered_preview.html"
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(preview_html)
            print(f"   - 預覽檔案已儲存: {test_file}")
            print("   - 您可以在瀏覽器中開啟此檔案查看渲染效果")
            
            return True
        else:
            print("❌ HTML 生成器預覽功能有問題")
            print(f"   - 檢查結果: {checks}")
            return False
            
    except Exception as e:
        print(f"❌ HTML 生成器測試失敗: {e}")
        return False


def create_comparison_demo():
    """建立對比示例"""
    print("\n🔍 建立 HTML 渲染對比示例...")
    
    try:
        test_html = create_test_html()
        
        # 建立對比 HTML 檔案
        comparison_html = f"""
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML 渲染對比示例</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .comparison {{
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }}
        .panel {{
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .panel h2 {{
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }}
        .code {{
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }}
        .rendered {{
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
        }}
        .rendered h1 {{
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        .rendered h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            background-color: #f8f9fa;
            padding-top: 10px;
            padding-bottom: 10px;
        }}
        .rendered h3 {{
            color: #5a6c7d;
            border-bottom: 1px solid #ecf0f1;
            padding-bottom: 5px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 HTML 預覽功能對比示例</h1>
        <p>這個示例展示了 HTML 原始碼和渲染效果的對比，幫助您理解 AI 商品描述編輯器的預覽功能。</p>
        
        <div class="comparison">
            <div class="panel">
                <h2>📝 HTML 原始碼</h2>
                <div class="code">{test_html.replace('<', '&lt;').replace('>', '&gt;')}</div>
            </div>
            
            <div class="panel">
                <h2>🎨 渲染效果</h2>
                <div class="rendered">
                    {test_html}
                </div>
            </div>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2>💡 使用說明</h2>
            <ul>
                <li><strong>左側</strong>：顯示 AI 生成的 HTML 原始碼</li>
                <li><strong>右側</strong>：顯示在瀏覽器中的實際渲染效果</li>
                <li><strong>在程式中</strong>：「HTML 預覽」標籤頁顯示渲染效果，「HTML 原始碼」標籤頁顯示代碼</li>
                <li><strong>點擊表格</strong>：可以切換查看不同產品的描述</li>
            </ul>
        </div>
    </div>
</body>
</html>
"""
        
        demo_file = project_root / "html_preview_demo.html"
        with open(demo_file, 'w', encoding='utf-8') as f:
            f.write(comparison_html)
        
        print(f"✅ 對比示例已建立: {demo_file}")
        print("   - 在瀏覽器中開啟此檔案可查看完整的對比效果")
        
        return True
        
    except Exception as e:
        print(f"❌ 建立對比示例失敗: {e}")
        return False


def main():
    """主測試函數"""
    print("🖥️ HTML 預覽功能測試")
    print("=" * 50)
    
    tests = [
        ("HTML 渲染功能", test_html_rendering),
        ("HTML 生成器預覽", test_html_generator),
        ("對比示例建立", create_comparison_demo),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 HTML 預覽測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 HTML 預覽功能測試通過！")
        print("\n📋 修復內容:")
        print("1. ✅ HTML 預覽現在顯示渲染效果，而不是原始碼")
        print("2. ✅ 新增「HTML 原始碼」標籤頁供查看代碼")
        print("3. ✅ 改善了 QTextEdit 的 HTML 渲染設定")
        print("4. ✅ 提供完整的樣式和格式化")
        print("\n🚀 現在啟動程式測試: python main.py")
        print("   - 「HTML 預覽」標籤頁：查看美觀的渲染效果")
        print("   - 「HTML 原始碼」標籤頁：查看 AI 生成的代碼")
    else:
        print("⚠️ 部分 HTML 預覽功能測試失敗。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
