#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cost Calculator Module
成本計算模組 - 計算 AI API 使用成本
"""

from typing import Dict, List, Any
from dataclasses import dataclass
from datetime import datetime


@dataclass
class APIUsage:
    """API 使用記錄"""
    timestamp: datetime
    model: str
    input_tokens: int
    output_tokens: int
    total_tokens: int
    estimated_cost: float


class CostCalculator:
    """成本計算器"""
    
    def __init__(self):
        self.usage_records: List[APIUsage] = []
        
        # AI 模型定價 (USD per 1K tokens) - 2025年1月價格
        self.pricing = {
            # OpenAI GPT-4o-mini (推薦 - 經濟實惠)
            'openai': {
                'input': 0.00015,   # $0.15 per 1M tokens
                'output': 0.0006,   # $0.60 per 1M tokens
                'display_name': 'GPT-4o-mini'
            },
            
            # Anthropic Claude-3.5-Sonnet (最新)
            'anthropic': {
                'input': 0.003,     # $3.00 per 1M tokens
                'output': 0.015,    # $15.00 per 1M tokens
                'display_name': 'Claude-3.5-Sonnet'
            },
            
            # Google Gemini-Pro (免費額度)
            'google': {
                'input': 0.00125,   # $1.25 per 1M tokens (付費後)
                'output': 0.00375,  # $3.75 per 1M tokens (付費後)
                'display_name': 'Gemini-Pro',
                'free_tier': True,
                'free_limit': 1000000  # 每月免費 1M tokens
            }
        }
    
    def calculate_cost(self, model: str, input_tokens: int, output_tokens: int) -> float:
        """
        計算單次 API 呼叫成本
        
        Args:
            model: AI 模型名稱
            input_tokens: 輸入 token 數量
            output_tokens: 輸出 token 數量
            
        Returns:
            float: 預估成本 (USD)
        """
        if model not in self.pricing:
            return 0.0
        
        pricing = self.pricing[model]
        
        # 計算成本 (轉換為每 1K tokens)
        input_cost = (input_tokens / 1000) * pricing['input']
        output_cost = (output_tokens / 1000) * pricing['output']
        
        total_cost = input_cost + output_cost
        
        # Google Gemini 免費額度處理
        if model == 'google' and pricing.get('free_tier'):
            monthly_usage = self.get_monthly_usage(model)
            if monthly_usage + input_tokens + output_tokens <= pricing['free_limit']:
                return 0.0  # 在免費額度內
        
        return total_cost
    
    def record_usage(self, model: str, input_tokens: int, output_tokens: int) -> float:
        """
        記錄 API 使用並計算成本
        
        Args:
            model: AI 模型名稱
            input_tokens: 輸入 token 數量
            output_tokens: 輸出 token 數量
            
        Returns:
            float: 本次使用的預估成本
        """
        total_tokens = input_tokens + output_tokens
        cost = self.calculate_cost(model, input_tokens, output_tokens)
        
        usage = APIUsage(
            timestamp=datetime.now(),
            model=model,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=total_tokens,
            estimated_cost=cost
        )
        
        self.usage_records.append(usage)
        return cost
    
    def get_usage_summary(self) -> Dict[str, Any]:
        """
        取得使用統計摘要
        
        Returns:
            Dict[str, Any]: 使用統計
        """
        if not self.usage_records:
            return {
                'total_requests': 0,
                'total_tokens': 0,
                'total_cost': 0.0,
                'by_model': {}
            }
        
        summary = {
            'total_requests': len(self.usage_records),
            'total_tokens': sum(r.total_tokens for r in self.usage_records),
            'total_cost': sum(r.estimated_cost for r in self.usage_records),
            'by_model': {}
        }
        
        # 按模型統計
        for record in self.usage_records:
            model = record.model
            if model not in summary['by_model']:
                summary['by_model'][model] = {
                    'requests': 0,
                    'input_tokens': 0,
                    'output_tokens': 0,
                    'total_tokens': 0,
                    'cost': 0.0,
                    'display_name': self.pricing.get(model, {}).get('display_name', model)
                }
            
            model_stats = summary['by_model'][model]
            model_stats['requests'] += 1
            model_stats['input_tokens'] += record.input_tokens
            model_stats['output_tokens'] += record.output_tokens
            model_stats['total_tokens'] += record.total_tokens
            model_stats['cost'] += record.estimated_cost
        
        return summary
    
    def get_monthly_usage(self, model: str) -> int:
        """
        取得指定模型的當月使用量
        
        Args:
            model: AI 模型名稱
            
        Returns:
            int: 當月總 token 使用量
        """
        current_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        monthly_tokens = 0
        for record in self.usage_records:
            if record.model == model and record.timestamp >= current_month:
                monthly_tokens += record.total_tokens
        
        return monthly_tokens
    
    def get_cost_breakdown(self) -> List[Dict[str, Any]]:
        """
        取得詳細的成本分解
        
        Returns:
            List[Dict[str, Any]]: 每個模型的詳細成本資訊
        """
        summary = self.get_usage_summary()
        breakdown = []
        
        for model, stats in summary['by_model'].items():
            pricing = self.pricing.get(model, {})
            
            item = {
                'model': model,
                'display_name': stats['display_name'],
                'requests': stats['requests'],
                'input_tokens': stats['input_tokens'],
                'output_tokens': stats['output_tokens'],
                'total_tokens': stats['total_tokens'],
                'cost': stats['cost'],
                'input_rate': pricing.get('input', 0) * 1000,  # 轉換為每 1K tokens
                'output_rate': pricing.get('output', 0) * 1000,
                'is_free_tier': pricing.get('free_tier', False)
            }
            
            # Google Gemini 免費額度資訊
            if model == 'google' and pricing.get('free_tier'):
                monthly_usage = self.get_monthly_usage(model)
                free_limit = pricing.get('free_limit', 0)
                item['monthly_usage'] = monthly_usage
                item['free_limit'] = free_limit
                item['free_remaining'] = max(0, free_limit - monthly_usage)
                item['is_within_free_tier'] = monthly_usage <= free_limit
            
            breakdown.append(item)
        
        # 按成本排序
        breakdown.sort(key=lambda x: x['cost'], reverse=True)
        return breakdown
    
    def get_recommendations(self) -> List[str]:
        """
        取得成本優化建議
        
        Returns:
            List[str]: 建議列表
        """
        recommendations = []
        summary = self.get_usage_summary()
        
        if not summary['by_model']:
            return ["開始使用 AI 模型後將顯示成本優化建議"]
        
        # 分析使用模式
        total_cost = summary['total_cost']
        
        if total_cost > 10.0:  # 超過 $10
            recommendations.append("💡 考慮使用更經濟的 GPT-4o-mini 模型來降低成本")
        
        # 檢查 Google Gemini 免費額度
        if 'google' in summary['by_model']:
            google_stats = summary['by_model']['google']
            monthly_usage = self.get_monthly_usage('google')
            free_limit = self.pricing['google'].get('free_limit', 0)
            
            if monthly_usage < free_limit * 0.8:  # 使用量低於 80%
                recommendations.append("🆓 Google Gemini 仍有免費額度，可優先使用")
            elif monthly_usage >= free_limit:
                recommendations.append("⚠️ Google Gemini 免費額度已用完，將開始計費")
        
        # 模型使用建議
        if len(summary['by_model']) > 1:
            cheapest_model = min(summary['by_model'].items(), key=lambda x: x[1]['cost'])
            most_expensive = max(summary['by_model'].items(), key=lambda x: x[1]['cost'])
            
            if cheapest_model[1]['cost'] * 2 < most_expensive[1]['cost']:
                recommendations.append(
                    f"💰 {cheapest_model[1]['display_name']} 比 {most_expensive[1]['display_name']} 更經濟"
                )
        
        if not recommendations:
            recommendations.append("✅ 目前使用模式良好，成本控制得當")
        
        return recommendations
    
    def reset_statistics(self):
        """重置所有統計資料"""
        self.usage_records.clear()
    
    def export_usage_data(self) -> List[Dict[str, Any]]:
        """
        匯出使用資料
        
        Returns:
            List[Dict[str, Any]]: 使用記錄列表
        """
        return [
            {
                'timestamp': record.timestamp.isoformat(),
                'model': record.model,
                'display_name': self.pricing.get(record.model, {}).get('display_name', record.model),
                'input_tokens': record.input_tokens,
                'output_tokens': record.output_tokens,
                'total_tokens': record.total_tokens,
                'estimated_cost': record.estimated_cost
            }
            for record in self.usage_records
        ]
