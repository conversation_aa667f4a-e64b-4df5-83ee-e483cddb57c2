#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Anthropic Model Fix
測試 Anthropic 模型修復
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_anthropic_model_fix():
    """測試 Anthropic 模型修復"""
    print("🔍 測試 Anthropic 模型配置修復...")
    
    try:
        from config.settings import load_config
        from core.ai_models import AIModelManager
        from core.cost_calculator import CostCalculator
        
        # 載入配置
        config = load_config()
        print("✅ 配置載入成功")
        
        # 檢查 Anthropic 配置
        anthropic_config = config.get('ai_models', {}).get('anthropic', {})
        model_name = anthropic_config.get('model', '')
        
        print(f"📋 Anthropic 模型配置:")
        print(f"   - 模型名稱: {model_name}")
        print(f"   - API Key 已設定: {'是' if anthropic_config.get('api_key') else '否'}")
        
        # 檢查模型名稱是否已更新
        if model_name == 'claude-3-5-sonnet-20241022':
            print("✅ Anthropic 模型名稱已正確更新")
        elif model_name == 'claude-3-sonnet-20240229':
            print("❌ Anthropic 模型名稱仍是舊版本")
            return False
        else:
            print(f"⚠️ Anthropic 模型名稱: {model_name}")
        
        # 測試 AI 模型管理器
        cost_calculator = CostCalculator()
        ai_manager = AIModelManager(config, cost_calculator)
        print("✅ AI 模型管理器初始化成功")
        
        # 檢查可用模型
        available_models = ai_manager.get_available_models()
        print(f"📊 可用模型: {available_models}")
        
        # 如果有 Anthropic API key，測試模型
        if 'anthropic' in available_models and anthropic_config.get('api_key'):
            print("🧪 測試 Anthropic 模型連接...")
            try:
                # 簡單測試（不實際呼叫 API）
                anthropic_model = ai_manager.models.get('anthropic')
                if anthropic_model:
                    print(f"✅ Anthropic 模型已載入: {anthropic_model.model_name}")
                else:
                    print("❌ Anthropic 模型未載入")
            except Exception as e:
                print(f"⚠️ Anthropic 模型測試警告: {e}")
        
        print("\n🎉 Anthropic 模型修復測試完成！")
        print("\n📋 修復摘要:")
        print("✅ 更新模型名稱: claude-3-sonnet-20240229 → claude-3-5-sonnet-20241022")
        print("✅ 配置文件已更新: config/settings.py")
        print("✅ AI 模型管理器已更新: core/ai_models.py")
        
        print("\n🚀 建議的使用方式:")
        print("1. 重新啟動程式: python main.py")
        print("2. 選擇 AI 模型時，Anthropic 現在應該可以正常工作")
        print("3. 如果仍有問題，請檢查 Anthropic API key 是否有效")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_model_recommendations():
    """顯示模型推薦"""
    print("\n💡 AI 模型使用建議:")
    print("\n🥇 **推薦配置** (經濟實惠):")
    print("   Writer AI: OpenAI GPT-4o-mini")
    print("   Reviewer AI: OpenAI GPT-4o-mini")
    print("   成本: ~$0.0005 per product")
    
    print("\n🥈 **免費測試配置**:")
    print("   Writer AI: Google Gemini-Pro")
    print("   Reviewer AI: Google Gemini-Pro") 
    print("   成本: $0.00 (免費額度內)")
    
    print("\n🥉 **高品質配置** (現在可用):")
    print("   Writer AI: Anthropic Claude-3.5-Sonnet")
    print("   Reviewer AI: Anthropic Claude-3.5-Sonnet")
    print("   成本: ~$0.005 per product")
    
    print("\n🎯 **混合配置** (平衡):")
    print("   Writer AI: OpenAI GPT-4o-mini (快速生成)")
    print("   Reviewer AI: Anthropic Claude-3.5-Sonnet (高品質評估)")
    print("   成本: ~$0.003 per product")

def main():
    """主測試函數"""
    print("🚀 Anthropic 模型修復測試")
    print("=" * 50)
    
    success = test_anthropic_model_fix()
    show_model_recommendations()
    
    if success:
        print("\n✅ 修復成功！Anthropic 模型現在應該可以正常使用。")
    else:
        print("\n❌ 修復過程中遇到問題，請檢查錯誤訊息。")
    
    return success

if __name__ == "__main__":
    main()
