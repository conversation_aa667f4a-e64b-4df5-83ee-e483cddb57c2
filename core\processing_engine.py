#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Processing Engine Module
處理引擎模組 - 實作雙重 AI 處理流程（Writer + Reviewer）
"""

import time
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from loguru import logger

from .data_processor import ExcelProcessor, ImageProcessor
from .ai_models import AIModelManager
from .prompt_manager import PromptManager
from .keyword_manager import KeywordManager
from .html_generator import HTMLGenerator


@dataclass
class ProcessingResult:
    """處理結果資料類別"""
    success: bool
    row_index: int
    product_name: str
    html_output: str
    used_keywords: List[str]
    review_notes: str
    processing_time: float
    error_message: str = ""
    tokens_used: int = 0


class ProcessingEngine:
    """處理引擎 - 協調所有組件進行雙重處理"""
    
    def __init__(
        self,
        excel_processor: ExcelProcessor,
        ai_manager: AIModelManager,
        prompt_manager: <PERSON>mpt<PERSON><PERSON><PERSON>,
        keyword_manager: KeywordManager,
        html_generator: HTMLGenerator,
        image_processor: Optional[ImageProcessor] = None
    ):
        self.excel_processor = excel_processor
        self.ai_manager = ai_manager
        self.prompt_manager = prompt_manager
        self.keyword_manager = keyword_manager
        self.html_generator = html_generator
        self.image_processor = image_processor
        
        self.processing_stats = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "total_tokens": 0,
            "total_time": 0.0
        }
    
    def process_single_item(
        self,
        row_index: int,
        writer_ai: str,
        writer_prompt: str,
        reviewer_prompt: str,
        category: str = None,
        max_keywords: int = 5,
        enable_images: bool = False,
        reviewer_ai: str = None
    ) -> ProcessingResult:
        """
        處理單一項目
        
        Args:
            row_index: 資料行索引
            ai_model: AI 模型名稱
            writer_prompt: Writer Prompt 名稱
            reviewer_prompt: Reviewer Prompt 名稱
            category: 產品分類
            max_keywords: 最大關鍵字數量
            enable_images: 是否啟用圖片
            
        Returns:
            ProcessingResult: 處理結果
        """
        start_time = time.time()
        
        try:
            # 取得產品資料
            product_data = self.excel_processor.get_processing_data(row_index)
            if not product_data:
                return ProcessingResult(
                    success=False,
                    row_index=row_index,
                    product_name="未知產品",
                    html_output="",
                    used_keywords=[],
                    review_notes="",
                    processing_time=0,
                    error_message="無法取得產品資料"
                )
            
            # 取得產品名稱（用於顯示）
            product_name = self._extract_product_name(product_data)
            
            # 取得關鍵字
            keywords = self.keyword_manager.get_keywords_for_product(
                product_data, category, max_keywords
            )
            
            # 取得圖片（如果啟用）
            images = []
            if enable_images and self.image_processor:
                image_filename = self.excel_processor.get_image_filename(row_index)
                if image_filename:
                    images = self.image_processor.find_images(image_filename)
            
            # 第一階段：Writer 處理
            # 處理模型鍵值映射
            writer_model_key = self._get_model_key(writer_ai)
            writer_result = self._run_writer_stage(
                product_data, keywords, images, writer_model_key, writer_prompt
            )
            
            if not writer_result["success"]:
                return ProcessingResult(
                    success=False,
                    row_index=row_index,
                    product_name=product_name,
                    html_output="",
                    used_keywords=keywords,
                    review_notes="",
                    processing_time=time.time() - start_time,
                    error_message=f"Writer 階段失敗: {writer_result['error']}"
                )
            
            html_output = writer_result["content"]
            
            # 清理和驗證 HTML
            html_output = self.html_generator.clean_html_content(html_output)
            
            # 第二階段：Reviewer 處理
            reviewer_ai_model = reviewer_ai or writer_ai
            reviewer_model_key = self._get_model_key(reviewer_ai_model)
            reviewer_result = self._run_reviewer_stage(
                html_output, product_data, reviewer_model_key, reviewer_prompt, writer_prompt
            )
            
            review_notes = "✅ 未進行審查"
            if reviewer_result["success"]:
                review_notes = reviewer_result["content"]
            else:
                logger.warning(f"Reviewer 階段失敗: {reviewer_result['error']}")
            
            # 計算總 token 使用量
            total_tokens = writer_result.get("tokens_used", 0) + reviewer_result.get("tokens_used", 0)
            
            # 更新統計
            self.processing_stats["total_processed"] += 1
            self.processing_stats["successful"] += 1
            self.processing_stats["total_tokens"] += total_tokens
            
            processing_time = time.time() - start_time
            self.processing_stats["total_time"] += processing_time
            
            return ProcessingResult(
                success=True,
                row_index=row_index,
                product_name=product_name,
                html_output=html_output,
                used_keywords=keywords,
                review_notes=review_notes,
                processing_time=processing_time,
                tokens_used=total_tokens
            )
            
        except Exception as e:
            logger.error(f"處理項目失敗 (行 {row_index}): {e}")
            
            self.processing_stats["total_processed"] += 1
            self.processing_stats["failed"] += 1
            
            return ProcessingResult(
                success=False,
                row_index=row_index,
                product_name=self._extract_product_name(product_data) if 'product_data' in locals() else "未知產品",
                html_output="",
                used_keywords=[],
                review_notes="",
                processing_time=time.time() - start_time,
                error_message=str(e)
            )
    
    def _extract_product_name(self, product_data: Dict[str, Any]) -> str:
        """從產品資料中提取產品名稱"""
        # 常見的產品名稱欄位
        name_fields = ['name', 'title', 'product_name', 'product_title', '產品名稱', '名稱', '標題']

        for field in name_fields:
            for key, value in product_data.items():
                if field.lower() in key.lower() and value and str(value).strip():
                    return str(value)[:50]  # 限制長度

        # 如果沒有找到產品名稱，嘗試從其他欄位生成
        return self._generate_product_name(product_data)

    def _generate_product_name(self, product_data: Dict[str, Any]) -> str:
        """AI 生成產品名稱"""
        try:
            # 收集有用的資訊
            useful_info = []

            # 品牌資訊
            brand_fields = ['brand', 'manufacturer', '品牌', '製造商']
            brand = None
            for field in brand_fields:
                for key, value in product_data.items():
                    if field.lower() in key.lower() and value and str(value).strip():
                        brand = str(value)
                        break
                if brand:
                    break

            # 分類資訊
            category_fields = ['category', 'type', '分類', '類型']
            category = None
            for field in category_fields:
                for key, value in product_data.items():
                    if field.lower() in key.lower() and value and str(value).strip():
                        category = str(value)
                        break
                if category:
                    break

            # 描述資訊
            desc_fields = ['description', 'desc', '描述', '說明']
            description = None
            for field in desc_fields:
                for key, value in product_data.items():
                    if field.lower() in key.lower() and value and str(value).strip():
                        description = str(value)[:100]  # 限制長度
                        break
                if description:
                    break

            # 構建簡單的產品名稱
            name_parts = []
            if brand:
                name_parts.append(brand)
            if category:
                name_parts.append(category)

            if name_parts:
                generated_name = " ".join(name_parts)
                logger.info(f"AI 生成產品名稱: {generated_name}")
                return generated_name[:50]

            # 如果還是沒有，使用第一個非空值
            for key, value in product_data.items():
                if value and str(value).strip():
                    return f"{str(value)[:30]}..."

            return "未知產品"

        except Exception as e:
            logger.error(f"生成產品名稱失敗: {e}")
            return "未知產品"
    
    def _run_writer_stage(
        self,
        product_data: Dict[str, Any],
        keywords: List[str],
        images: List[str],
        ai_model: str,
        writer_prompt: str
    ) -> Dict[str, Any]:
        """執行 Writer 階段"""
        try:
            # 格式化 Prompt
            formatted_prompt = self.prompt_manager.format_writer_prompt(
                writer_prompt, product_data, keywords
            )
            
            if not formatted_prompt:
                return {
                    "success": False,
                    "error": f"Writer Prompt '{writer_prompt}' 不存在",
                    "content": "",
                    "tokens_used": 0
                }
            
            # 呼叫 AI 模型
            result = self.ai_manager.generate_text(ai_model, formatted_prompt, images)
            
            return result
            
        except Exception as e:
            logger.error(f"Writer 階段執行失敗: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "tokens_used": 0
            }
    
    def _run_reviewer_stage(
        self,
        html_content: str,
        product_data: Dict[str, Any],
        ai_model: str,
        reviewer_prompt: str,
        writer_prompt: str = None
    ) -> Dict[str, Any]:
        """執行 Reviewer 階段"""
        try:
            # 格式化 Prompt
            formatted_prompt = self.prompt_manager.format_reviewer_prompt(
                reviewer_prompt, html_content, product_data, writer_prompt
            )
            
            if not formatted_prompt:
                return {
                    "success": False,
                    "error": f"Reviewer Prompt '{reviewer_prompt}' 不存在",
                    "content": "",
                    "tokens_used": 0
                }
            
            # 呼叫 AI 模型
            result = self.ai_manager.generate_text(ai_model, formatted_prompt)
            
            return result
            
        except Exception as e:
            logger.error(f"Reviewer 階段執行失敗: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "tokens_used": 0
            }
    
    def process_batch(
        self,
        start_row: int,
        end_row: int,
        writer_ai: str,
        writer_prompt: str,
        reviewer_prompt: str,
        category: str = None,
        max_keywords: int = 5,
        enable_images: bool = False,
        progress_callback: Optional[Callable[[int, int], None]] = None,
        reviewer_ai: str = None
    ) -> List[ProcessingResult]:
        """
        批次處理多個項目
        
        Args:
            start_row: 起始行
            end_row: 結束行
            ai_model: AI 模型名稱
            writer_prompt: Writer Prompt 名稱
            reviewer_prompt: Reviewer Prompt 名稱
            category: 產品分類
            max_keywords: 最大關鍵字數量
            enable_images: 是否啟用圖片
            progress_callback: 進度回調函數
            
        Returns:
            List[ProcessingResult]: 處理結果列表
        """
        results = []
        total_items = end_row - start_row + 1
        reviewer_ai_model = reviewer_ai or writer_ai  # 如果沒有指定 reviewer AI，使用 writer AI

        logger.info(f"開始批次處理: 行 {start_row} 到 {end_row} (共 {total_items} 項)")
        logger.info(f"Writer AI: {writer_ai}, Reviewer AI: {reviewer_ai_model}")

        for i, row_index in enumerate(range(start_row, end_row + 1)):
            logger.info(f"處理項目 {i + 1}/{total_items} (行 {row_index})")

            result = self.process_single_item(
                row_index, writer_ai, writer_prompt, reviewer_prompt,
                category, max_keywords, enable_images, reviewer_ai_model
            )
            
            results.append(result)
            
            # 呼叫進度回調
            if progress_callback:
                progress_callback(i + 1, total_items)
            
            # 記錄處理結果
            if result.success:
                logger.info(f"項目處理成功: {result.product_name} ({result.processing_time:.2f}s)")
            else:
                logger.error(f"項目處理失敗: {result.error_message}")
        
        logger.info(f"批次處理完成: 成功 {self.processing_stats['successful']}, 失敗 {self.processing_stats['failed']}")
        
        return results
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """取得處理統計資訊"""
        return self.processing_stats.copy()
    
    def reset_stats(self):
        """重設統計資訊"""
        self.processing_stats = {
            "total_processed": 0,
            "successful": 0,
            "failed": 0,
            "total_tokens": 0,
            "total_time": 0.0
        }

    def _get_model_key(self, model_display_name: str) -> str:
        """
        將顯示名稱轉換為模型鍵值

        Args:
            model_display_name: GUI 中顯示的模型名稱

        Returns:
            str: 對應的模型鍵值
        """
        # 模型顯示名稱到鍵值的映射 (更新版本，按長度排序避免部分匹配問題)
        model_mapping = [
            # OpenAI 系列（先匹配較長的名稱）
            ('GPT-4o mini', 'openai-gpt4o-mini'),
            ('GPT-4o', 'openai-gpt4o'),

            # Anthropic 系列
            ('Claude Opus 4', 'anthropic-opus4'),
            ('Claude Sonnet 4', 'anthropic-sonnet4'),

            # Google 系列
            ('Gemini 2.5 Pro', 'google-pro25'),
            ('Gemini 2.5 Flash', 'google-flash25')
        ]

        # 嘗試按順序匹配（避免部分匹配問題）
        for model_name, model_key in model_mapping:
            if model_name in model_display_name:
                return model_key

        # 如果沒有找到映射，嘗試舊的格式
        if 'openai' in model_display_name.lower():
            return 'openai-gpt4o'  # 預設為 GPT-4o
        elif 'anthropic' in model_display_name.lower() or 'claude' in model_display_name.lower():
            return 'anthropic-sonnet4'  # 預設為 Sonnet 4
        elif 'google' in model_display_name.lower() or 'gemini' in model_display_name.lower():
            return 'google-pro25'  # 預設為 Pro 2.5

        # 最後的備用方案
        return model_display_name
