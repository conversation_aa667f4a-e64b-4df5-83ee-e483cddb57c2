#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Settings Manager
設定管理器
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger


class SettingsManager:
    """設定管理器"""
    
    def __init__(self, settings_file: str = "user_settings.json"):
        self.settings_file = Path(settings_file)
        self.default_settings = {
            "last_excel_file": "",
            "last_image_dir": "",
            "last_seo_dir": "",
            "last_sheet": "",
            "writer_ai_model": "",
            "reviewer_ai_model": "",
            "writer_prompt": "",
            "reviewer_prompt": "",
            "excluded_columns": [],
            "image_column": "",
            "enable_images": False,
            "start_row": 1,
            "end_row": 10000,
            "language": "zh_TW",  # zh_TW, en_US
            "window_geometry": {
                "x": 100,
                "y": 100,
                "width": 1400,
                "height": 900
            },
            "splitter_sizes": [400, 1000],
            "api_keys": {
                "openai": "",
                "anthropic": "",
                "google": ""
            }
        }
        self.settings = self.load_settings()
    
    def load_settings(self) -> Dict[str, Any]:
        """載入設定"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                
                # 合併預設設定和載入的設定
                settings = self.default_settings.copy()
                settings.update(loaded_settings)
                
                logger.info(f"設定已載入: {self.settings_file}")
                return settings
            else:
                logger.info("使用預設設定")
                return self.default_settings.copy()
                
        except Exception as e:
            logger.error(f"載入設定失敗: {e}")
            return self.default_settings.copy()
    
    def save_settings(self) -> bool:
        """儲存設定"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            
            logger.info(f"設定已儲存: {self.settings_file}")
            return True
            
        except Exception as e:
            logger.error(f"儲存設定失敗: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """取得設定值"""
        keys = key.split('.')
        value = self.settings
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """設定值"""
        keys = key.split('.')
        target = self.settings
        
        # 導航到目標位置
        for k in keys[:-1]:
            if k not in target:
                target[k] = {}
            target = target[k]
        
        # 設定值
        target[keys[-1]] = value
    
    def update_file_settings(self, excel_file: str = None, image_dir: str = None, 
                           seo_dir: str = None, sheet: str = None):
        """更新檔案相關設定"""
        if excel_file:
            self.set("last_excel_file", excel_file)
        if image_dir:
            self.set("last_image_dir", image_dir)
        if seo_dir:
            self.set("last_seo_dir", seo_dir)
        if sheet:
            self.set("last_sheet", sheet)
        self.save_settings()
    
    def update_ai_settings(self, writer_ai: str = None, reviewer_ai: str = None,
                          writer_prompt: str = None, reviewer_prompt: str = None):
        """更新 AI 相關設定"""
        if writer_ai:
            self.set("writer_ai_model", writer_ai)
        if reviewer_ai:
            self.set("reviewer_ai_model", reviewer_ai)
        if writer_prompt:
            self.set("writer_prompt", writer_prompt)
        if reviewer_prompt:
            self.set("reviewer_prompt", reviewer_prompt)
        self.save_settings()
    
    def update_column_settings(self, excluded_columns: list = None, 
                             image_column: str = None, enable_images: bool = None):
        """更新欄位相關設定"""
        if excluded_columns is not None:
            self.set("excluded_columns", excluded_columns)
        if image_column is not None:
            self.set("image_column", image_column)
        if enable_images is not None:
            self.set("enable_images", enable_images)
        self.save_settings()
    
    def update_range_settings(self, start_row: int = None, end_row: int = None):
        """更新範圍設定"""
        if start_row is not None:
            self.set("start_row", start_row)
        if end_row is not None:
            self.set("end_row", end_row)
        self.save_settings()
    
    def update_window_settings(self, geometry: Dict[str, int] = None, 
                             splitter_sizes: list = None):
        """更新視窗設定"""
        if geometry:
            self.set("window_geometry", geometry)
        if splitter_sizes:
            self.set("splitter_sizes", splitter_sizes)
        self.save_settings()
    
    def update_api_key(self, provider: str, api_key: str):
        """更新 API 金鑰"""
        self.set(f"api_keys.{provider}", api_key)
        self.save_settings()
    
    def get_api_key(self, provider: str) -> str:
        """取得 API 金鑰"""
        return self.get(f"api_keys.{provider}", "")
    
    def set_language(self, language: str):
        """設定語言"""
        self.set("language", language)
        self.save_settings()
    
    def get_language(self) -> str:
        """取得語言設定"""
        return self.get("language", "zh_TW")
    
    def reset_to_defaults(self):
        """重置為預設設定"""
        self.settings = self.default_settings.copy()
        self.save_settings()
        logger.info("設定已重置為預設值")
