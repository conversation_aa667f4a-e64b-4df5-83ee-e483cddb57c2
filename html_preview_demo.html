
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML 渲染對比示例</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .comparison {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .panel {
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .panel h2 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .rendered {
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
        }
        .rendered h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        .rendered h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            background-color: #f8f9fa;
            padding-top: 10px;
            padding-bottom: 10px;
        }
        .rendered h3 {
            color: #5a6c7d;
            border-bottom: 1px solid #ecf0f1;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 HTML 預覽功能對比示例</h1>
        <p>這個示例展示了 HTML 原始碼和渲染效果的對比，幫助您理解 AI 商品描述編輯器的預覽功能。</p>
        
        <div class="comparison">
            <div class="panel">
                <h2>📝 HTML 原始碼</h2>
                <div class="code">
&lt;h1&gt;Swisse Vitamin C 1000mg&lt;/h1&gt;

&lt;h2&gt;Description&lt;/h2&gt;
&lt;h3&gt;Product Overview&lt;/h3&gt;
&lt;p&gt;Swisse Vitamin C 是一款&lt;strong&gt;高效維生素C補充劑&lt;/strong&gt;，來自澳洲知名品牌 Swisse。每粒含有 1000mg 的維生素C，搭配生物類黃酮和玫瑰果萃取，提供全方位的&lt;em&gt;免疫支持&lt;/em&gt;。&lt;/p&gt;

&lt;h3&gt;Main Benefits&lt;/h3&gt;
&lt;ul&gt;
    &lt;li&gt;&lt;strong&gt;增強免疫系統&lt;/strong&gt; - 提升身體自然防禦能力&lt;/li&gt;
    &lt;li&gt;&lt;strong&gt;強效抗氧化&lt;/strong&gt; - 對抗自由基傷害&lt;/li&gt;
    &lt;li&gt;&lt;strong&gt;促進膠原蛋白合成&lt;/strong&gt; - 維持肌膚健康&lt;/li&gt;
    &lt;li&gt;&lt;strong&gt;支持鐵質吸收&lt;/strong&gt; - 改善營養吸收效率&lt;/li&gt;
&lt;/ul&gt;

&lt;h2&gt;Ingredients&lt;/h2&gt;
&lt;h3&gt;Active Ingredients&lt;/h3&gt;
&lt;p&gt;每粒含有：&lt;/p&gt;
&lt;ul&gt;
    &lt;li&gt;維生素C (抗壞血酸) - 1000mg&lt;/li&gt;
    &lt;li&gt;生物類黃酮 - 25mg&lt;/li&gt;
    &lt;li&gt;玫瑰果萃取 - 5mg&lt;/li&gt;
&lt;/ul&gt;

&lt;h3&gt;Free From / Allergy Info&lt;/h3&gt;
&lt;p&gt;本產品&lt;strong&gt;不含&lt;/strong&gt;：人工色素、人工香料、防腐劑。適合素食者使用。如對任何成分過敏，請諮詢醫師。&lt;/p&gt;

&lt;h2&gt;How to Use&lt;/h2&gt;
&lt;h3&gt;Dosage&lt;/h3&gt;
&lt;p&gt;&lt;strong&gt;建議用量&lt;/strong&gt;：成人每日1粒，餐後服用，或遵照醫師指示。&lt;/p&gt;

&lt;h3&gt;Usage Warnings&lt;/h3&gt;
&lt;p&gt;&lt;em&gt;注意事項&lt;/em&gt;：&lt;/p&gt;
&lt;ul&gt;
    &lt;li&gt;孕婦、哺乳期婦女使用前請諮詢醫師&lt;/li&gt;
    &lt;li&gt;請存放於陰涼乾燥處，避免陽光直射&lt;/li&gt;
    &lt;li&gt;請勿超過建議劑量&lt;/li&gt;
    &lt;li&gt;如有不適請停止使用並諮詢醫師&lt;/li&gt;
&lt;/ul&gt;

&lt;h2&gt;Additional Information&lt;/h2&gt;
&lt;h3&gt;Miscellaneous&lt;/h3&gt;
&lt;p&gt;&lt;strong&gt;產地&lt;/strong&gt;：澳洲&lt;br&gt;
&lt;strong&gt;包裝&lt;/strong&gt;：60粒/瓶&lt;br&gt;
&lt;strong&gt;保存期限&lt;/strong&gt;：請參考包裝標示&lt;br&gt;
&lt;strong&gt;認證&lt;/strong&gt;：TGA認證，品質保證&lt;/p&gt;
</div>
            </div>
            
            <div class="panel">
                <h2>🎨 渲染效果</h2>
                <div class="rendered">
                    
<h1>Swisse Vitamin C 1000mg</h1>

<h2>Description</h2>
<h3>Product Overview</h3>
<p>Swisse Vitamin C 是一款<strong>高效維生素C補充劑</strong>，來自澳洲知名品牌 Swisse。每粒含有 1000mg 的維生素C，搭配生物類黃酮和玫瑰果萃取，提供全方位的<em>免疫支持</em>。</p>

<h3>Main Benefits</h3>
<ul>
    <li><strong>增強免疫系統</strong> - 提升身體自然防禦能力</li>
    <li><strong>強效抗氧化</strong> - 對抗自由基傷害</li>
    <li><strong>促進膠原蛋白合成</strong> - 維持肌膚健康</li>
    <li><strong>支持鐵質吸收</strong> - 改善營養吸收效率</li>
</ul>

<h2>Ingredients</h2>
<h3>Active Ingredients</h3>
<p>每粒含有：</p>
<ul>
    <li>維生素C (抗壞血酸) - 1000mg</li>
    <li>生物類黃酮 - 25mg</li>
    <li>玫瑰果萃取 - 5mg</li>
</ul>

<h3>Free From / Allergy Info</h3>
<p>本產品<strong>不含</strong>：人工色素、人工香料、防腐劑。適合素食者使用。如對任何成分過敏，請諮詢醫師。</p>

<h2>How to Use</h2>
<h3>Dosage</h3>
<p><strong>建議用量</strong>：成人每日1粒，餐後服用，或遵照醫師指示。</p>

<h3>Usage Warnings</h3>
<p><em>注意事項</em>：</p>
<ul>
    <li>孕婦、哺乳期婦女使用前請諮詢醫師</li>
    <li>請存放於陰涼乾燥處，避免陽光直射</li>
    <li>請勿超過建議劑量</li>
    <li>如有不適請停止使用並諮詢醫師</li>
</ul>

<h2>Additional Information</h2>
<h3>Miscellaneous</h3>
<p><strong>產地</strong>：澳洲<br>
<strong>包裝</strong>：60粒/瓶<br>
<strong>保存期限</strong>：請參考包裝標示<br>
<strong>認證</strong>：TGA認證，品質保證</p>

                </div>
            </div>
        </div>
        
        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2>💡 使用說明</h2>
            <ul>
                <li><strong>左側</strong>：顯示 AI 生成的 HTML 原始碼</li>
                <li><strong>右側</strong>：顯示在瀏覽器中的實際渲染效果</li>
                <li><strong>在程式中</strong>：「HTML 預覽」標籤頁顯示渲染效果，「HTML 原始碼」標籤頁顯示代碼</li>
                <li><strong>點擊表格</strong>：可以切換查看不同產品的描述</li>
            </ul>
        </div>
    </div>
</body>
</html>
