#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Review Engine Module
審查引擎模組 - 實作 Review-Only Redo 功能
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger

from .data_processor import ExcelProcessor
from .ai_models import AIModelManager
from .prompt_manager import PromptManager
from .html_generator import HTMLGenerator
from .processing_engine import ProcessingResult


class ReviewEngine:
    """審查引擎 - 專門處理已完成檔案的重新審查"""
    
    def __init__(
        self,
        excel_processor: ExcelProcessor,
        ai_manager: AIModelManager,
        prompt_manager: PromptManager,
        html_generator: HTMLGenerator
    ):
        self.excel_processor = excel_processor
        self.ai_manager = ai_manager
        self.prompt_manager = prompt_manager
        self.html_generator = html_generator
        
        # 審查狀態標記
        self.review_indicators = {
            "needs_improvement": ["⚠️", "🛠️", "❌"],
            "passed": ["✅"],
            "warning_keywords": ["需", "補", "修", "改", "錯", "問題", "缺", "不符"]
        }
    
    def identify_review_items(self, review_column: str = "Review Notes") -> List[int]:
        """
        識別需要重新處理的項目
        
        Args:
            review_column: 審查欄位名稱
            
        Returns:
            List[int]: 需要重新處理的行索引列表
        """
        if not self.excel_processor or self.excel_processor.data is None:
            logger.error("沒有載入 Excel 資料")
            return []
        
        if review_column not in self.excel_processor.data.columns:
            logger.error(f"找不到審查欄位: {review_column}")
            return []
        
        needs_review = []
        
        for index, row in self.excel_processor.data.iterrows():
            review_note = str(row[review_column]) if row[review_column] else ""
            
            if self._needs_improvement(review_note):
                needs_review.append(index)
                logger.debug(f"行 {index + 1} 需要重新處理: {review_note}")
        
        logger.info(f"識別出 {len(needs_review)} 個需要重新處理的項目")
        return needs_review
    
    def _needs_improvement(self, review_note: str) -> bool:
        """
        判斷審查備註是否表示需要改善
        
        Args:
            review_note: 審查備註
            
        Returns:
            bool: 是否需要改善
        """
        if not review_note:
            return False
        
        # 檢查改善指標
        for indicator in self.review_indicators["needs_improvement"]:
            if indicator in review_note:
                return True
        
        # 檢查通過指標
        for indicator in self.review_indicators["passed"]:
            if indicator in review_note:
                return False
        
        # 檢查警告關鍵字
        for keyword in self.review_indicators["warning_keywords"]:
            if keyword in review_note:
                return True
        
        return False
    
    def re_process_item(
        self,
        row_index: int,
        ai_model: str,
        writer_prompt: str,
        reviewer_prompt: str,
        original_html_column: str = "HTML Output",
        original_review_column: str = "Review Notes"
    ) -> ProcessingResult:
        """
        重新處理單一項目
        
        Args:
            row_index: 行索引
            ai_model: AI 模型名稱
            writer_prompt: Writer Prompt 名稱
            reviewer_prompt: Reviewer Prompt 名稱
            original_html_column: 原始 HTML 欄位名稱
            original_review_column: 原始審查欄位名稱
            
        Returns:
            ProcessingResult: 處理結果
        """
        try:
            # 取得原始資料
            product_data = self.excel_processor.get_processing_data(row_index)
            original_html = ""
            original_review = ""
            
            if original_html_column in self.excel_processor.data.columns:
                original_html = str(self.excel_processor.data.iloc[row_index][original_html_column])
            
            if original_review_column in self.excel_processor.data.columns:
                original_review = str(self.excel_processor.data.iloc[row_index][original_review_column])
            
            # 建立改善版的 Writer Prompt
            enhanced_prompt = self._create_enhanced_prompt(
                writer_prompt, product_data, original_html, original_review
            )
            
            # 執行 Writer 階段
            writer_result = self.ai_manager.generate_text(ai_model, enhanced_prompt)
            
            if not writer_result["success"]:
                return ProcessingResult(
                    success=False,
                    row_index=row_index,
                    product_name=self._extract_product_name(product_data),
                    html_output="",
                    used_keywords=[],
                    review_notes="",
                    processing_time=0,
                    error_message=f"Writer 階段失敗: {writer_result['error']}"
                )
            
            # 清理 HTML
            html_output = self.html_generator.clean_html_content(writer_result["content"])
            
            # 執行 Reviewer 階段
            reviewer_prompt_formatted = self.prompt_manager.format_reviewer_prompt(
                reviewer_prompt, html_output, product_data
            )
            
            reviewer_result = self.ai_manager.generate_text(ai_model, reviewer_prompt_formatted)
            
            review_notes = "✅ 重新處理完成"
            if reviewer_result["success"]:
                review_notes = reviewer_result["content"]
            
            return ProcessingResult(
                success=True,
                row_index=row_index,
                product_name=self._extract_product_name(product_data),
                html_output=html_output,
                used_keywords=[],  # Review-Only 模式不重新選擇關鍵字
                review_notes=review_notes,
                processing_time=0,
                tokens_used=writer_result.get("tokens_used", 0) + reviewer_result.get("tokens_used", 0)
            )
            
        except Exception as e:
            logger.error(f"重新處理項目失敗 (行 {row_index}): {e}")
            return ProcessingResult(
                success=False,
                row_index=row_index,
                product_name="未知產品",
                html_output="",
                used_keywords=[],
                review_notes="",
                processing_time=0,
                error_message=str(e)
            )
    
    def _create_enhanced_prompt(
        self,
        writer_prompt: str,
        product_data: Dict[str, Any],
        original_html: str,
        original_review: str
    ) -> str:
        """
        建立增強版的 Writer Prompt
        
        Args:
            writer_prompt: 原始 Writer Prompt 名稱
            product_data: 產品資料
            original_html: 原始 HTML 輸出
            original_review: 原始審查備註
            
        Returns:
            str: 增強版 Prompt
        """
        base_prompt = self.prompt_manager.get_writer_prompt(writer_prompt)
        if not base_prompt:
            return ""
        
        # 格式化產品資料
        product_data_str = self._format_product_data(product_data)
        
        # 建立改善指導
        improvement_guidance = self._extract_improvement_guidance(original_review)
        
        # 建立增強版 Prompt
        enhanced_prompt = f"""{base_prompt}

## 重要：這是重新處理任務
以下是之前的輸出和審查意見，請根據審查意見進行改善：

### 之前的 HTML 輸出：
{original_html}

### 審查意見：
{original_review}

### 改善指導：
{improvement_guidance}

### 產品資料：
{product_data_str}

請根據審查意見重新生成改善後的 HTML 描述。"""
        
        return enhanced_prompt
    
    def _extract_improvement_guidance(self, review_note: str) -> str:
        """
        從審查備註中提取改善指導
        
        Args:
            review_note: 審查備註
            
        Returns:
            str: 改善指導
        """
        if not review_note:
            return "請改善整體內容品質"
        
        guidance = []
        
        # 常見問題對應的改善建議
        improvement_map = {
            "劑量": "請補充詳細的劑量資訊和使用方法",
            "成分": "請詳細說明產品成分和功效",
            "結構": "請確保 HTML 結構符合要求的格式",
            "內容": "請豐富產品描述內容",
            "關鍵字": "請自然地融入更多相關關鍵字",
            "效益": "請強調產品的主要效益和優勢",
            "警告": "請加入必要的使用警告和注意事項",
            "過敏": "請補充過敏資訊和禁忌說明"
        }
        
        for keyword, suggestion in improvement_map.items():
            if keyword in review_note:
                guidance.append(suggestion)
        
        if not guidance:
            guidance.append("請根據審查意見改善內容品質和完整性")
        
        return "\n".join(f"- {g}" for g in guidance)
    
    def _format_product_data(self, product_data: Dict[str, Any]) -> str:
        """格式化產品資料"""
        if not product_data:
            return "無產品資料"
        
        formatted_lines = []
        for key, value in product_data.items():
            if value and str(value).strip():
                formatted_lines.append(f"{key}: {value}")
        
        return "\n".join(formatted_lines) if formatted_lines else "無有效產品資料"
    
    def _extract_product_name(self, product_data: Dict[str, Any]) -> str:
        """從產品資料中提取產品名稱"""
        name_fields = ['name', 'title', 'product_name', 'product_title', '產品名稱', '名稱', '標題']
        
        for field in name_fields:
            for key, value in product_data.items():
                if field.lower() in key.lower() and value:
                    return str(value)[:50]
        
        for key, value in product_data.items():
            if value and str(value).strip():
                return str(value)[:50]
        
        return "未知產品"
    
    def batch_re_process(
        self,
        row_indices: List[int],
        ai_model: str,
        writer_prompt: str,
        reviewer_prompt: str,
        progress_callback: Optional[callable] = None
    ) -> List[ProcessingResult]:
        """
        批次重新處理多個項目
        
        Args:
            row_indices: 行索引列表
            ai_model: AI 模型名稱
            writer_prompt: Writer Prompt 名稱
            reviewer_prompt: Reviewer Prompt 名稱
            progress_callback: 進度回調函數
            
        Returns:
            List[ProcessingResult]: 處理結果列表
        """
        results = []
        total_items = len(row_indices)
        
        logger.info(f"開始批次重新處理 {total_items} 個項目")
        
        for i, row_index in enumerate(row_indices):
            logger.info(f"重新處理項目 {i + 1}/{total_items} (行 {row_index + 1})")
            
            result = self.re_process_item(
                row_index, ai_model, writer_prompt, reviewer_prompt
            )
            
            results.append(result)
            
            if progress_callback:
                progress_callback(i + 1, total_items)
            
            if result.success:
                logger.info(f"項目重新處理成功: {result.product_name}")
            else:
                logger.error(f"項目重新處理失敗: {result.error_message}")
        
        logger.info(f"批次重新處理完成")
        return results
    
    def update_excel_with_review_results(self, results: List[ProcessingResult]):
        """
        將重新處理結果更新到 Excel
        
        Args:
            results: 處理結果列表
        """
        if not self.excel_processor:
            return
        
        # 準備新欄位資料
        review_v2_notes = [""] * len(self.excel_processor.data)
        
        for result in results:
            if result.row_index < len(review_v2_notes):
                review_v2_notes[result.row_index] = result.review_notes
                
                # 如果重新處理成功，也更新 HTML 輸出
                if result.success and result.html_output:
                    if "HTML Output" in self.excel_processor.data.columns:
                        self.excel_processor.data.iloc[result.row_index, 
                            self.excel_processor.data.columns.get_loc("HTML Output")] = result.html_output
        
        # 新增 Review Notes (v2) 欄位
        self.excel_processor.add_output_column("Review Notes (v2)", review_v2_notes)
        
        logger.info(f"已更新 {len(results)} 個項目的重新處理結果")
