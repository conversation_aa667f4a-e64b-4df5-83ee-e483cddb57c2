#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Models Integration Module
AI 模型整合模組 - 支援多種 AI 模型的統一介面
"""

import os
import time
import base64
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from loguru import logger

try:
    import openai
except ImportError:
    openai = None

try:
    import anthropic
except ImportError:
    anthropic = None

try:
    import google.generativeai as genai
except ImportError:
    genai = None


class AIModelBase(ABC):
    """AI 模型基礎類別"""
    
    def __init__(self, api_key: str, model_name: str, **kwargs):
        self.api_key = api_key
        self.model_name = model_name
        self.config = kwargs
        self.setup_client()
    
    @abstractmethod
    def setup_client(self):
        """設定 API 客戶端"""
        pass
    
    @abstractmethod
    def generate_text(self, prompt: str, images: List[str] = None) -> Dict[str, Any]:
        """
        生成文字
        
        Args:
            prompt: 提示文字
            images: 圖片路徑列表（可選）
            
        Returns:
            Dict[str, Any]: 生成結果
        """
        pass
    
    def encode_image(self, image_path: str) -> str:
        """將圖片編碼為 base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"圖片編碼失敗 {image_path}: {e}")
            return ""


class OpenAIModel(AIModelBase):
    """OpenAI GPT 模型"""
    
    def setup_client(self):
        if openai is None:
            raise ImportError("請安裝 openai 套件: pip install openai")
        
        self.client = openai.OpenAI(api_key=self.api_key)
    
    def generate_text(self, prompt: str, images: List[str] = None) -> Dict[str, Any]:
        try:
            messages = [{"role": "user", "content": prompt}]
            
            # 如果有圖片，使用 GPT-4 Vision
            if images and len(images) > 0:
                content = [{"type": "text", "text": prompt}]
                
                for image_path in images[:4]:  # 最多 4 張圖片
                    if os.path.exists(image_path):
                        base64_image = self.encode_image(image_path)
                        if base64_image:
                            content.append({
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            })
                
                messages = [{"role": "user", "content": content}]
                model = "gpt-4-vision-preview"
            else:
                model = self.model_name
            
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=self.config.get('max_tokens', 2000),
                temperature=self.config.get('temperature', 0.7)
            )
            
            # 記錄 token 使用量到成本計算器
            input_tokens = response.usage.prompt_tokens if response.usage else 0
            output_tokens = response.usage.completion_tokens if response.usage else 0
            total_tokens = response.usage.total_tokens if response.usage else 0

            cost = 0
            if self.cost_calculator:
                cost = self.cost_calculator.record_usage('openai', input_tokens, output_tokens)
                logger.info(f"OpenAI 使用記錄: {input_tokens} input + {output_tokens} output = {total_tokens} tokens, 成本: ${cost:.6f}")

            return {
                "success": True,
                "content": response.choices[0].message.content,
                "tokens_used": total_tokens,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "cost": cost,
                "model": model
            }
            
        except Exception as e:
            logger.error(f"OpenAI API 呼叫失敗: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "tokens_used": 0
            }


class AnthropicModel(AIModelBase):
    """Anthropic Claude 模型"""
    
    def setup_client(self):
        if anthropic is None:
            raise ImportError("請安裝 anthropic 套件: pip install anthropic")
        
        self.client = anthropic.Anthropic(api_key=self.api_key)
    
    def generate_text(self, prompt: str, images: List[str] = None) -> Dict[str, Any]:
        try:
            content = [{"type": "text", "text": prompt}]
            
            # 如果有圖片，加入圖片內容
            if images and len(images) > 0:
                for image_path in images[:5]:  # Claude 支援多張圖片
                    if os.path.exists(image_path):
                        base64_image = self.encode_image(image_path)
                        if base64_image:
                            # 判斷圖片格式
                            image_format = Path(image_path).suffix.lower()
                            if image_format == '.png':
                                media_type = "image/png"
                            elif image_format in ['.jpg', '.jpeg']:
                                media_type = "image/jpeg"
                            elif image_format == '.webp':
                                media_type = "image/webp"
                            else:
                                media_type = "image/jpeg"  # 預設
                            
                            content.append({
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": media_type,
                                    "data": base64_image
                                }
                            })
            
            response = self.client.messages.create(
                model=self.model_name,
                max_tokens=self.config.get('max_tokens', 2000),
                temperature=self.config.get('temperature', 0.7),
                messages=[{"role": "user", "content": content}]
            )
            
            # 記錄 token 使用量到成本計算器
            input_tokens = response.usage.input_tokens if response.usage else 0
            output_tokens = response.usage.output_tokens if response.usage else 0
            total_tokens = input_tokens + output_tokens

            cost = 0
            if hasattr(self, 'cost_calculator') and self.cost_calculator:
                cost = self.cost_calculator.record_usage('anthropic', input_tokens, output_tokens)
                logger.info(f"Anthropic 使用記錄: {input_tokens} input + {output_tokens} output = {total_tokens} tokens, 成本: ${cost:.6f}")

            return {
                "success": True,
                "content": response.content[0].text,
                "tokens_used": total_tokens,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "cost": cost,
                "model": self.model_name
            }
            
        except Exception as e:
            logger.error(f"Anthropic API 呼叫失敗: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "tokens_used": 0
            }


class GoogleModel(AIModelBase):
    """Google Gemini 模型"""
    
    def setup_client(self):
        if genai is None:
            raise ImportError("請安裝 google-generativeai 套件: pip install google-generativeai")
        
        genai.configure(api_key=self.api_key)
        self.client = genai.GenerativeModel(self.model_name)
    
    def generate_text(self, prompt: str, images: List[str] = None) -> Dict[str, Any]:
        try:
            content = [prompt]
            
            # 如果有圖片，加入圖片內容
            if images and len(images) > 0:
                from PIL import Image
                
                for image_path in images[:10]:  # Gemini 支援多張圖片
                    if os.path.exists(image_path):
                        try:
                            img = Image.open(image_path)
                            content.append(img)
                        except Exception as e:
                            logger.warning(f"無法載入圖片 {image_path}: {e}")
            
            response = self.client.generate_content(
                content,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=self.config.get('max_tokens', 2000),
                    temperature=self.config.get('temperature', 0.7)
                )
            )
            
            # 記錄 token 使用量到成本計算器
            total_tokens = response.usage_metadata.total_token_count if hasattr(response, 'usage_metadata') else 0
            input_tokens = response.usage_metadata.prompt_token_count if hasattr(response, 'usage_metadata') else 0
            output_tokens = response.usage_metadata.candidates_token_count if hasattr(response, 'usage_metadata') else 0

            cost = 0
            if hasattr(self, 'cost_calculator') and self.cost_calculator:
                cost = self.cost_calculator.record_usage('google', input_tokens, output_tokens)
                logger.info(f"Google 使用記錄: {input_tokens} input + {output_tokens} output = {total_tokens} tokens, 成本: ${cost:.6f}")

            return {
                "success": True,
                "content": response.text,
                "tokens_used": total_tokens,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "cost": cost,
                "model": self.model_name
            }
            
        except Exception as e:
            logger.error(f"Google API 呼叫失敗: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "tokens_used": 0
            }


class AIModelManager:
    """AI 模型管理器"""

    def __init__(self, config: Dict[str, Any], cost_calculator=None):
        self.config = config
        self.models = {}
        self.cost_calculator = cost_calculator
        self.setup_models()

    def setup_models(self):
        """設定所有可用的 AI 模型"""
        ai_config = self.config.get('ai_models', {})

        # OpenAI
        openai_config = ai_config.get('openai', {})
        if openai_config.get('api_key'):
            try:
                openai_model = OpenAIModel(
                    api_key=openai_config['api_key'],
                    model_name=openai_config.get('model', 'gpt-4'),
                    max_tokens=openai_config.get('max_tokens', 2000),
                    temperature=openai_config.get('temperature', 0.7)
                )
                openai_model.cost_calculator = self.cost_calculator
                self.models['openai'] = openai_model
                logger.info("OpenAI 模型已設定")
            except Exception as e:
                logger.warning(f"OpenAI 模型設定失敗: {e}")

        # Anthropic
        anthropic_config = ai_config.get('anthropic', {})
        if anthropic_config.get('api_key'):
            try:
                anthropic_model = AnthropicModel(
                    api_key=anthropic_config['api_key'],
                    model_name=anthropic_config.get('model', 'claude-3-sonnet-20240229'),
                    max_tokens=anthropic_config.get('max_tokens', 2000),
                    temperature=anthropic_config.get('temperature', 0.7)
                )
                anthropic_model.cost_calculator = self.cost_calculator
                self.models['anthropic'] = anthropic_model
                logger.info("Anthropic 模型已設定")
            except Exception as e:
                logger.warning(f"Anthropic 模型設定失敗: {e}")

        # Google
        google_config = ai_config.get('google', {})
        if google_config.get('api_key'):
            try:
                google_model = GoogleModel(
                    api_key=google_config['api_key'],
                    model_name=google_config.get('model', 'gemini-pro'),
                    max_tokens=google_config.get('max_tokens', 2000),
                    temperature=google_config.get('temperature', 0.7)
                )
                google_model.cost_calculator = self.cost_calculator
                self.models['google'] = google_model
                logger.info("Google 模型已設定")
            except Exception as e:
                logger.warning(f"Google 模型設定失敗: {e}")

    def get_available_models(self) -> List[str]:
        """取得可用的模型列表"""
        return list(self.models.keys())

    def generate_text(self, model_name: str, prompt: str, images: List[str] = None) -> Dict[str, Any]:
        """
        使用指定模型生成文字

        Args:
            model_name: 模型名稱
            prompt: 提示文字
            images: 圖片路徑列表

        Returns:
            Dict[str, Any]: 生成結果
        """
        if model_name not in self.models:
            return {
                "success": False,
                "error": f"模型 '{model_name}' 不可用",
                "content": "",
                "tokens_used": 0
            }

        model = self.models[model_name]

        # 記錄 API 呼叫
        start_time = time.time()
        result = model.generate_text(prompt, images)
        end_time = time.time()

        # 記錄使用統計
        if result.get('success'):
            from utils.logger import log_api_usage
            log_api_usage(
                model=f"{model_name}:{result.get('model', 'unknown')}",
                tokens_used=result.get('tokens_used', 0),
                cost=0.0  # 可以根據模型計算實際費用
            )

            logger.info(
                f"AI 生成完成 - 模型: {model_name}, "
                f"Token: {result.get('tokens_used', 0)}, "
                f"時間: {end_time - start_time:.2f}s"
            )
        else:
            logger.error(f"AI 生成失敗 - 模型: {model_name}, 錯誤: {result.get('error', 'Unknown')}")

        return result
