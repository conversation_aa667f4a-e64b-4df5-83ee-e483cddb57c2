#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prompt Management Module
Prompt 管理模組 - 處理 Writer 和 Reviewer Prompt 的載入與管理
"""

import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from loguru import logger


class PromptManager:
    """Prompt 管理器"""
    
    def __init__(self, prompts_dir: str):
        self.prompts_dir = Path(prompts_dir)
        self.writer_prompts = {}
        self.reviewer_prompts = {}
        self.load_prompts()
    
    def load_prompts(self):
        """載入所有 Prompt 檔案"""
        self.load_writer_prompts()
        self.load_reviewer_prompts()
    
    def load_writer_prompts(self):
        """載入 Writer Prompt"""
        writer_dir = self.prompts_dir / "writer"
        if not writer_dir.exists():
            logger.warning(f"Writer prompts 目錄不存在: {writer_dir}")
            return
        
        for prompt_file in writer_dir.glob("*.txt"):
            try:
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                prompt_name = prompt_file.stem
                self.writer_prompts[prompt_name] = content
                logger.debug(f"載入 Writer Prompt: {prompt_name}")
                
            except Exception as e:
                logger.error(f"載入 Writer Prompt 失敗 {prompt_file}: {e}")
        
        logger.info(f"載入 {len(self.writer_prompts)} 個 Writer Prompt")
    
    def load_reviewer_prompts(self):
        """載入 Reviewer Prompt"""
        reviewer_dir = self.prompts_dir / "reviewer"
        if not reviewer_dir.exists():
            logger.warning(f"Reviewer prompts 目錄不存在: {reviewer_dir}")
            return
        
        for prompt_file in reviewer_dir.glob("*.txt"):
            try:
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                prompt_name = prompt_file.stem
                self.reviewer_prompts[prompt_name] = content
                logger.debug(f"載入 Reviewer Prompt: {prompt_name}")
                
            except Exception as e:
                logger.error(f"載入 Reviewer Prompt 失敗 {prompt_file}: {e}")
        
        logger.info(f"載入 {len(self.reviewer_prompts)} 個 Reviewer Prompt")
    
    def get_writer_prompts(self) -> List[str]:
        """取得所有 Writer Prompt 名稱"""
        return list(self.writer_prompts.keys())
    
    def get_reviewer_prompts(self) -> List[str]:
        """取得所有 Reviewer Prompt 名稱"""
        return list(self.reviewer_prompts.keys())
    
    def get_writer_prompt(self, name: str) -> Optional[str]:
        """
        取得指定的 Writer Prompt
        
        Args:
            name: Prompt 名稱
            
        Returns:
            Optional[str]: Prompt 內容，如果不存在則返回 None
        """
        return self.writer_prompts.get(name)
    
    def get_reviewer_prompt(self, name: str) -> Optional[str]:
        """
        取得指定的 Reviewer Prompt
        
        Args:
            name: Prompt 名稱
            
        Returns:
            Optional[str]: Prompt 內容，如果不存在則返回 None
        """
        return self.reviewer_prompts.get(name)
    
    def format_writer_prompt(self, prompt_name: str, product_data: Dict[str, Any], keywords: List[str]) -> str:
        """
        格式化 Writer Prompt
        
        Args:
            prompt_name: Prompt 名稱
            product_data: 產品資料
            keywords: 關鍵字列表
            
        Returns:
            str: 格式化後的 Prompt
        """
        prompt_template = self.get_writer_prompt(prompt_name)
        if not prompt_template:
            logger.error(f"Writer Prompt '{prompt_name}' 不存在")
            return ""
        
        # 格式化產品資料
        product_data_str = self._format_product_data(product_data)
        
        # 格式化關鍵字
        keywords_str = ", ".join(keywords) if keywords else "無特定關鍵字"
        
        # 替換模板變數
        formatted_prompt = prompt_template.format(
            product_data=product_data_str,
            keywords=keywords_str
        )
        
        return formatted_prompt
    
    def format_reviewer_prompt(self, prompt_name: str, html_description: str, product_data: Dict[str, Any], writer_prompt: str = None) -> str:
        """
        格式化 Reviewer Prompt

        Args:
            prompt_name: Prompt 名稱
            html_description: HTML 描述內容
            product_data: 原始產品資料
            writer_prompt: Writer Prompt 名稱（用於確定 Writer Profile）

        Returns:
            str: 格式化後的 Prompt
        """
        prompt_template = self.get_reviewer_prompt(prompt_name)
        if not prompt_template:
            logger.error(f"Reviewer Prompt '{prompt_name}' 不存在")
            return ""

        # 格式化產品資料
        product_data_str = self._format_product_data(product_data)

        # 確定 Writer Profile
        expected_writer_profile = writer_prompt or "default"

        # 替換模板變數
        try:
            formatted_prompt = prompt_template.format(
                html_description=html_description,
                product_data=product_data_str,
                expected_writer_profile=expected_writer_profile
            )
        except KeyError as e:
            # 如果模板中沒有 expected_writer_profile 參數，使用舊格式
            logger.warning(f"Reviewer Prompt 模板缺少參數 {e}，使用舊格式")
            formatted_prompt = prompt_template.format(
                html_description=html_description,
                product_data=product_data_str
            )

        return formatted_prompt
    
    def _format_product_data(self, product_data: Dict[str, Any]) -> str:
        """
        格式化產品資料為可讀的字串
        
        Args:
            product_data: 產品資料字典
            
        Returns:
            str: 格式化後的產品資料
        """
        if not product_data:
            return "無產品資料"
        
        formatted_lines = []
        for key, value in product_data.items():
            if value and str(value).strip():
                formatted_lines.append(f"{key}: {value}")
        
        return "\n".join(formatted_lines) if formatted_lines else "無有效產品資料"
    
    def add_writer_prompt(self, name: str, content: str) -> bool:
        """
        新增 Writer Prompt
        
        Args:
            name: Prompt 名稱
            content: Prompt 內容
            
        Returns:
            bool: 是否成功
        """
        try:
            writer_dir = self.prompts_dir / "writer"
            writer_dir.mkdir(exist_ok=True)
            
            prompt_file = writer_dir / f"{name}.txt"
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.writer_prompts[name] = content
            logger.info(f"新增 Writer Prompt: {name}")
            return True
            
        except Exception as e:
            logger.error(f"新增 Writer Prompt 失敗: {e}")
            return False
    
    def add_reviewer_prompt(self, name: str, content: str) -> bool:
        """
        新增 Reviewer Prompt
        
        Args:
            name: Prompt 名稱
            content: Prompt 內容
            
        Returns:
            bool: 是否成功
        """
        try:
            reviewer_dir = self.prompts_dir / "reviewer"
            reviewer_dir.mkdir(exist_ok=True)
            
            prompt_file = reviewer_dir / f"{name}.txt"
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.reviewer_prompts[name] = content
            logger.info(f"新增 Reviewer Prompt: {name}")
            return True
            
        except Exception as e:
            logger.error(f"新增 Reviewer Prompt 失敗: {e}")
            return False
    
    def reload_prompts(self):
        """重新載入所有 Prompt"""
        self.writer_prompts.clear()
        self.reviewer_prompts.clear()
        self.load_prompts()
        logger.info("Prompt 重新載入完成")
