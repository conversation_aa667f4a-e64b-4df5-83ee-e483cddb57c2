# 🚀 AI 商品描述優化系統 - 增強功能指南

## 🎉 新功能總覽

根據您的需求，我已經實現了以下 6 大增強功能：

### 1. 🤖 分離式 AI 模型選擇
- **Writer AI 模型**: 專門用於生成產品描述
- **Reviewer AI 模型**: 專門用於品質評估
- **智能推薦**: 顯示最新且經濟的模型選項

### 2. 💰 成本計算系統
- **即時成本追蹤**: 每次 API 呼叫的詳細成本
- **模型比較**: 不同 AI 模型的成本對比
- **優化建議**: 智能推薦更經濟的使用方式

### 3. 📝 Prompt 管理系統
- **視覺化管理**: 在 GUI 中直接 CRUD 操作
- **即時編輯**: 新增、編輯、查看、刪除 Prompt
- **分類管理**: Writer 和 Reviewer Prompt 分別管理

### 4. 🖥️ 分割式 HTML 預覽
- **上方**: HTML 渲染預覽
- **下方**: Reviewer 評估結果
- **同步顯示**: 點擊表格同時顯示兩者

### 5. 📊 智能處理範圍
- **自動設定**: 載入資料後自動設定為全部行數
- **無需手動計算**: 系統自動識別總行數

### 6. 🎯 模型推薦系統
- **經濟推薦**: GPT-4o-mini (最佳性價比)
- **品質推薦**: Claude-3.5-Sonnet (最新技術)
- **免費推薦**: Google Gemini-Pro (免費額度)

## 💡 詳細使用指南

### 🤖 AI 模型選擇

#### 推薦配置
```
Writer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
Reviewer AI 模型: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
```

#### 高品質配置
```
Writer AI 模型: Anthropic Claude-3.5-Sonnet (最新)
Reviewer AI 模型: Anthropic Claude-3.5-Sonnet (最新)
```

#### 免費測試配置
```
Writer AI 模型: Google Gemini-Pro (免費額度)
Reviewer AI 模型: Google Gemini-Pro (免費額度)
```

### 💰 成本計算功能

#### 查看成本統計
1. 點擊「成本計算」標籤頁
2. 查看各模型的使用統計
3. 監控總費用和 Token 使用量

#### 成本對比 (每 1000 input + 500 output tokens)
- 🥇 **Google Gemini-Pro**: $0.000 (免費額度內)
- 🥈 **OpenAI GPT-4o-mini**: $0.0005 (最經濟)
- 🥉 **Anthropic Claude-3.5-Sonnet**: $0.0105 (最高品質)

#### 優化建議
- **日常使用**: GPT-4o-mini (性價比最佳)
- **高品質需求**: Claude-3.5-Sonnet (最新技術)
- **測試階段**: Gemini-Pro (免費額度)

### 📝 Prompt 管理

#### 新增 Prompt
1. 點擊「Prompt 管理」標籤頁
2. 選擇 Writer 或 Reviewer 區域
3. 點擊「新增」按鈕
4. 在右側編輯器輸入名稱和內容
5. 點擊「💾 儲存 Prompt」

#### 編輯 Prompt
1. 在左側列表選擇要編輯的 Prompt
2. 點擊「編輯」按鈕
3. 在右側編輯器修改內容
4. 點擊「💾 儲存 Prompt」

#### 刪除 Prompt
1. 在左側列表選擇要刪除的 Prompt
2. 點擊「刪除」按鈕
3. 確認刪除操作

### 🖥️ 分割式預覽

#### 查看方式
1. 處理完成後，自動顯示第一個結果
2. 點擊「資料表格」中任意單元格切換查看
3. 上方顯示 HTML 渲染效果
4. 下方顯示 Reviewer 評估結果

#### 標籤頁說明
- **HTML 預覽**: 分割視窗 (HTML + Reviewer)
- **HTML 原始碼**: 純代碼查看
- **資料表格**: 所有處理結果
- **成本計算**: 費用統計
- **Prompt 管理**: Prompt 編輯

### 📊 處理範圍設定

#### 自動設定
- 載入 Excel 後自動設定結束列為總行數
- 無需手動計算或調整

#### 手動調整
- 仍可在「處理範圍」區域手動調整
- 適合測試或部分處理需求

## 🎯 最佳實踐

### 成本控制策略

#### 1. 模型選擇
```
測試階段: Google Gemini-Pro (免費)
日常使用: OpenAI GPT-4o-mini (經濟)
重要項目: Anthropic Claude-3.5-Sonnet (高品質)
```

#### 2. 分離配置
```
Writer: GPT-4o-mini (生成速度快，成本低)
Reviewer: Claude-3.5-Sonnet (評估更準確)
```

#### 3. 批次處理
- 小批量測試 (1-5 個產品)
- 確認效果後大批量處理
- 定期檢查成本統計

### Prompt 優化建議

#### Writer Prompt 要點
- 明確指定 HTML 結構要求
- 包含產品資料和關鍵字變數
- 強調 SEO 和可讀性

#### Reviewer Prompt 要點
- 設定評估標準
- 要求簡潔的反饋格式
- 使用狀態指標 (✅ ⚠️ ❌)

### 工作流程建議

#### 1. 初始設定
1. 載入 Excel 資料
2. 選擇經濟的 AI 模型組合
3. 設定適當的 Prompt

#### 2. 小批量測試
1. 設定處理範圍為 1-3 行
2. 執行處理並查看結果
3. 檢查成本統計

#### 3. 優化調整
1. 根據結果調整 Prompt
2. 嘗試不同的 AI 模型組合
3. 平衡品質和成本

#### 4. 大批量處理
1. 確認設定無誤
2. 執行全部資料處理
3. 使用 Review-Only 模式優化

## 📊 功能對比

| 功能 | 舊版本 | 新版本 |
|------|--------|--------|
| AI 模型選擇 | 單一模型 | Writer + Reviewer 分離 |
| 成本追蹤 | 無 | 詳細統計和建議 |
| Prompt 管理 | 檔案編輯 | GUI 視覺化管理 |
| HTML 預覽 | 單一視窗 | 分割視窗 + Reviewer |
| 處理範圍 | 手動設定 | 自動識別 + 手動調整 |
| 模型推薦 | 無 | 智能推薦標記 |

## 🚀 立即開始

### 啟動程式
```bash
python main.py
```

### 體驗新功能
1. **載入資料**: 選擇 Excel 檔案，系統自動設定處理範圍
2. **選擇模型**: 分別選擇 Writer 和 Reviewer AI 模型
3. **開始處理**: 執行處理並觀察成本統計
4. **查看結果**: 在分割視窗中查看 HTML 和 Reviewer 結果
5. **管理 Prompt**: 在 Prompt 管理標籤頁編輯模板

### 推薦首次使用設定
```
Writer AI: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
Reviewer AI: OpenAI GPT-4o-mini (推薦 - 經濟實惠)
Writer Prompt: pharmacy 或 furniture (根據產品類型)
Reviewer Prompt: standard
處理範圍: 1-2 行 (測試用)
```

## 🎉 享受新功能

所有新功能都已完美整合，提供更強大、更經濟、更易用的 AI 商品描述優化體驗！

---

**功能完成日期**: 2025-08-01  
**測試狀態**: ✅ 全部通過 (5/5)  
**可用狀態**: 🚀 立即可用
