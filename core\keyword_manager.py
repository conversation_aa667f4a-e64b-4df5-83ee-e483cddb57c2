#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keyword Management Module
關鍵字管理模組 - 處理 SEO 關鍵字的載入、分類和選擇
"""

import os
import random
from pathlib import Path
from typing import Dict, List, Set, Optional
from loguru import logger


class KeywordManager:
    """關鍵字管理器"""
    
    def __init__(self, categories_dir: str):
        self.categories_dir = Path(categories_dir)
        self.general_keywords = set()
        self.category_keywords = {}
        self.load_keywords()
    
    def load_keywords(self):
        """載入所有關鍵字檔案"""
        self.load_general_keywords()
        self.load_category_keywords()
    
    def load_general_keywords(self):
        """載入通用關鍵字"""
        general_file = self.categories_dir / "_general" / "keywords.txt"
        
        if not general_file.exists():
            logger.warning(f"通用關鍵字檔案不存在: {general_file}")
            return
        
        try:
            with open(general_file, 'r', encoding='utf-8') as f:
                keywords = [line.strip() for line in f if line.strip()]
            
            self.general_keywords = set(keywords)
            logger.info(f"載入 {len(self.general_keywords)} 個通用關鍵字")
            
        except Exception as e:
            logger.error(f"載入通用關鍵字失敗: {e}")
    
    def load_category_keywords(self):
        """載入分類關鍵字"""
        if not self.categories_dir.exists():
            logger.warning(f"分類目錄不存在: {self.categories_dir}")
            return
        
        for category_dir in self.categories_dir.iterdir():
            if not category_dir.is_dir() or category_dir.name.startswith('_'):
                continue
            
            keywords_file = category_dir / "keywords.txt"
            if not keywords_file.exists():
                logger.debug(f"分類 {category_dir.name} 沒有關鍵字檔案")
                continue
            
            try:
                with open(keywords_file, 'r', encoding='utf-8') as f:
                    keywords = [line.strip() for line in f if line.strip()]
                
                self.category_keywords[category_dir.name] = set(keywords)
                logger.info(f"載入分類 '{category_dir.name}' 的 {len(keywords)} 個關鍵字")
                
            except Exception as e:
                logger.error(f"載入分類關鍵字失敗 {category_dir.name}: {e}")
    
    def get_categories(self) -> List[str]:
        """取得所有可用的分類"""
        return list(self.category_keywords.keys())
    
    def get_keywords_for_category(self, category: str, max_keywords: int = 5) -> List[str]:
        """
        取得指定分類的關鍵字
        
        Args:
            category: 分類名稱
            max_keywords: 最大關鍵字數量
            
        Returns:
            List[str]: 關鍵字列表
        """
        keywords = set()
        
        # 加入分類專用關鍵字
        if category in self.category_keywords:
            category_kw = self.category_keywords[category]
            keywords.update(category_kw)
        
        # 加入通用關鍵字
        keywords.update(self.general_keywords)
        
        # 隨機選擇關鍵字
        if len(keywords) <= max_keywords:
            return list(keywords)
        
        return random.sample(list(keywords), max_keywords)
    
    def get_keywords_for_product(self, product_data: Dict[str, str], category: str = None, max_keywords: int = 5) -> List[str]:
        """
        根據產品資料智能選擇關鍵字
        
        Args:
            product_data: 產品資料
            category: 產品分類
            max_keywords: 最大關鍵字數量
            
        Returns:
            List[str]: 選擇的關鍵字列表
        """
        # 如果沒有指定分類，嘗試從產品資料推斷
        if not category:
            category = self._infer_category(product_data)
        
        # 取得候選關鍵字
        candidate_keywords = set()
        
        # 分類關鍵字
        if category and category in self.category_keywords:
            candidate_keywords.update(self.category_keywords[category])
        
        # 通用關鍵字
        candidate_keywords.update(self.general_keywords)
        
        # 根據產品資料內容評分選擇關鍵字
        scored_keywords = self._score_keywords(candidate_keywords, product_data)
        
        # 選擇評分最高的關鍵字
        selected_keywords = [kw for kw, score in scored_keywords[:max_keywords]]
        
        logger.debug(f"為產品選擇關鍵字: {selected_keywords}")
        return selected_keywords
    
    def _infer_category(self, product_data: Dict[str, str]) -> Optional[str]:
        """
        從產品資料推斷分類
        
        Args:
            product_data: 產品資料
            
        Returns:
            Optional[str]: 推斷的分類名稱
        """
        # 將所有產品資料合併為文字
        product_text = " ".join(str(value) for value in product_data.values()).lower()
        
        # 分類關鍵字匹配
        category_scores = {}
        
        for category, keywords in self.category_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword.lower() in product_text:
                    score += 1
            
            if score > 0:
                category_scores[category] = score
        
        if category_scores:
            # 返回評分最高的分類
            best_category = max(category_scores, key=category_scores.get)
            logger.debug(f"推斷產品分類: {best_category} (評分: {category_scores[best_category]})")
            return best_category
        
        return None
    
    def _score_keywords(self, keywords: Set[str], product_data: Dict[str, str]) -> List[tuple]:
        """
        為關鍵字評分
        
        Args:
            keywords: 候選關鍵字集合
            product_data: 產品資料
            
        Returns:
            List[tuple]: (關鍵字, 評分) 的列表，按評分降序排列
        """
        # 將產品資料合併為文字
        product_text = " ".join(str(value) for value in product_data.values()).lower()
        
        keyword_scores = []
        
        for keyword in keywords:
            score = 0
            keyword_lower = keyword.lower()
            
            # 完全匹配
            if keyword_lower in product_text:
                score += 10
            
            # 部分匹配
            for char in keyword_lower:
                if char in product_text:
                    score += 1
            
            # 關鍵字長度獎勵（較短的關鍵字更容易自然嵌入）
            if len(keyword) <= 3:
                score += 5
            elif len(keyword) <= 5:
                score += 3
            
            keyword_scores.append((keyword, score))
        
        # 按評分降序排列
        keyword_scores.sort(key=lambda x: x[1], reverse=True)
        
        return keyword_scores
    
    def add_category_keywords(self, category: str, keywords: List[str]) -> bool:
        """
        新增分類關鍵字
        
        Args:
            category: 分類名稱
            keywords: 關鍵字列表
            
        Returns:
            bool: 是否成功
        """
        try:
            category_dir = self.categories_dir / category
            category_dir.mkdir(exist_ok=True)
            
            keywords_file = category_dir / "keywords.txt"
            
            # 如果檔案已存在，合併關鍵字
            existing_keywords = set()
            if keywords_file.exists():
                with open(keywords_file, 'r', encoding='utf-8') as f:
                    existing_keywords = set(line.strip() for line in f if line.strip())
            
            # 合併新關鍵字
            all_keywords = existing_keywords.union(set(keywords))
            
            # 寫入檔案
            with open(keywords_file, 'w', encoding='utf-8') as f:
                for keyword in sorted(all_keywords):
                    f.write(f"{keyword}\n")
            
            # 更新記憶體中的關鍵字
            self.category_keywords[category] = all_keywords
            
            logger.info(f"新增分類 '{category}' 關鍵字: {len(keywords)} 個")
            return True
            
        except Exception as e:
            logger.error(f"新增分類關鍵字失敗: {e}")
            return False
    
    def reload_keywords(self):
        """重新載入所有關鍵字"""
        self.general_keywords.clear()
        self.category_keywords.clear()
        self.load_keywords()
        logger.info("關鍵字重新載入完成")
    
    def get_keyword_stats(self) -> Dict[str, int]:
        """取得關鍵字統計資訊"""
        stats = {
            "general_keywords": len(self.general_keywords),
            "categories": len(self.category_keywords),
            "total_category_keywords": sum(len(kw) for kw in self.category_keywords.values())
        }
        
        for category, keywords in self.category_keywords.items():
            stats[f"category_{category}"] = len(keywords)
        
        return stats
