2025-08-02 00:15:07 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 00:15:07 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 00:15:08 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 00:15:08 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 00:15:08 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 00:15:08 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 00:15:08 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 00:15:08 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 00:15:08 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 00:15:08 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 00:15:08 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 00:15:08 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 00:15:08 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 00:15:08 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 00:15:08 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-02 00:15:09 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 00:15:09 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 00:15:09 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 00:15:09 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 00:15:09 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-02 00:15:10 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 00:17:09 | INFO     | gui.main_window:log_message:910 | 選擇工作表: Sheet1
2025-08-02 00:17:09 | INFO     | gui.main_window:log_message:910 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-02 00:17:13 | INFO     | gui.main_window:log_message:910 | 圖片資料夾警告: 圖片目錄中沒有找到支援的圖片檔案
2025-08-02 00:17:16 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 00:17:16 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 00:17:16 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 00:17:16 | INFO     | gui.main_window:log_message:910 | 自動設定處理範圍: 1 到 14 行
2025-08-02 00:17:16 | INFO     | gui.main_window:log_message:910 | 資料載入成功: 14 行, 7 欄
2025-08-02 00:17:16 | INFO     | gui.main_window:log_message:910 | 警告: 部分欄位有缺失值
2025-08-02 00:17:37 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 00:17:41 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-02 00:17:41 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号, 数量
2025-08-02 00:17:41 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 00:17:41 | INFO     | gui.main_window:log_message:910 | 開始處理 5 個項目...
2025-08-02 00:17:41 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 0 到 4 (共 5 項)
2025-08-02 00:17:41 | INFO     | core.processing_engine:process_batch:379 | Writer AI: anthropic-sonnet4, Reviewer AI: openai-gpt4o-mini
2025-08-02 00:17:41 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/5 (行 0)
2025-08-02 00:17:41 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['輕鬆', '安全', '營養', '無人工色素', '有效']
2025-08-02 00:17:52 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 539 input + 521 output = 1060 tokens, 成本: $0.000000
2025-08-02 00:17:52 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1060,0.0
2025-08-02 00:17:52 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1060, 時間: 11.40s
2025-08-02 00:17:53 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 942 input + 11 output = 953 tokens, 成本: $0.000000
2025-08-02 00:17:53 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,953,0.0
2025-08-02 00:17:53 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 953, 時間: 1.39s
2025-08-02 00:17:53 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A309... (12.79s)
2025-08-02 00:17:53 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/5 (行 1)
2025-08-02 00:17:53 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['輕鬆', '安全', '營養', '無人工色素', '有效']
2025-08-02 00:17:53 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_BD19B18174F845EA81C94203F1012FC2' 找到 0 張圖片
2025-08-02 00:18:05 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 563 input + 582 output = 1145 tokens, 成本: $0.000000
2025-08-02 00:18:05 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1145,0.0
2025-08-02 00:18:05 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1145, 時間: 11.22s
2025-08-02 00:18:06 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1030 input + 10 output = 1040 tokens, 成本: $0.000000
2025-08-02 00:18:06 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1040,0.0
2025-08-02 00:18:06 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1040, 時間: 1.16s
2025-08-02 00:18:06 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2423... (12.38s)
2025-08-02 00:18:06 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/5 (行 2)
2025-08-02 00:18:06 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['輕鬆', '安全', '營養', '有效', '溫和']
2025-08-02 00:18:06 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_BB234940DB994A06BF882CFB01761806' 找到 0 張圖片
2025-08-02 00:18:17 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 557 input + 584 output = 1141 tokens, 成本: $0.000000
2025-08-02 00:18:17 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1141,0.0
2025-08-02 00:18:17 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1141, 時間: 11.34s
2025-08-02 00:18:18 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1015 input + 10 output = 1025 tokens, 成本: $0.000000
2025-08-02 00:18:18 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1025,0.0
2025-08-02 00:18:18 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1025, 時間: 0.99s
2025-08-02 00:18:18 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2561-18... (12.34s)
2025-08-02 00:18:18 | INFO     | core.processing_engine:process_batch:382 | 處理項目 4/5 (行 3)
2025-08-02 00:18:18 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '輕鬆', '安全', '營養', '無人工色素']
2025-08-02 00:18:18 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_7790BC4249FE45D5AA00F5DF1E857F02' 找到 0 張圖片
2025-08-02 00:18:29 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 573 input + 562 output = 1135 tokens, 成本: $0.000000
2025-08-02 00:18:29 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1135,0.0
2025-08-02 00:18:29 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1135, 時間: 11.04s
2025-08-02 00:18:30 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1010 input + 6 output = 1016 tokens, 成本: $0.000000
2025-08-02 00:18:30 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1016,0.0
2025-08-02 00:18:30 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1016, 時間: 1.11s
2025-08-02 00:18:30 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2368... (12.15s)
2025-08-02 00:18:30 | INFO     | core.processing_engine:process_batch:382 | 處理項目 5/5 (行 4)
2025-08-02 00:18:30 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['輕鬆', '安全', '營養', '無人工色素', '有效']
2025-08-02 00:18:30 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_4901209267F84A65B5B3C44C9FF10428' 找到 0 張圖片
2025-08-02 00:18:41 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 569 input + 593 output = 1162 tokens, 成本: $0.000000
2025-08-02 00:18:41 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1162,0.0
2025-08-02 00:18:41 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1162, 時間: 10.64s
2025-08-02 00:18:42 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1049 input + 11 output = 1060 tokens, 成本: $0.000000
2025-08-02 00:18:42 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1060,0.0
2025-08-02 00:18:42 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1060, 時間: 0.88s
2025-08-02 00:18:42 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2556... (11.52s)
2025-08-02 00:18:42 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 5, 失敗 0
2025-08-02 00:18:42 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 00:18:42 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 00:18:42 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 00:18:42 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 5, 失敗: 0, 總 Token: 10,737, 預估費用: $0.000000
2025-08-02 00:18:42 | INFO     | gui.main_window:log_message:910 | 自動顯示 A309... 的預覽
2025-08-02 00:20:55 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-02 00:20:55 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号, 数量
2025-08-02 00:20:55 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 00:20:55 | INFO     | gui.main_window:log_message:910 | 開始處理 5 個項目...
2025-08-02 00:20:55 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 0 到 4 (共 5 項)
2025-08-02 00:20:55 | INFO     | core.processing_engine:process_batch:379 | Writer AI: anthropic-sonnet4, Reviewer AI: openai-gpt4o-mini
2025-08-02 00:20:55 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/5 (行 0)
2025-08-02 00:20:55 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 2)
2025-08-02 00:20:55 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['無人工色素', '輕鬆', '營養', '安全', '有效']
2025-08-02 00:21:00 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 519 input + 156 output = 675 tokens, 成本: $0.000000
2025-08-02 00:21:00 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,675,0.0
2025-08-02 00:21:00 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 675, 時間: 4.89s
2025-08-02 00:21:01 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1125 input + 11 output = 1136 tokens, 成本: $0.000000
2025-08-02 00:21:01 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1136,0.0
2025-08-02 00:21:01 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1136, 時間: 0.94s
2025-08-02 00:21:01 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A309... (5.84s)
2025-08-02 00:21:01 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/5 (行 1)
2025-08-02 00:21:01 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 2)
2025-08-02 00:21:01 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['無人工色素', '輕鬆', '營養', '安全', '有效']
2025-08-02 00:21:01 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_BD19B18174F845EA81C94203F1012FC2' 找到 0 張圖片
2025-08-02 00:21:08 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 519 input + 193 output = 712 tokens, 成本: $0.000000
2025-08-02 00:21:08 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,712,0.0
2025-08-02 00:21:08 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 712, 時間: 7.06s
2025-08-02 00:21:09 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1246 input + 6 output = 1252 tokens, 成本: $0.000000
2025-08-02 00:21:09 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1252,0.0
2025-08-02 00:21:09 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1252, 時間: 0.84s
2025-08-02 00:21:09 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2423... (7.91s)
2025-08-02 00:21:09 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/5 (行 2)
2025-08-02 00:21:09 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 2)
2025-08-02 00:21:09 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['輕鬆', '營養', '安全', '溫和', '有效']
2025-08-02 00:21:09 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_BB234940DB994A06BF882CFB01761806' 找到 0 張圖片
2025-08-02 00:21:12 | INFO     | gui.main_window:log_message:910 | 顯示第 2 行的 HTML 預覽和 Reviewer 結果
2025-08-02 00:21:12 | INFO     | gui.main_window:log_message:910 | 顯示第 2 行的 HTML 預覽和 Reviewer 結果
2025-08-02 00:21:13 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 519 input + 160 output = 679 tokens, 成本: $0.000000
2025-08-02 00:21:13 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,679,0.0
2025-08-02 00:21:13 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 679, 時間: 4.56s
2025-08-02 00:21:14 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1200 input + 10 output = 1210 tokens, 成本: $0.000000
2025-08-02 00:21:14 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1210,0.0
2025-08-02 00:21:14 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1210, 時間: 1.10s
2025-08-02 00:21:14 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2561-18... (5.66s)
2025-08-02 00:21:14 | INFO     | core.processing_engine:process_batch:382 | 處理項目 4/5 (行 3)
2025-08-02 00:21:14 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 2)
2025-08-02 00:21:14 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '無人工色素', '輕鬆', '營養', '安全']
2025-08-02 00:21:14 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_7790BC4249FE45D5AA00F5DF1E857F02' 找到 0 張圖片
2025-08-02 00:21:20 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 519 input + 191 output = 710 tokens, 成本: $0.000000
2025-08-02 00:21:20 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,710,0.0
2025-08-02 00:21:20 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 710, 時間: 5.87s
2025-08-02 00:21:21 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1220 input + 9 output = 1229 tokens, 成本: $0.000000
2025-08-02 00:21:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1229,0.0
2025-08-02 00:21:21 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1229, 時間: 0.76s
2025-08-02 00:21:21 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2368... (6.63s)
2025-08-02 00:21:21 | INFO     | core.processing_engine:process_batch:382 | 處理項目 5/5 (行 4)
2025-08-02 00:21:21 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 2)
2025-08-02 00:21:21 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['無人工色素', '輕鬆', '營養', '安全', '有效']
2025-08-02 00:21:21 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_4901209267F84A65B5B3C44C9FF10428' 找到 0 張圖片
2025-08-02 00:21:25 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 519 input + 139 output = 658 tokens, 成本: $0.000000
2025-08-02 00:21:25 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,658,0.0
2025-08-02 00:21:25 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 658, 時間: 3.86s
2025-08-02 00:21:26 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1215 input + 9 output = 1224 tokens, 成本: $0.000000
2025-08-02 00:21:26 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1224,0.0
2025-08-02 00:21:26 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1224, 時間: 0.92s
2025-08-02 00:21:26 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2556... (4.79s)
2025-08-02 00:21:26 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 5, 失敗 0
2025-08-02 00:21:26 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 00:21:26 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 00:21:26 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 00:21:26 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 5, 失敗: 0, 總 Token: 9,485, 預估費用: $0.000000
2025-08-02 00:21:26 | INFO     | gui.main_window:log_message:910 | 自動顯示 A309... 的預覽
2025-08-02 08:16:30 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 10:20:27 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 10:20:27 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 10:20:28 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 10:20:28 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 10:20:28 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 10:20:28 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 10:20:28 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 10:20:28 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 10:20:28 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 10:20:28 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 10:20:28 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 10:20:28 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 10:20:28 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 10:20:28 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 10:20:28 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-02 10:20:29 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:20:29 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:20:29 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:20:29 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:20:29 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-02 10:20:30 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 10:20:54 | INFO     | gui.main_window:log_message:910 | 選擇工作表: Sheet1
2025-08-02 10:20:54 | INFO     | gui.main_window:log_message:910 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-02 10:20:57 | INFO     | gui.main_window:log_message:910 | 圖片資料夾警告: 圖片目錄中沒有找到支援的圖片檔案
2025-08-02 10:21:00 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 10:21:00 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 10:21:00 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 10:21:00 | INFO     | gui.main_window:log_message:910 | 自動設定處理範圍: 1 到 14 行
2025-08-02 10:21:00 | INFO     | gui.main_window:log_message:910 | 資料載入成功: 14 行, 7 欄
2025-08-02 10:21:00 | INFO     | gui.main_window:log_message:910 | 警告: 部分欄位有缺失值
2025-08-02 10:21:12 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:21:24 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: Gemini 2.5 Pro | Google | ✅強大圖片 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:21:34 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Gemini 2.5 Flash | Google | ✅圖片理解 | 💰經濟 | 🌟🌟🌟精緻度 | Reviewer快速審查
2025-08-02 10:21:44 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-02 10:21:44 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号, 数量
2025-08-02 10:21:44 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 10:21:44 | INFO     | gui.main_window:log_message:910 | 開始處理 4 個項目...
2025-08-02 10:21:44 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 8 到 11 (共 4 項)
2025-08-02 10:21:44 | INFO     | core.processing_engine:process_batch:379 | Writer AI: google-pro25, Reviewer AI: google-flash25
2025-08-02 10:21:44 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/4 (行 8)
2025-08-02 10:21:44 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '實用', '營養', '全方位', '舒適']
2025-08-02 10:21:44 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y172chair' 找到 0 張圖片
2025-08-02 10:21:45 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 14
}
]
2025-08-02 10:21:45 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: google-pro25, 錯誤: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 14
}
]
2025-08-02 10:21:45 | ERROR    | core.processing_engine:process_batch:399 | 項目處理失敗: Writer 階段失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 14
}
]
2025-08-02 10:21:45 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/4 (行 9)
2025-08-02 10:21:45 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '實用', '營養', '全方位', '舒適']
2025-08-02 10:21:45 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y173chair' 找到 0 張圖片
2025-08-02 10:21:46 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 13
}
]
2025-08-02 10:21:46 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: google-pro25, 錯誤: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 13
}
]
2025-08-02 10:21:46 | ERROR    | core.processing_engine:process_batch:399 | 項目處理失敗: Writer 階段失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 13
}
]
2025-08-02 10:21:46 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/4 (行 10)
2025-08-02 10:21:46 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['實用', '營養', '全方位', '舒適', '完整']
2025-08-02 10:21:46 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y174chair' 找到 0 張圖片
2025-08-02 10:21:46 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 13
}
]
2025-08-02 10:21:46 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: google-pro25, 錯誤: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 13
}
]
2025-08-02 10:21:46 | ERROR    | core.processing_engine:process_batch:399 | 項目處理失敗: Writer 階段失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 13
}
]
2025-08-02 10:21:46 | INFO     | core.processing_engine:process_batch:382 | 處理項目 4/4 (行 11)
2025-08-02 10:21:46 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['實用', '營養', '全方位', '舒適', '完整']
2025-08-02 10:21:46 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y200chair' 找到 0 張圖片
2025-08-02 10:21:47 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 12
}
]
2025-08-02 10:21:47 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: google-pro25, 錯誤: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 12
}
]
2025-08-02 10:21:47 | ERROR    | core.processing_engine:process_batch:399 | 項目處理失敗: Writer 階段失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 12
}
]
2025-08-02 10:21:47 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 0, 失敗 0
2025-08-02 10:21:47 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 10:21:47 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 10:21:47 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 10:21:47 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 0, 失敗: 4, 總 Token: 0, 預估費用: $0.000000
2025-08-02 10:21:58 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:22:02 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:22:04 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-02 10:22:04 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号, 数量
2025-08-02 10:22:04 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 10:22:04 | INFO     | gui.main_window:log_message:910 | 開始處理 4 個項目...
2025-08-02 10:22:04 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 8 到 11 (共 4 項)
2025-08-02 10:22:04 | INFO     | core.processing_engine:process_batch:379 | Writer AI: openai-gpt4o, Reviewer AI: anthropic-sonnet4
2025-08-02 10:22:04 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/4 (行 8)
2025-08-02 10:22:04 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 3)
2025-08-02 10:22:04 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['全方位', '高品質', '實用', '舒適', '品質']
2025-08-02 10:22:04 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y172chair' 找到 0 張圖片
2025-08-02 10:22:17 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 486 input + 491 output = 977 tokens, 成本: $0.000000
2025-08-02 10:22:17 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,977,0.0
2025-08-02 10:22:17 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 977, 時間: 13.10s
2025-08-02 10:22:21 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 1319 input + 88 output = 1407 tokens, 成本: $0.000000
2025-08-02 10:22:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1407,0.0
2025-08-02 10:22:21 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1407, 時間: 3.91s
2025-08-02 10:22:21 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: B2551... (17.02s)
2025-08-02 10:22:21 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/4 (行 9)
2025-08-02 10:22:21 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 3)
2025-08-02 10:22:21 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['全方位', '高品質', '實用', '舒適', '品質']
2025-08-02 10:22:21 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y173chair' 找到 0 張圖片
2025-08-02 10:22:34 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 471 input + 521 output = 992 tokens, 成本: $0.000000
2025-08-02 10:22:34 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,992,0.0
2025-08-02 10:22:34 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 992, 時間: 13.09s
2025-08-02 10:22:41 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 1354 input + 95 output = 1449 tokens, 成本: $0.000000
2025-08-02 10:22:41 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1449,0.0
2025-08-02 10:22:41 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1449, 時間: 6.84s
2025-08-02 10:22:41 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: B2550... (19.93s)
2025-08-02 10:22:41 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/4 (行 10)
2025-08-02 10:22:41 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 2)
2025-08-02 10:22:41 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['全方位', '實用', '舒適', '完整', '營養']
2025-08-02 10:22:41 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y174chair' 找到 0 張圖片
2025-08-02 10:22:57 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 458 input + 518 output = 976 tokens, 成本: $0.000000
2025-08-02 10:22:57 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,976,0.0
2025-08-02 10:22:57 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 976, 時間: 16.56s
2025-08-02 10:23:04 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 1343 input + 84 output = 1427 tokens, 成本: $0.000000
2025-08-02 10:23:04 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1427,0.0
2025-08-02 10:23:04 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1427, 時間: 6.47s
2025-08-02 10:23:04 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: 24-26#... (23.04s)
2025-08-02 10:23:04 | INFO     | core.processing_engine:process_batch:382 | 處理項目 4/4 (行 11)
2025-08-02 10:23:04 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 2)
2025-08-02 10:23:04 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['全方位', '實用', '舒適', '完整', '營養']
2025-08-02 10:23:04 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y200chair' 找到 0 張圖片
2025-08-02 10:23:19 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 459 input + 534 output = 993 tokens, 成本: $0.000000
2025-08-02 10:23:19 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,993,0.0
2025-08-02 10:23:19 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 993, 時間: 14.96s
2025-08-02 10:23:23 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 1309 input + 87 output = 1396 tokens, 成本: $0.000000
2025-08-02 10:23:23 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1396,0.0
2025-08-02 10:23:23 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1396, 時間: 3.95s
2025-08-02 10:23:23 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Y200... (18.92s)
2025-08-02 10:23:23 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 4, 失敗 0
2025-08-02 10:23:23 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 10:23:23 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 10:23:23 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 10:23:23 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 4, 失敗: 0, 總 Token: 9,617, 預估費用: $0.000000
2025-08-02 10:23:23 | INFO     | gui.main_window:log_message:910 | 自動顯示 B2551... 的預覽
2025-08-02 10:26:01 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 10:26:01 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 10:26:01 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 10:26:01 | INFO     | gui.main_window:log_message:910 | 自動設定處理範圍: 1 到 14 行
2025-08-02 10:26:01 | INFO     | gui.main_window:log_message:910 | 資料載入成功: 14 行, 7 欄
2025-08-02 10:26:01 | INFO     | gui.main_window:log_message:910 | 警告: 部分欄位有缺失值
2025-08-02 10:26:25 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 10:26:25 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 10:26:25 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 10:26:26 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 10:26:26 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 10:26:26 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 10:26:26 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 10:26:26 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 10:26:26 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 10:26:26 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 10:26:26 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 10:26:26 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 10:26:26 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 10:26:26 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 10:26:26 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-02 10:26:26 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:26:26 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:26:26 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:26:26 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:26:26 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-02 10:26:26 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 10:27:36 | INFO     | gui.main_window:log_message:910 | 選擇工作表: Sheet1
2025-08-02 10:27:36 | INFO     | gui.main_window:log_message:910 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-02 10:27:41 | INFO     | gui.main_window:log_message:910 | 圖片資料夾警告: 圖片目錄中沒有找到支援的圖片檔案
2025-08-02 10:27:46 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 10:27:46 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 10:27:46 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 10:27:46 | INFO     | gui.main_window:log_message:910 | 自動設定處理範圍: 1 到 14 行
2025-08-02 10:27:46 | INFO     | gui.main_window:log_message:910 | 資料載入成功: 14 行, 7 欄
2025-08-02 10:27:46 | INFO     | gui.main_window:log_message:910 | 警告: 部分欄位有缺失值
2025-08-02 10:27:50 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:27:54 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 10:28:07 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号']
2025-08-02 10:28:07 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号
2025-08-02 10:28:07 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 10:28:07 | INFO     | gui.main_window:log_message:910 | 開始處理 3 個項目...
2025-08-02 10:28:07 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 10 到 12 (共 3 項)
2025-08-02 10:28:07 | INFO     | core.processing_engine:process_batch:379 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 10:28:07 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/3 (行 10)
2025-08-02 10:28:07 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['守護', '簡單', '高品質', '認證', '節能']
2025-08-02 10:28:07 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y174chair' 找到 0 張圖片
2025-08-02 10:28:16 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 449 input + 519 output = 968 tokens, 成本: $0.000000
2025-08-02 10:28:16 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,968,0.0
2025-08-02 10:28:16 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 968, 時間: 9.04s
2025-08-02 10:28:17 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1031 input + 12 output = 1043 tokens, 成本: $0.000000
2025-08-02 10:28:17 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1043,0.0
2025-08-02 10:28:17 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1043, 時間: 1.61s
2025-08-02 10:28:17 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: 24-26#... (10.66s)
2025-08-02 10:28:17 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/3 (行 11)
2025-08-02 10:28:17 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['守護', '簡單', '高品質', '認證', '節能']
2025-08-02 10:28:17 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y200chair' 找到 0 張圖片
2025-08-02 10:28:25 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 449 input + 508 output = 957 tokens, 成本: $0.000000
2025-08-02 10:28:25 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,957,0.0
2025-08-02 10:28:25 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 957, 時間: 7.90s
2025-08-02 10:28:26 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1020 input + 9 output = 1029 tokens, 成本: $0.000000
2025-08-02 10:28:26 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1029,0.0
2025-08-02 10:28:26 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1029, 時間: 0.98s
2025-08-02 10:28:26 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Y200... (8.89s)
2025-08-02 10:28:26 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/3 (行 12)
2025-08-02 10:28:26 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '守護', '簡單', '認證', '節能']
2025-08-02 10:28:26 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y176chair' 找到 0 張圖片
2025-08-02 10:28:35 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 449 input + 529 output = 978 tokens, 成本: $0.000000
2025-08-02 10:28:35 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,978,0.0
2025-08-02 10:28:35 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 978, 時間: 8.75s
2025-08-02 10:28:36 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1063 input + 9 output = 1072 tokens, 成本: $0.000000
2025-08-02 10:28:36 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1072,0.0
2025-08-02 10:28:36 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1072, 時間: 0.74s
2025-08-02 10:28:36 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Y-171... (9.49s)
2025-08-02 10:28:36 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 3, 失敗 0
2025-08-02 10:28:36 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 10:28:36 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 10:28:36 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 10:28:36 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 3, 失敗: 0, 總 Token: 6,047, 預估費用: $0.000000
2025-08-02 10:28:36 | INFO     | gui.main_window:log_message:910 | 自動顯示 24-26#... 的預覽
2025-08-02 10:50:24 | INFO     | core.prompt_manager:add_writer_prompt:214 | 新增 Writer Prompt: pharmacy
2025-08-02 10:50:24 | INFO     | gui.main_window:log_message:910 | 已儲存 Writer Prompt: pharmacy
2025-08-02 10:52:46 | INFO     | core.prompt_manager:add_reviewer_prompt:241 | 新增 Reviewer Prompt: standard
2025-08-02 10:52:46 | INFO     | gui.main_window:log_message:910 | 已儲存 Reviewer Prompt: standard
2025-08-02 10:54:17 | INFO     | core.prompt_manager:add_writer_prompt:214 | 新增 Writer Prompt: furniture
2025-08-02 10:54:17 | INFO     | gui.main_window:log_message:910 | 已儲存 Writer Prompt: furniture
2025-08-02 10:55:39 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 10:55:39 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 10:55:40 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 10:55:40 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 10:55:40 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 10:55:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 10:55:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 10:55:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 10:55:40 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 10:55:40 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 10:55:40 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 10:55:40 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 10:55:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 10:55:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 10:55:40 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-02 10:55:41 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:55:41 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:55:41 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:55:41 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:55:41 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-02 10:55:41 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 10:55:50 | INFO     | gui.main_window:log_message:910 | 選擇工作表: Sheet1
2025-08-02 10:55:50 | INFO     | gui.main_window:log_message:910 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-02 10:55:52 | INFO     | gui.main_window:log_message:910 | 選擇工作表: Sheet1
2025-08-02 10:55:52 | INFO     | gui.main_window:log_message:910 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-02 10:55:55 | INFO     | gui.main_window:log_message:910 | 圖片資料夾警告: 圖片目錄中沒有找到支援的圖片檔案
2025-08-02 10:55:58 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 10:55:58 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 10:55:58 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 10:55:58 | INFO     | gui.main_window:log_message:910 | 自動設定處理範圍: 1 到 14 行
2025-08-02 10:55:58 | INFO     | gui.main_window:log_message:910 | 資料載入成功: 14 行, 7 欄
2025-08-02 10:55:58 | INFO     | gui.main_window:log_message:910 | 警告: 部分欄位有缺失值
2025-08-02 10:56:34 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 10:56:35 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 10:56:37 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:56:49 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-02 10:56:49 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号, 数量
2025-08-02 10:56:49 | INFO     | gui.main_window:log_message:910 | 開始處理 2 個項目...
2025-08-02 10:56:49 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 3 到 4 (共 2 項)
2025-08-02 10:56:49 | INFO     | core.processing_engine:process_batch:379 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 10:56:49 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/2 (行 3)
2025-08-02 10:56:49 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '親膚', '多功能', '健康', '無人工色素']
2025-08-02 10:57:00 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 540 input + 465 output = 1005 tokens, 成本: $0.000000
2025-08-02 10:57:00 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1005,0.0
2025-08-02 10:57:00 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1005, 時間: 10.83s
2025-08-02 10:57:03 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1029 input + 83 output = 1112 tokens, 成本: $0.000000
2025-08-02 10:57:03 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1112,0.0
2025-08-02 10:57:03 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1112, 時間: 3.10s
2025-08-02 10:57:03 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2368... (13.93s)
2025-08-02 10:57:03 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/2 (行 4)
2025-08-02 10:57:03 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['親膚', '多功能', '可持續', '健康', '無人工色素']
2025-08-02 10:57:14 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 540 input + 434 output = 974 tokens, 成本: $0.000000
2025-08-02 10:57:14 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,974,0.0
2025-08-02 10:57:14 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 974, 時間: 11.71s
2025-08-02 10:57:21 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1000 input + 89 output = 1089 tokens, 成本: $0.000000
2025-08-02 10:57:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1089,0.0
2025-08-02 10:57:21 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1089, 時間: 6.19s
2025-08-02 10:57:21 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2556... (17.90s)
2025-08-02 10:57:21 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 2, 失敗 0
2025-08-02 10:57:21 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 10:57:21 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 10:57:21 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 10:57:21 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 2, 失敗: 0, 總 Token: 4,180, 預估費用: $0.000000
2025-08-02 10:57:21 | INFO     | gui.main_window:log_message:910 | 自動顯示 A2368... 的預覽
2025-08-02 10:59:56 | INFO     | core.prompt_manager:add_reviewer_prompt:241 | 新增 Reviewer Prompt: standard
2025-08-02 10:59:56 | INFO     | gui.main_window:log_message:910 | 已儲存 Reviewer Prompt: standard
2025-08-02 11:02:29 | INFO     | core.prompt_manager:add_writer_prompt:214 | 新增 Writer Prompt: furniture
2025-08-02 11:02:29 | INFO     | gui.main_window:log_message:910 | 已儲存 Writer Prompt: furniture
2025-08-02 11:03:36 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 11:03:36 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 11:03:37 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 11:03:37 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 11:03:37 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 11:03:37 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 11:03:37 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 11:03:37 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 11:03:37 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 11:03:37 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 11:03:37 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 11:03:37 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 11:03:37 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 11:03:37 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 11:03:37 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-02 11:03:38 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:03:38 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:03:38 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:03:38 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:03:38 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-02 11:03:38 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 11:03:52 | INFO     | gui.main_window:log_message:910 | 選擇工作表: Sheet1
2025-08-02 11:03:52 | INFO     | gui.main_window:log_message:910 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-02 11:03:55 | INFO     | gui.main_window:log_message:910 | 圖片資料夾警告: 圖片目錄中沒有找到支援的圖片檔案
2025-08-02 11:04:02 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 11:04:02 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 11:04:02 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 11:04:02 | INFO     | gui.main_window:log_message:910 | 自動設定處理範圍: 1 到 14 行
2025-08-02 11:04:02 | INFO     | gui.main_window:log_message:910 | 資料載入成功: 14 行, 7 欄
2025-08-02 11:04:02 | INFO     | gui.main_window:log_message:910 | 警告: 部分欄位有缺失值
2025-08-02 11:04:14 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:04:16 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 11:04:28 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-02 11:04:28 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号, 数量
2025-08-02 11:04:28 | INFO     | gui.main_window:log_message:910 | 開始處理 3 個項目...
2025-08-02 11:04:28 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 0 到 2 (共 3 項)
2025-08-02 11:04:28 | INFO     | core.processing_engine:process_batch:379 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 11:04:28 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/3 (行 0)
2025-08-02 11:04:28 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['節能', '舒適', '均衡', '經典', '滋養']
2025-08-02 11:04:34 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 617 input + 322 output = 939 tokens, 成本: $0.000000
2025-08-02 11:04:34 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,939,0.0
2025-08-02 11:04:34 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 939, 時間: 6.13s
2025-08-02 11:04:39 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 817 input + 217 output = 1034 tokens, 成本: $0.000000
2025-08-02 11:04:39 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1034,0.0
2025-08-02 11:04:39 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1034, 時間: 4.71s
2025-08-02 11:04:39 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A309... (10.84s)
2025-08-02 11:04:39 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/3 (行 1)
2025-08-02 11:04:39 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['節能', '舒適', '均衡', '經典', '滋養']
2025-08-02 11:04:46 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 617 input + 306 output = 923 tokens, 成本: $0.000000
2025-08-02 11:04:46 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,923,0.0
2025-08-02 11:04:46 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 923, 時間: 6.59s
2025-08-02 11:04:49 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 833 input + 140 output = 973 tokens, 成本: $0.000000
2025-08-02 11:04:49 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,973,0.0
2025-08-02 11:04:49 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 973, 時間: 3.16s
2025-08-02 11:04:49 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2423... (9.75s)
2025-08-02 11:04:49 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/3 (行 2)
2025-08-02 11:04:49 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['節能', '舒適', '均衡', '經典', '滋養']
2025-08-02 11:04:57 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 617 input + 357 output = 974 tokens, 成本: $0.000000
2025-08-02 11:04:57 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,974,0.0
2025-08-02 11:04:57 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 974, 時間: 7.99s
2025-08-02 11:05:03 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 877 input + 295 output = 1172 tokens, 成本: $0.000000
2025-08-02 11:05:03 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1172,0.0
2025-08-02 11:05:03 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1172, 時間: 5.77s
2025-08-02 11:05:03 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2561-18... (13.76s)
2025-08-02 11:05:03 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 3, 失敗 0
2025-08-02 11:05:03 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 11:05:03 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 11:05:03 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 11:05:03 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 3, 失敗: 0, 總 Token: 6,015, 預估費用: $0.000000
2025-08-02 11:05:03 | INFO     | gui.main_window:log_message:910 | 自動顯示 A309... 的預覽
2025-08-02 11:09:17 | INFO     | gui.main_window:log_message:910 | 顯示第 1 行的 HTML 預覽和 Reviewer 結果
2025-08-02 11:09:17 | INFO     | gui.main_window:log_message:910 | 顯示第 1 行的 HTML 預覽和 Reviewer 結果
2025-08-02 11:15:20 | INFO     | gui.main_window:log_message:910 | 顯示第 2 行的 HTML 預覽和 Reviewer 結果
2025-08-02 11:15:20 | INFO     | gui.main_window:log_message:910 | 顯示第 2 行的 HTML 預覽和 Reviewer 結果
2025-08-02 11:16:41 | INFO     | core.prompt_manager:add_writer_prompt:214 | 新增 Writer Prompt: furniture
2025-08-02 11:16:41 | INFO     | gui.main_window:log_message:910 | 已儲存 Writer Prompt: furniture
2025-08-02 11:17:08 | INFO     | core.prompt_manager:add_reviewer_prompt:241 | 新增 Reviewer Prompt: standard
2025-08-02 11:17:08 | INFO     | gui.main_window:log_message:910 | 已儲存 Reviewer Prompt: standard
