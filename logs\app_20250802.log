2025-08-02 00:15:07 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 00:15:07 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 00:15:08 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 00:15:08 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 00:15:08 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 00:15:08 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 00:15:08 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 00:15:08 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 00:15:08 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 00:15:08 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 00:15:08 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 00:15:08 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 00:15:08 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 00:15:08 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 00:15:08 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-02 00:15:09 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 00:15:09 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 00:15:09 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 00:15:09 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 00:15:09 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-02 00:15:10 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 00:17:09 | INFO     | gui.main_window:log_message:910 | 選擇工作表: Sheet1
2025-08-02 00:17:09 | INFO     | gui.main_window:log_message:910 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-02 00:17:13 | INFO     | gui.main_window:log_message:910 | 圖片資料夾警告: 圖片目錄中沒有找到支援的圖片檔案
2025-08-02 00:17:16 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 00:17:16 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 00:17:16 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 00:17:16 | INFO     | gui.main_window:log_message:910 | 自動設定處理範圍: 1 到 14 行
2025-08-02 00:17:16 | INFO     | gui.main_window:log_message:910 | 資料載入成功: 14 行, 7 欄
2025-08-02 00:17:16 | INFO     | gui.main_window:log_message:910 | 警告: 部分欄位有缺失值
2025-08-02 00:17:37 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 00:17:41 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-02 00:17:41 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号, 数量
2025-08-02 00:17:41 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 00:17:41 | INFO     | gui.main_window:log_message:910 | 開始處理 5 個項目...
2025-08-02 00:17:41 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 0 到 4 (共 5 項)
2025-08-02 00:17:41 | INFO     | core.processing_engine:process_batch:379 | Writer AI: anthropic-sonnet4, Reviewer AI: openai-gpt4o-mini
2025-08-02 00:17:41 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/5 (行 0)
2025-08-02 00:17:41 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['輕鬆', '安全', '營養', '無人工色素', '有效']
2025-08-02 00:17:52 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 539 input + 521 output = 1060 tokens, 成本: $0.000000
2025-08-02 00:17:52 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1060,0.0
2025-08-02 00:17:52 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1060, 時間: 11.40s
2025-08-02 00:17:53 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 942 input + 11 output = 953 tokens, 成本: $0.000000
2025-08-02 00:17:53 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,953,0.0
2025-08-02 00:17:53 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 953, 時間: 1.39s
2025-08-02 00:17:53 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A309... (12.79s)
2025-08-02 00:17:53 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/5 (行 1)
2025-08-02 00:17:53 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['輕鬆', '安全', '營養', '無人工色素', '有效']
2025-08-02 00:17:53 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_BD19B18174F845EA81C94203F1012FC2' 找到 0 張圖片
2025-08-02 00:18:05 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 563 input + 582 output = 1145 tokens, 成本: $0.000000
2025-08-02 00:18:05 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1145,0.0
2025-08-02 00:18:05 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1145, 時間: 11.22s
2025-08-02 00:18:06 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1030 input + 10 output = 1040 tokens, 成本: $0.000000
2025-08-02 00:18:06 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1040,0.0
2025-08-02 00:18:06 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1040, 時間: 1.16s
2025-08-02 00:18:06 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2423... (12.38s)
2025-08-02 00:18:06 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/5 (行 2)
2025-08-02 00:18:06 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['輕鬆', '安全', '營養', '有效', '溫和']
2025-08-02 00:18:06 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_BB234940DB994A06BF882CFB01761806' 找到 0 張圖片
2025-08-02 00:18:17 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 557 input + 584 output = 1141 tokens, 成本: $0.000000
2025-08-02 00:18:17 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1141,0.0
2025-08-02 00:18:17 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1141, 時間: 11.34s
2025-08-02 00:18:18 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1015 input + 10 output = 1025 tokens, 成本: $0.000000
2025-08-02 00:18:18 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1025,0.0
2025-08-02 00:18:18 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1025, 時間: 0.99s
2025-08-02 00:18:18 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2561-18... (12.34s)
2025-08-02 00:18:18 | INFO     | core.processing_engine:process_batch:382 | 處理項目 4/5 (行 3)
2025-08-02 00:18:18 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '輕鬆', '安全', '營養', '無人工色素']
2025-08-02 00:18:18 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_7790BC4249FE45D5AA00F5DF1E857F02' 找到 0 張圖片
2025-08-02 00:18:29 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 573 input + 562 output = 1135 tokens, 成本: $0.000000
2025-08-02 00:18:29 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1135,0.0
2025-08-02 00:18:29 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1135, 時間: 11.04s
2025-08-02 00:18:30 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1010 input + 6 output = 1016 tokens, 成本: $0.000000
2025-08-02 00:18:30 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1016,0.0
2025-08-02 00:18:30 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1016, 時間: 1.11s
2025-08-02 00:18:30 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2368... (12.15s)
2025-08-02 00:18:30 | INFO     | core.processing_engine:process_batch:382 | 處理項目 5/5 (行 4)
2025-08-02 00:18:30 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['輕鬆', '安全', '營養', '無人工色素', '有效']
2025-08-02 00:18:30 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_4901209267F84A65B5B3C44C9FF10428' 找到 0 張圖片
2025-08-02 00:18:41 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 569 input + 593 output = 1162 tokens, 成本: $0.000000
2025-08-02 00:18:41 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1162,0.0
2025-08-02 00:18:41 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1162, 時間: 10.64s
2025-08-02 00:18:42 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1049 input + 11 output = 1060 tokens, 成本: $0.000000
2025-08-02 00:18:42 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1060,0.0
2025-08-02 00:18:42 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1060, 時間: 0.88s
2025-08-02 00:18:42 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2556... (11.52s)
2025-08-02 00:18:42 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 5, 失敗 0
2025-08-02 00:18:42 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 00:18:42 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 00:18:42 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 00:18:42 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 5, 失敗: 0, 總 Token: 10,737, 預估費用: $0.000000
2025-08-02 00:18:42 | INFO     | gui.main_window:log_message:910 | 自動顯示 A309... 的預覽
2025-08-02 00:20:55 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-02 00:20:55 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号, 数量
2025-08-02 00:20:55 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 00:20:55 | INFO     | gui.main_window:log_message:910 | 開始處理 5 個項目...
2025-08-02 00:20:55 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 0 到 4 (共 5 項)
2025-08-02 00:20:55 | INFO     | core.processing_engine:process_batch:379 | Writer AI: anthropic-sonnet4, Reviewer AI: openai-gpt4o-mini
2025-08-02 00:20:55 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/5 (行 0)
2025-08-02 00:20:55 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 2)
2025-08-02 00:20:55 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['無人工色素', '輕鬆', '營養', '安全', '有效']
2025-08-02 00:21:00 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 519 input + 156 output = 675 tokens, 成本: $0.000000
2025-08-02 00:21:00 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,675,0.0
2025-08-02 00:21:00 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 675, 時間: 4.89s
2025-08-02 00:21:01 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1125 input + 11 output = 1136 tokens, 成本: $0.000000
2025-08-02 00:21:01 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1136,0.0
2025-08-02 00:21:01 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1136, 時間: 0.94s
2025-08-02 00:21:01 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A309... (5.84s)
2025-08-02 00:21:01 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/5 (行 1)
2025-08-02 00:21:01 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 2)
2025-08-02 00:21:01 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['無人工色素', '輕鬆', '營養', '安全', '有效']
2025-08-02 00:21:01 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_BD19B18174F845EA81C94203F1012FC2' 找到 0 張圖片
2025-08-02 00:21:08 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 519 input + 193 output = 712 tokens, 成本: $0.000000
2025-08-02 00:21:08 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,712,0.0
2025-08-02 00:21:08 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 712, 時間: 7.06s
2025-08-02 00:21:09 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1246 input + 6 output = 1252 tokens, 成本: $0.000000
2025-08-02 00:21:09 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1252,0.0
2025-08-02 00:21:09 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1252, 時間: 0.84s
2025-08-02 00:21:09 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2423... (7.91s)
2025-08-02 00:21:09 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/5 (行 2)
2025-08-02 00:21:09 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 2)
2025-08-02 00:21:09 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['輕鬆', '營養', '安全', '溫和', '有效']
2025-08-02 00:21:09 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_BB234940DB994A06BF882CFB01761806' 找到 0 張圖片
2025-08-02 00:21:12 | INFO     | gui.main_window:log_message:910 | 顯示第 2 行的 HTML 預覽和 Reviewer 結果
2025-08-02 00:21:12 | INFO     | gui.main_window:log_message:910 | 顯示第 2 行的 HTML 預覽和 Reviewer 結果
2025-08-02 00:21:13 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 519 input + 160 output = 679 tokens, 成本: $0.000000
2025-08-02 00:21:13 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,679,0.0
2025-08-02 00:21:13 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 679, 時間: 4.56s
2025-08-02 00:21:14 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1200 input + 10 output = 1210 tokens, 成本: $0.000000
2025-08-02 00:21:14 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1210,0.0
2025-08-02 00:21:14 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1210, 時間: 1.10s
2025-08-02 00:21:14 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2561-18... (5.66s)
2025-08-02 00:21:14 | INFO     | core.processing_engine:process_batch:382 | 處理項目 4/5 (行 3)
2025-08-02 00:21:14 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 2)
2025-08-02 00:21:14 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '無人工色素', '輕鬆', '營養', '安全']
2025-08-02 00:21:14 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_7790BC4249FE45D5AA00F5DF1E857F02' 找到 0 張圖片
2025-08-02 00:21:20 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 519 input + 191 output = 710 tokens, 成本: $0.000000
2025-08-02 00:21:20 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,710,0.0
2025-08-02 00:21:20 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 710, 時間: 5.87s
2025-08-02 00:21:21 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1220 input + 9 output = 1229 tokens, 成本: $0.000000
2025-08-02 00:21:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1229,0.0
2025-08-02 00:21:21 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1229, 時間: 0.76s
2025-08-02 00:21:21 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2368... (6.63s)
2025-08-02 00:21:21 | INFO     | core.processing_engine:process_batch:382 | 處理項目 5/5 (行 4)
2025-08-02 00:21:21 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 2)
2025-08-02 00:21:21 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['無人工色素', '輕鬆', '營養', '安全', '有效']
2025-08-02 00:21:21 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_4901209267F84A65B5B3C44C9FF10428' 找到 0 張圖片
2025-08-02 00:21:25 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 519 input + 139 output = 658 tokens, 成本: $0.000000
2025-08-02 00:21:25 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,658,0.0
2025-08-02 00:21:25 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 658, 時間: 3.86s
2025-08-02 00:21:26 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1215 input + 9 output = 1224 tokens, 成本: $0.000000
2025-08-02 00:21:26 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1224,0.0
2025-08-02 00:21:26 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1224, 時間: 0.92s
2025-08-02 00:21:26 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2556... (4.79s)
2025-08-02 00:21:26 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 5, 失敗 0
2025-08-02 00:21:26 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 00:21:26 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 00:21:26 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 00:21:26 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 5, 失敗: 0, 總 Token: 9,485, 預估費用: $0.000000
2025-08-02 00:21:26 | INFO     | gui.main_window:log_message:910 | 自動顯示 A309... 的預覽
2025-08-02 08:16:30 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 10:20:27 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 10:20:27 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 10:20:28 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 10:20:28 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 10:20:28 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 10:20:28 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 10:20:28 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 10:20:28 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 10:20:28 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 10:20:28 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 10:20:28 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 10:20:28 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 10:20:28 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 10:20:28 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 10:20:28 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-02 10:20:29 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:20:29 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:20:29 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:20:29 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:20:29 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-02 10:20:30 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 10:20:54 | INFO     | gui.main_window:log_message:910 | 選擇工作表: Sheet1
2025-08-02 10:20:54 | INFO     | gui.main_window:log_message:910 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-02 10:20:57 | INFO     | gui.main_window:log_message:910 | 圖片資料夾警告: 圖片目錄中沒有找到支援的圖片檔案
2025-08-02 10:21:00 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 10:21:00 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 10:21:00 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 10:21:00 | INFO     | gui.main_window:log_message:910 | 自動設定處理範圍: 1 到 14 行
2025-08-02 10:21:00 | INFO     | gui.main_window:log_message:910 | 資料載入成功: 14 行, 7 欄
2025-08-02 10:21:00 | INFO     | gui.main_window:log_message:910 | 警告: 部分欄位有缺失值
2025-08-02 10:21:12 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:21:24 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: Gemini 2.5 Pro | Google | ✅強大圖片 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:21:34 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Gemini 2.5 Flash | Google | ✅圖片理解 | 💰經濟 | 🌟🌟🌟精緻度 | Reviewer快速審查
2025-08-02 10:21:44 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-02 10:21:44 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号, 数量
2025-08-02 10:21:44 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 10:21:44 | INFO     | gui.main_window:log_message:910 | 開始處理 4 個項目...
2025-08-02 10:21:44 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 8 到 11 (共 4 項)
2025-08-02 10:21:44 | INFO     | core.processing_engine:process_batch:379 | Writer AI: google-pro25, Reviewer AI: google-flash25
2025-08-02 10:21:44 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/4 (行 8)
2025-08-02 10:21:44 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '實用', '營養', '全方位', '舒適']
2025-08-02 10:21:44 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y172chair' 找到 0 張圖片
2025-08-02 10:21:45 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 14
}
]
2025-08-02 10:21:45 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: google-pro25, 錯誤: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 14
}
]
2025-08-02 10:21:45 | ERROR    | core.processing_engine:process_batch:399 | 項目處理失敗: Writer 階段失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 14
}
]
2025-08-02 10:21:45 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/4 (行 9)
2025-08-02 10:21:45 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '實用', '營養', '全方位', '舒適']
2025-08-02 10:21:45 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y173chair' 找到 0 張圖片
2025-08-02 10:21:46 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 13
}
]
2025-08-02 10:21:46 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: google-pro25, 錯誤: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 13
}
]
2025-08-02 10:21:46 | ERROR    | core.processing_engine:process_batch:399 | 項目處理失敗: Writer 階段失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 13
}
]
2025-08-02 10:21:46 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/4 (行 10)
2025-08-02 10:21:46 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['實用', '營養', '全方位', '舒適', '完整']
2025-08-02 10:21:46 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y174chair' 找到 0 張圖片
2025-08-02 10:21:46 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 13
}
]
2025-08-02 10:21:46 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: google-pro25, 錯誤: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 13
}
]
2025-08-02 10:21:46 | ERROR    | core.processing_engine:process_batch:399 | 項目處理失敗: Writer 階段失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 13
}
]
2025-08-02 10:21:46 | INFO     | core.processing_engine:process_batch:382 | 處理項目 4/4 (行 11)
2025-08-02 10:21:46 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['實用', '營養', '全方位', '舒適', '完整']
2025-08-02 10:21:46 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y200chair' 找到 0 張圖片
2025-08-02 10:21:47 | ERROR    | core.ai_models:generate_text:271 | Google API 呼叫失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 12
}
]
2025-08-02 10:21:47 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: google-pro25, 錯誤: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 12
}
]
2025-08-02 10:21:47 | ERROR    | core.processing_engine:process_batch:399 | 項目處理失敗: Writer 階段失敗: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 12
}
]
2025-08-02 10:21:47 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 0, 失敗 0
2025-08-02 10:21:47 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 10:21:47 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 10:21:47 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 10:21:47 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 0, 失敗: 4, 總 Token: 0, 預估費用: $0.000000
2025-08-02 10:21:58 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:22:02 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:22:04 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-02 10:22:04 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号, 数量
2025-08-02 10:22:04 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 10:22:04 | INFO     | gui.main_window:log_message:910 | 開始處理 4 個項目...
2025-08-02 10:22:04 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 8 到 11 (共 4 項)
2025-08-02 10:22:04 | INFO     | core.processing_engine:process_batch:379 | Writer AI: openai-gpt4o, Reviewer AI: anthropic-sonnet4
2025-08-02 10:22:04 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/4 (行 8)
2025-08-02 10:22:04 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 3)
2025-08-02 10:22:04 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['全方位', '高品質', '實用', '舒適', '品質']
2025-08-02 10:22:04 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y172chair' 找到 0 張圖片
2025-08-02 10:22:17 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 486 input + 491 output = 977 tokens, 成本: $0.000000
2025-08-02 10:22:17 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,977,0.0
2025-08-02 10:22:17 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 977, 時間: 13.10s
2025-08-02 10:22:21 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 1319 input + 88 output = 1407 tokens, 成本: $0.000000
2025-08-02 10:22:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1407,0.0
2025-08-02 10:22:21 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1407, 時間: 3.91s
2025-08-02 10:22:21 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: B2551... (17.02s)
2025-08-02 10:22:21 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/4 (行 9)
2025-08-02 10:22:21 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 3)
2025-08-02 10:22:21 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['全方位', '高品質', '實用', '舒適', '品質']
2025-08-02 10:22:21 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y173chair' 找到 0 張圖片
2025-08-02 10:22:34 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 471 input + 521 output = 992 tokens, 成本: $0.000000
2025-08-02 10:22:34 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,992,0.0
2025-08-02 10:22:34 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 992, 時間: 13.09s
2025-08-02 10:22:41 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 1354 input + 95 output = 1449 tokens, 成本: $0.000000
2025-08-02 10:22:41 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1449,0.0
2025-08-02 10:22:41 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1449, 時間: 6.84s
2025-08-02 10:22:41 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: B2550... (19.93s)
2025-08-02 10:22:41 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/4 (行 10)
2025-08-02 10:22:41 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 2)
2025-08-02 10:22:41 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['全方位', '實用', '舒適', '完整', '營養']
2025-08-02 10:22:41 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y174chair' 找到 0 張圖片
2025-08-02 10:22:57 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 458 input + 518 output = 976 tokens, 成本: $0.000000
2025-08-02 10:22:57 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,976,0.0
2025-08-02 10:22:57 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 976, 時間: 16.56s
2025-08-02 10:23:04 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 1343 input + 84 output = 1427 tokens, 成本: $0.000000
2025-08-02 10:23:04 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1427,0.0
2025-08-02 10:23:04 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1427, 時間: 6.47s
2025-08-02 10:23:04 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: 24-26#... (23.04s)
2025-08-02 10:23:04 | INFO     | core.processing_engine:process_batch:382 | 處理項目 4/4 (行 11)
2025-08-02 10:23:04 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 2)
2025-08-02 10:23:04 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['全方位', '實用', '舒適', '完整', '營養']
2025-08-02 10:23:04 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y200chair' 找到 0 張圖片
2025-08-02 10:23:19 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 459 input + 534 output = 993 tokens, 成本: $0.000000
2025-08-02 10:23:19 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,993,0.0
2025-08-02 10:23:19 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 993, 時間: 14.96s
2025-08-02 10:23:23 | INFO     | core.ai_models:generate_text:194 | Anthropic 使用記錄: 1309 input + 87 output = 1396 tokens, 成本: $0.000000
2025-08-02 10:23:23 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1396,0.0
2025-08-02 10:23:23 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1396, 時間: 3.95s
2025-08-02 10:23:23 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Y200... (18.92s)
2025-08-02 10:23:23 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 4, 失敗 0
2025-08-02 10:23:23 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 10:23:23 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 10:23:23 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 10:23:23 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 4, 失敗: 0, 總 Token: 9,617, 預估費用: $0.000000
2025-08-02 10:23:23 | INFO     | gui.main_window:log_message:910 | 自動顯示 B2551... 的預覽
2025-08-02 10:26:01 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 10:26:01 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 10:26:01 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 10:26:01 | INFO     | gui.main_window:log_message:910 | 自動設定處理範圍: 1 到 14 行
2025-08-02 10:26:01 | INFO     | gui.main_window:log_message:910 | 資料載入成功: 14 行, 7 欄
2025-08-02 10:26:01 | INFO     | gui.main_window:log_message:910 | 警告: 部分欄位有缺失值
2025-08-02 10:26:25 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 10:26:25 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 10:26:25 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 10:26:26 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 10:26:26 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 10:26:26 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 10:26:26 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 10:26:26 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 10:26:26 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 10:26:26 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 10:26:26 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 10:26:26 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 10:26:26 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 10:26:26 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 10:26:26 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-02 10:26:26 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:26:26 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:26:26 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:26:26 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:26:26 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-02 10:26:26 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 10:27:36 | INFO     | gui.main_window:log_message:910 | 選擇工作表: Sheet1
2025-08-02 10:27:36 | INFO     | gui.main_window:log_message:910 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-02 10:27:41 | INFO     | gui.main_window:log_message:910 | 圖片資料夾警告: 圖片目錄中沒有找到支援的圖片檔案
2025-08-02 10:27:46 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 10:27:46 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 10:27:46 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 10:27:46 | INFO     | gui.main_window:log_message:910 | 自動設定處理範圍: 1 到 14 行
2025-08-02 10:27:46 | INFO     | gui.main_window:log_message:910 | 資料載入成功: 14 行, 7 欄
2025-08-02 10:27:46 | INFO     | gui.main_window:log_message:910 | 警告: 部分欄位有缺失值
2025-08-02 10:27:50 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:27:54 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 10:28:07 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号']
2025-08-02 10:28:07 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号
2025-08-02 10:28:07 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 10:28:07 | INFO     | gui.main_window:log_message:910 | 開始處理 3 個項目...
2025-08-02 10:28:07 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 10 到 12 (共 3 項)
2025-08-02 10:28:07 | INFO     | core.processing_engine:process_batch:379 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 10:28:07 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/3 (行 10)
2025-08-02 10:28:07 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['守護', '簡單', '高品質', '認證', '節能']
2025-08-02 10:28:07 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y174chair' 找到 0 張圖片
2025-08-02 10:28:16 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 449 input + 519 output = 968 tokens, 成本: $0.000000
2025-08-02 10:28:16 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,968,0.0
2025-08-02 10:28:16 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 968, 時間: 9.04s
2025-08-02 10:28:17 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1031 input + 12 output = 1043 tokens, 成本: $0.000000
2025-08-02 10:28:17 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1043,0.0
2025-08-02 10:28:17 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1043, 時間: 1.61s
2025-08-02 10:28:17 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: 24-26#... (10.66s)
2025-08-02 10:28:17 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/3 (行 11)
2025-08-02 10:28:17 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['守護', '簡單', '高品質', '認證', '節能']
2025-08-02 10:28:17 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y200chair' 找到 0 張圖片
2025-08-02 10:28:25 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 449 input + 508 output = 957 tokens, 成本: $0.000000
2025-08-02 10:28:25 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,957,0.0
2025-08-02 10:28:25 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 957, 時間: 7.90s
2025-08-02 10:28:26 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1020 input + 9 output = 1029 tokens, 成本: $0.000000
2025-08-02 10:28:26 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1029,0.0
2025-08-02 10:28:26 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1029, 時間: 0.98s
2025-08-02 10:28:26 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Y200... (8.89s)
2025-08-02 10:28:26 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/3 (行 12)
2025-08-02 10:28:26 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '守護', '簡單', '認證', '節能']
2025-08-02 10:28:26 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y176chair' 找到 0 張圖片
2025-08-02 10:28:35 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 449 input + 529 output = 978 tokens, 成本: $0.000000
2025-08-02 10:28:35 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,978,0.0
2025-08-02 10:28:35 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 978, 時間: 8.75s
2025-08-02 10:28:36 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1063 input + 9 output = 1072 tokens, 成本: $0.000000
2025-08-02 10:28:36 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1072,0.0
2025-08-02 10:28:36 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1072, 時間: 0.74s
2025-08-02 10:28:36 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Y-171... (9.49s)
2025-08-02 10:28:36 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 3, 失敗 0
2025-08-02 10:28:36 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 10:28:36 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 10:28:36 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 10:28:36 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 3, 失敗: 0, 總 Token: 6,047, 預估費用: $0.000000
2025-08-02 10:28:36 | INFO     | gui.main_window:log_message:910 | 自動顯示 24-26#... 的預覽
2025-08-02 10:50:24 | INFO     | core.prompt_manager:add_writer_prompt:214 | 新增 Writer Prompt: pharmacy
2025-08-02 10:50:24 | INFO     | gui.main_window:log_message:910 | 已儲存 Writer Prompt: pharmacy
2025-08-02 10:52:46 | INFO     | core.prompt_manager:add_reviewer_prompt:241 | 新增 Reviewer Prompt: standard
2025-08-02 10:52:46 | INFO     | gui.main_window:log_message:910 | 已儲存 Reviewer Prompt: standard
2025-08-02 10:54:17 | INFO     | core.prompt_manager:add_writer_prompt:214 | 新增 Writer Prompt: furniture
2025-08-02 10:54:17 | INFO     | gui.main_window:log_message:910 | 已儲存 Writer Prompt: furniture
2025-08-02 10:55:39 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 10:55:39 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 10:55:40 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 10:55:40 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 10:55:40 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 10:55:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 10:55:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 10:55:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 10:55:40 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 10:55:40 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 10:55:40 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 10:55:40 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 10:55:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 10:55:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 10:55:40 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-02 10:55:41 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:55:41 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:55:41 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:55:41 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:55:41 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-02 10:55:41 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 10:55:50 | INFO     | gui.main_window:log_message:910 | 選擇工作表: Sheet1
2025-08-02 10:55:50 | INFO     | gui.main_window:log_message:910 | 已選擇 Excel 檔案: sample_products.xlsx
2025-08-02 10:55:52 | INFO     | gui.main_window:log_message:910 | 選擇工作表: Sheet1
2025-08-02 10:55:52 | INFO     | gui.main_window:log_message:910 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-02 10:55:55 | INFO     | gui.main_window:log_message:910 | 圖片資料夾警告: 圖片目錄中沒有找到支援的圖片檔案
2025-08-02 10:55:58 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 10:55:58 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 10:55:58 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 10:55:58 | INFO     | gui.main_window:log_message:910 | 自動設定處理範圍: 1 到 14 行
2025-08-02 10:55:58 | INFO     | gui.main_window:log_message:910 | 資料載入成功: 14 行, 7 欄
2025-08-02 10:55:58 | INFO     | gui.main_window:log_message:910 | 警告: 部分欄位有缺失值
2025-08-02 10:56:34 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 10:56:35 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 10:56:37 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 10:56:49 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-02 10:56:49 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号, 数量
2025-08-02 10:56:49 | INFO     | gui.main_window:log_message:910 | 開始處理 2 個項目...
2025-08-02 10:56:49 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 3 到 4 (共 2 項)
2025-08-02 10:56:49 | INFO     | core.processing_engine:process_batch:379 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 10:56:49 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/2 (行 3)
2025-08-02 10:56:49 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '親膚', '多功能', '健康', '無人工色素']
2025-08-02 10:57:00 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 540 input + 465 output = 1005 tokens, 成本: $0.000000
2025-08-02 10:57:00 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1005,0.0
2025-08-02 10:57:00 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1005, 時間: 10.83s
2025-08-02 10:57:03 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1029 input + 83 output = 1112 tokens, 成本: $0.000000
2025-08-02 10:57:03 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1112,0.0
2025-08-02 10:57:03 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1112, 時間: 3.10s
2025-08-02 10:57:03 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2368... (13.93s)
2025-08-02 10:57:03 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/2 (行 4)
2025-08-02 10:57:03 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['親膚', '多功能', '可持續', '健康', '無人工色素']
2025-08-02 10:57:14 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 540 input + 434 output = 974 tokens, 成本: $0.000000
2025-08-02 10:57:14 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,974,0.0
2025-08-02 10:57:14 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 974, 時間: 11.71s
2025-08-02 10:57:21 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1000 input + 89 output = 1089 tokens, 成本: $0.000000
2025-08-02 10:57:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1089,0.0
2025-08-02 10:57:21 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1089, 時間: 6.19s
2025-08-02 10:57:21 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2556... (17.90s)
2025-08-02 10:57:21 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 2, 失敗 0
2025-08-02 10:57:21 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 10:57:21 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 10:57:21 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 10:57:21 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 2, 失敗: 0, 總 Token: 4,180, 預估費用: $0.000000
2025-08-02 10:57:21 | INFO     | gui.main_window:log_message:910 | 自動顯示 A2368... 的預覽
2025-08-02 10:59:56 | INFO     | core.prompt_manager:add_reviewer_prompt:241 | 新增 Reviewer Prompt: standard
2025-08-02 10:59:56 | INFO     | gui.main_window:log_message:910 | 已儲存 Reviewer Prompt: standard
2025-08-02 11:02:29 | INFO     | core.prompt_manager:add_writer_prompt:214 | 新增 Writer Prompt: furniture
2025-08-02 11:02:29 | INFO     | gui.main_window:log_message:910 | 已儲存 Writer Prompt: furniture
2025-08-02 11:03:36 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 11:03:36 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 11:03:37 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 11:03:37 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 11:03:37 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 11:03:37 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 11:03:37 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 11:03:37 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 11:03:37 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 11:03:37 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 11:03:37 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 11:03:37 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 11:03:37 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 11:03:37 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 11:03:37 | INFO     | gui.main_window:init_components:146 | 核心組件初始化完成
2025-08-02 11:03:38 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:03:38 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:03:38 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:03:38 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:03:38 | INFO     | gui.main_window:__init__:133 | 主視窗初始化完成
2025-08-02 11:03:38 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 11:03:52 | INFO     | gui.main_window:log_message:910 | 選擇工作表: Sheet1
2025-08-02 11:03:52 | INFO     | gui.main_window:log_message:910 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-02 11:03:55 | INFO     | gui.main_window:log_message:910 | 圖片資料夾警告: 圖片目錄中沒有找到支援的圖片檔案
2025-08-02 11:04:02 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 11:04:02 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 11:04:02 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 11:04:02 | INFO     | gui.main_window:log_message:910 | 自動設定處理範圍: 1 到 14 行
2025-08-02 11:04:02 | INFO     | gui.main_window:log_message:910 | 資料載入成功: 14 行, 7 欄
2025-08-02 11:04:02 | INFO     | gui.main_window:log_message:910 | 警告: 部分欄位有缺失值
2025-08-02 11:04:14 | INFO     | gui.main_window:log_message:910 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:04:16 | INFO     | gui.main_window:log_message:910 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 11:04:28 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号', '数量']
2025-08-02 11:04:28 | INFO     | gui.main_window:log_message:910 | 排除欄位: 序号, 数量
2025-08-02 11:04:28 | INFO     | gui.main_window:log_message:910 | 開始處理 3 個項目...
2025-08-02 11:04:28 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 0 到 2 (共 3 項)
2025-08-02 11:04:28 | INFO     | core.processing_engine:process_batch:379 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 11:04:28 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/3 (行 0)
2025-08-02 11:04:28 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['節能', '舒適', '均衡', '經典', '滋養']
2025-08-02 11:04:34 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 617 input + 322 output = 939 tokens, 成本: $0.000000
2025-08-02 11:04:34 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,939,0.0
2025-08-02 11:04:34 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 939, 時間: 6.13s
2025-08-02 11:04:39 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 817 input + 217 output = 1034 tokens, 成本: $0.000000
2025-08-02 11:04:39 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1034,0.0
2025-08-02 11:04:39 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1034, 時間: 4.71s
2025-08-02 11:04:39 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A309... (10.84s)
2025-08-02 11:04:39 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/3 (行 1)
2025-08-02 11:04:39 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['節能', '舒適', '均衡', '經典', '滋養']
2025-08-02 11:04:46 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 617 input + 306 output = 923 tokens, 成本: $0.000000
2025-08-02 11:04:46 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,923,0.0
2025-08-02 11:04:46 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 923, 時間: 6.59s
2025-08-02 11:04:49 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 833 input + 140 output = 973 tokens, 成本: $0.000000
2025-08-02 11:04:49 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,973,0.0
2025-08-02 11:04:49 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 973, 時間: 3.16s
2025-08-02 11:04:49 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2423... (9.75s)
2025-08-02 11:04:49 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/3 (行 2)
2025-08-02 11:04:49 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['節能', '舒適', '均衡', '經典', '滋養']
2025-08-02 11:04:57 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 617 input + 357 output = 974 tokens, 成本: $0.000000
2025-08-02 11:04:57 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,974,0.0
2025-08-02 11:04:57 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 974, 時間: 7.99s
2025-08-02 11:05:03 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 877 input + 295 output = 1172 tokens, 成本: $0.000000
2025-08-02 11:05:03 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1172,0.0
2025-08-02 11:05:03 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1172, 時間: 5.77s
2025-08-02 11:05:03 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2561-18... (13.76s)
2025-08-02 11:05:03 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 3, 失敗 0
2025-08-02 11:05:03 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 11:05:03 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 11:05:03 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 11:05:03 | INFO     | gui.main_window:log_message:910 | 處理完成！成功: 3, 失敗: 0, 總 Token: 6,015, 預估費用: $0.000000
2025-08-02 11:05:03 | INFO     | gui.main_window:log_message:910 | 自動顯示 A309... 的預覽
2025-08-02 11:09:17 | INFO     | gui.main_window:log_message:910 | 顯示第 1 行的 HTML 預覽和 Reviewer 結果
2025-08-02 11:09:17 | INFO     | gui.main_window:log_message:910 | 顯示第 1 行的 HTML 預覽和 Reviewer 結果
2025-08-02 11:15:20 | INFO     | gui.main_window:log_message:910 | 顯示第 2 行的 HTML 預覽和 Reviewer 結果
2025-08-02 11:15:20 | INFO     | gui.main_window:log_message:910 | 顯示第 2 行的 HTML 預覽和 Reviewer 結果
2025-08-02 11:16:41 | INFO     | core.prompt_manager:add_writer_prompt:214 | 新增 Writer Prompt: furniture
2025-08-02 11:16:41 | INFO     | gui.main_window:log_message:910 | 已儲存 Writer Prompt: furniture
2025-08-02 11:17:08 | INFO     | core.prompt_manager:add_reviewer_prompt:241 | 新增 Reviewer Prompt: standard
2025-08-02 11:17:08 | INFO     | gui.main_window:log_message:910 | 已儲存 Reviewer Prompt: standard
2025-08-02 11:58:11 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 11:58:11 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 11:58:11 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 11:58:11 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 11:58:12 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 11:58:13 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 11:58:13 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 11:58:13 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 11:58:13 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 11:58:13 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 11:58:13 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 11:58:13 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 11:58:13 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 11:58:13 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 11:58:13 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 11:58:13 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 11:58:13 | INFO     | gui.main_window:init_components:160 | 核心組件初始化完成
2025-08-02 11:58:14 | INFO     | gui.main_window:log_message:991 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:58:14 | INFO     | gui.main_window:log_message:991 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:58:14 | INFO     | gui.main_window:log_message:991 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:58:14 | INFO     | gui.main_window:log_message:991 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:58:14 | INFO     | gui.main_window:log_message:991 | ✅ 設定已載入
2025-08-02 11:58:14 | INFO     | gui.main_window:__init__:147 | 主視窗初始化完成
2025-08-02 11:58:14 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 11:58:21 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 11:58:25 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 11:58:25 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 11:58:25 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 11:58:25 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 11:58:32 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 11:58:32 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 11:58:32 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 11:58:32 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 11:58:33 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 11:58:34 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 11:58:34 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 11:58:34 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 11:58:34 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 11:58:34 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 11:58:34 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 11:58:34 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 11:58:34 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 11:58:34 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 11:58:34 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 11:58:34 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 11:58:34 | INFO     | gui.main_window:init_components:160 | 核心組件初始化完成
2025-08-02 11:58:35 | INFO     | gui.main_window:log_message:991 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:58:35 | INFO     | gui.main_window:log_message:991 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:58:35 | INFO     | gui.main_window:log_message:991 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:58:35 | INFO     | gui.main_window:log_message:991 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:58:35 | INFO     | gui.main_window:log_message:991 | ✅ 設定已載入
2025-08-02 11:58:35 | INFO     | gui.main_window:__init__:147 | 主視窗初始化完成
2025-08-02 11:58:35 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 11:59:06 | INFO     | gui.main_window:log_message:991 | 選擇工作表: Sheet1
2025-08-02 11:59:06 | INFO     | gui.main_window:log_message:991 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-02 11:59:30 | INFO     | gui.main_window:log_message:991 | 圖片資料夾警告: 圖片目錄中沒有找到支援的圖片檔案
2025-08-02 11:59:40 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 11:59:40 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 11:59:40 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 11:59:40 | INFO     | gui.main_window:log_message:991 | 自動設定處理範圍: 1 到 14 行
2025-08-02 11:59:40 | INFO     | gui.main_window:log_message:991 | 資料載入成功: 14 行, 7 欄
2025-08-02 11:59:40 | INFO     | gui.main_window:log_message:991 | 警告: 部分欄位有缺失值
2025-08-02 11:59:56 | INFO     | gui.main_window:log_message:991 | 選擇 Writer AI 模型: Gemini 2.5 Pro | Google | ✅強大圖片 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 11:59:58 | INFO     | gui.main_window:log_message:991 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 12:00:05 | INFO     | gui.main_window:log_message:991 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 12:00:14 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 12:00:14 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 12:00:14 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 12:00:15 | INFO     | gui.main_window:log_message:991 | 自動設定處理範圍: 1 到 14 行
2025-08-02 12:00:15 | INFO     | gui.main_window:log_message:991 | 資料載入成功: 14 行, 7 欄
2025-08-02 12:00:15 | INFO     | gui.main_window:log_message:991 | 警告: 部分欄位有缺失值
2025-08-02 12:00:20 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号']
2025-08-02 12:00:20 | INFO     | gui.main_window:log_message:991 | 排除欄位: 序号
2025-08-02 12:00:20 | INFO     | gui.main_window:log_message:991 | 開始處理 2 個項目...
2025-08-02 12:00:20 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 1 到 2 (共 2 項)
2025-08-02 12:00:20 | INFO     | core.processing_engine:process_batch:379 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 12:00:20 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/2 (行 1)
2025-08-02 12:00:20 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['守護', '無添加', '全方位', '豐富', '無人工色素']
2025-08-02 12:00:26 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 639 input + 176 output = 815 tokens, 成本: $0.000000
2025-08-02 12:00:26 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,815,0.0
2025-08-02 12:00:26 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 815, 時間: 6.17s
2025-08-02 12:00:43 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 729 input + 252 output = 981 tokens, 成本: $0.000000
2025-08-02 12:00:43 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,981,0.0
2025-08-02 12:00:43 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 981, 時間: 16.19s
2025-08-02 12:00:43 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2423... (22.37s)
2025-08-02 12:00:43 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/2 (行 2)
2025-08-02 12:00:43 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['守護', '無添加', '全方位', '豐富', '完整']
2025-08-02 12:00:50 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 639 input + 184 output = 823 tokens, 成本: $0.000000
2025-08-02 12:00:50 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,823,0.0
2025-08-02 12:00:50 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 823, 時間: 7.13s
2025-08-02 12:01:00 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 734 input + 180 output = 914 tokens, 成本: $0.000000
2025-08-02 12:01:00 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,914,0.0
2025-08-02 12:01:00 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 914, 時間: 10.23s
2025-08-02 12:01:00 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2561-18... (17.36s)
2025-08-02 12:01:00 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 2, 失敗 0
2025-08-02 12:01:00 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 12:01:00 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 12:01:00 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 12:01:00 | INFO     | gui.main_window:log_message:991 | 處理完成！成功: 2, 失敗: 0, 總 Token: 3,533, 預估費用: $0.000000
2025-08-02 12:01:00 | INFO     | gui.main_window:log_message:991 | 自動顯示 A2423... 的預覽
2025-08-02 12:21:58 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 12:21:58 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 12:21:58 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 12:21:58 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 12:21:58 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 12:21:58 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:00:33 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 13:00:33 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 13:00:33 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 13:00:33 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 13:00:34 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 13:00:34 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 13:00:34 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 13:00:34 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 13:00:34 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 13:00:34 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 13:00:34 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 13:00:34 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 13:00:34 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 13:00:34 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 13:00:34 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 13:00:34 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 13:00:34 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 13:00:35 | INFO     | gui.main_window:log_message:1033 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:00:35 | INFO     | gui.main_window:log_message:1033 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:00:35 | INFO     | gui.main_window:log_message:1033 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:00:35 | INFO     | gui.main_window:log_message:1033 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:00:35 | INFO     | gui.main_window:log_message:1033 | ✅ 設定已載入
2025-08-02 13:00:35 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 13:00:35 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 13:00:35 | INFO     | gui.main_window:log_message:1033 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:00:35 | INFO     | gui.main_window:log_message:1033 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 13:00:46 | INFO     | gui.main_window:log_message:1033 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:00:46 | INFO     | gui.main_window:log_message:1033 | 選擇 Reviewer AI 模型: Gemini 2.5 Pro | Google | ✅強大圖片 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:01:13 | INFO     | gui.main_window:log_message:1033 | 選擇工作表: Sheet1
2025-08-02 13:01:13 | INFO     | gui.main_window:log_message:1033 | 已選擇 Excel 檔案: Trizenith Product detail Raw data.xlsx
2025-08-02 13:01:18 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 13:01:18 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 13:01:18 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 13:01:18 | INFO     | gui.main_window:log_message:1033 | 自動設定處理範圍: 1 到 14 行
2025-08-02 13:01:18 | INFO     | gui.main_window:log_message:1033 | 資料載入成功: 14 行, 7 欄
2025-08-02 13:01:18 | INFO     | gui.main_window:log_message:1033 | 警告: 部分欄位有缺失值
2025-08-02 13:01:49 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:01:52 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:01:52 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:01:52 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:01:52 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:01:52 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:01:52 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:01:57 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 13:01:57 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 13:01:57 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 13:01:57 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 13:01:57 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 13:01:58 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 13:01:58 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 13:01:58 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 13:01:58 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 13:01:58 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 13:01:58 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 13:01:58 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 13:01:58 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 13:01:58 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 13:01:58 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 13:01:58 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 13:01:58 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 13:01:58 | INFO     | gui.main_window:log_message:1033 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:01:58 | INFO     | gui.main_window:log_message:1033 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:01:58 | INFO     | gui.main_window:log_message:1033 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:01:58 | INFO     | gui.main_window:log_message:1033 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:01:58 | INFO     | gui.main_window:log_message:1033 | ✅ 設定已載入
2025-08-02 13:01:58 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 13:01:59 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 13:01:59 | INFO     | gui.main_window:log_message:1033 | 選擇 Reviewer AI 模型: Gemini 2.5 Pro | Google | ✅強大圖片 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:01:59 | INFO     | gui.main_window:log_message:1033 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:05:44 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:05:44 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:05:44 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:05:44 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:05:44 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:05:44 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:18:05 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 13:18:05 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 13:18:05 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 13:18:05 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 13:18:06 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 13:18:06 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 13:18:06 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 13:18:06 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 13:18:06 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 13:18:06 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 13:18:06 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 13:18:06 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 13:18:06 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 13:18:06 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 13:18:06 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 13:18:06 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 13:18:06 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 13:18:08 | INFO     | gui.main_window:log_message:1118 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:18:08 | INFO     | gui.main_window:log_message:1118 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:18:08 | INFO     | gui.main_window:log_message:1118 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:18:08 | INFO     | gui.main_window:log_message:1118 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:18:08 | INFO     | gui.main_window:log_message:1118 | 選擇 Writer Prompt: default
2025-08-02 13:18:08 | INFO     | gui.main_window:log_message:1118 | 選擇 Reviewer Prompt: standard
2025-08-02 13:18:08 | INFO     | gui.main_window:log_message:1118 | 選擇工作表: Sheet1
2025-08-02 13:18:08 | INFO     | gui.main_window:log_message:1118 | ✅ 設定已載入
2025-08-02 13:18:08 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 13:18:08 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 13:18:49 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:18:49 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:18:49 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:18:49 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:18:49 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:18:49 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:20:20 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 13:20:20 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 13:20:20 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 13:20:20 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 13:20:20 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 13:20:21 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 13:20:21 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 13:20:21 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 13:20:21 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 13:20:21 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 13:20:21 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 13:20:21 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 13:20:21 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 13:20:21 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 13:20:21 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 13:20:21 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 13:20:21 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 13:20:22 | INFO     | gui.main_window:log_message:1118 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:20:22 | INFO     | gui.main_window:log_message:1118 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:20:22 | INFO     | gui.main_window:log_message:1118 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:20:22 | INFO     | gui.main_window:log_message:1118 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:20:22 | INFO     | gui.main_window:log_message:1118 | 選擇 Writer Prompt: default
2025-08-02 13:20:22 | INFO     | gui.main_window:log_message:1118 | 選擇 Reviewer Prompt: standard
2025-08-02 13:20:22 | INFO     | gui.main_window:log_message:1118 | 選擇工作表: Sheet1
2025-08-02 13:20:22 | INFO     | gui.main_window:log_message:1118 | ✅ 設定已載入
2025-08-02 13:20:22 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 13:20:22 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 13:20:57 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:20:57 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:20:57 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:20:57 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:20:57 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:20:57 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:22:53 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 13:22:53 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 13:22:53 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 13:22:53 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 13:22:54 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 13:22:54 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 13:22:54 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 13:22:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 13:22:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 13:22:54 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 13:22:54 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 13:22:54 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 13:22:54 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 13:22:54 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 13:22:54 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 13:22:54 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 13:22:54 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 13:22:54 | ERROR    | __main__:main:56 | 程式啟動失敗: 'MainWindow' object has no attribute 'status_text'
2025-08-02 13:23:54 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 13:23:54 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 13:23:54 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 13:23:54 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 13:23:55 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 13:23:56 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 13:23:56 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 13:23:56 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 13:23:56 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 13:23:56 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 13:23:56 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 13:23:56 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 13:23:56 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 13:23:56 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 13:23:56 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 13:23:56 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 13:23:56 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 13:23:57 | INFO     | gui.main_window:log_message:1151 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:23:57 | INFO     | gui.main_window:log_message:1151 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:23:57 | INFO     | gui.main_window:log_message:1151 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:23:57 | INFO     | gui.main_window:log_message:1151 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:23:57 | INFO     | gui.main_window:log_message:1151 | 選擇 Writer Prompt: default
2025-08-02 13:23:57 | INFO     | gui.main_window:log_message:1151 | 選擇 Reviewer Prompt: standard
2025-08-02 13:23:58 | INFO     | gui.main_window:log_message:1151 | 選擇工作表: Sheet1
2025-08-02 13:23:58 | INFO     | gui.main_window:log_message:1151 | ✅ 設定已載入
2025-08-02 13:23:58 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 13:23:58 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 13:24:27 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:24:27 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:24:27 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:24:27 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:24:27 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:24:27 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:28:56 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 13:28:56 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 13:28:56 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 13:28:56 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 13:28:57 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 13:28:58 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 13:28:58 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 13:28:58 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 13:28:58 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 13:28:58 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 13:28:58 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 13:28:58 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 13:28:58 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 13:28:58 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 13:28:58 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 13:28:58 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 13:28:58 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 13:28:59 | INFO     | gui.main_window:log_message:1171 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:28:59 | INFO     | gui.main_window:log_message:1171 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:28:59 | INFO     | gui.main_window:log_message:1171 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:28:59 | INFO     | gui.main_window:log_message:1171 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:28:59 | INFO     | gui.main_window:log_message:1171 | 選擇 Writer Prompt: default
2025-08-02 13:28:59 | INFO     | gui.main_window:log_message:1171 | 選擇 Reviewer Prompt: standard
2025-08-02 13:28:59 | INFO     | gui.main_window:log_message:1171 | 選擇工作表: Sheet1
2025-08-02 13:28:59 | INFO     | gui.main_window:log_message:1171 | ✅ 設定已載入
2025-08-02 13:28:59 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 13:29:00 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 13:31:08 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 13:31:08 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 13:31:08 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 13:31:08 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 13:31:10 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 13:31:10 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 13:31:10 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 13:31:10 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 13:31:10 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 13:31:10 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 13:31:10 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 13:31:10 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 13:31:10 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 13:31:10 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 13:31:10 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 13:31:10 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 13:31:10 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 13:31:11 | INFO     | gui.main_window:log_message:1177 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:31:11 | INFO     | gui.main_window:log_message:1177 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:31:11 | INFO     | gui.main_window:log_message:1177 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:31:11 | INFO     | gui.main_window:log_message:1177 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:31:11 | INFO     | gui.main_window:log_message:1177 | 選擇 Writer Prompt: default
2025-08-02 13:31:11 | INFO     | gui.main_window:log_message:1177 | 選擇 Reviewer Prompt: standard
2025-08-02 13:31:11 | INFO     | gui.main_window:log_message:1177 | 選擇工作表: Sheet1
2025-08-02 13:31:11 | INFO     | gui.main_window:log_message:1177 | ✅ 設定已載入
2025-08-02 13:31:11 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 13:31:13 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 13:38:08 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 13:38:08 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 13:38:08 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 13:38:08 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 13:38:09 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 13:38:09 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 13:38:09 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 13:38:09 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 13:38:09 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 13:38:09 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 13:38:09 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 13:38:09 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 13:38:09 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 13:38:09 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 13:38:09 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 13:38:09 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 13:38:09 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 13:38:10 | INFO     | gui.main_window:log_message:1177 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:38:10 | INFO     | gui.main_window:log_message:1177 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:38:10 | INFO     | gui.main_window:log_message:1177 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:38:10 | INFO     | gui.main_window:log_message:1177 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:38:10 | INFO     | gui.main_window:log_message:1177 | 選擇 Writer Prompt: default
2025-08-02 13:38:10 | INFO     | gui.main_window:log_message:1177 | 選擇 Reviewer Prompt: standard
2025-08-02 13:38:11 | INFO     | gui.main_window:log_message:1177 | 選擇工作表: Sheet1
2025-08-02 13:38:11 | INFO     | gui.main_window:log_message:1177 | ✅ 設定已載入
2025-08-02 13:38:11 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 13:38:12 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 13:38:21 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 13:38:21 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 13:38:21 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 13:38:21 | INFO     | gui.main_window:log_message:1177 | 選擇圖片欄位: 無
2025-08-02 13:38:21 | INFO     | gui.main_window:log_message:1177 | 選擇 SEO 欄位: 無
2025-08-02 13:38:21 | INFO     | gui.main_window:log_message:1177 | 選擇產品名稱欄位: 無
2025-08-02 13:38:21 | INFO     | gui.main_window:log_message:1177 | 自動設定處理範圍: 1 到 14 行
2025-08-02 13:38:21 | INFO     | gui.main_window:log_message:1177 | 資料載入成功: 14 行, 7 欄
2025-08-02 13:38:21 | INFO     | gui.main_window:log_message:1177 | 警告: 部分欄位有缺失值
2025-08-02 13:38:26 | INFO     | gui.main_window:log_message:1177 | 選擇圖片欄位: 产品图片                             
2025-08-02 13:38:33 | INFO     | gui.main_window:log_message:1177 | 選擇產品名稱欄位: 名称
2025-08-02 13:38:42 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:38:42 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:38:42 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:38:42 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:38:42 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:38:42 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:38:43 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:38:43 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:38:43 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:38:43 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:38:43 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:38:43 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:43:56 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 13:43:56 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 13:43:56 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 13:43:56 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 13:43:57 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 13:43:58 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 13:43:58 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 13:43:58 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 13:43:58 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 13:43:58 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 13:43:58 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 13:43:58 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 13:43:58 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 13:43:58 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 13:43:58 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 13:43:58 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 13:43:58 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 13:43:59 | INFO     | gui.main_window:log_message:1177 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:43:59 | INFO     | gui.main_window:log_message:1177 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:43:59 | INFO     | gui.main_window:log_message:1177 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:43:59 | INFO     | gui.main_window:log_message:1177 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:43:59 | INFO     | gui.main_window:log_message:1177 | 選擇 Writer Prompt: default
2025-08-02 13:43:59 | INFO     | gui.main_window:log_message:1177 | 選擇 Reviewer Prompt: standard
2025-08-02 13:43:59 | INFO     | gui.main_window:log_message:1177 | 選擇工作表: Sheet1
2025-08-02 13:43:59 | INFO     | gui.main_window:log_message:1177 | ✅ 設定已載入
2025-08-02 13:43:59 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 13:44:00 | INFO     | __main__:main:59 | GUI 介面已啟動
2025-08-02 13:46:31 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 13:46:31 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 13:46:31 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 13:46:31 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 13:46:31 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 13:46:32 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 13:46:32 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 13:46:32 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 13:46:32 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 13:46:32 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 13:46:32 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 13:46:32 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 13:46:32 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 13:46:32 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 13:46:32 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 13:46:32 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 13:46:32 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 13:46:32 | INFO     | gui.main_window:log_message:1169 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:46:32 | INFO     | gui.main_window:log_message:1169 | 選擇 Reviewer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:46:32 | INFO     | gui.main_window:log_message:1169 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:46:32 | INFO     | gui.main_window:log_message:1169 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:46:32 | INFO     | gui.main_window:log_message:1169 | 選擇 Writer Prompt: default
2025-08-02 13:46:32 | INFO     | gui.main_window:log_message:1169 | 選擇 Reviewer Prompt: standard
2025-08-02 13:46:33 | INFO     | gui.main_window:log_message:1169 | 選擇工作表: Sheet1
2025-08-02 13:46:33 | INFO     | gui.main_window:log_message:1169 | ✅ 設定已載入
2025-08-02 13:46:33 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 13:46:33 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 13:47:06 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 13:47:06 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 13:47:06 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 13:47:06 | INFO     | gui.main_window:log_message:1169 | 選擇圖片欄位: 無
2025-08-02 13:47:06 | INFO     | gui.main_window:log_message:1169 | 選擇 SEO 欄位: 無
2025-08-02 13:47:06 | INFO     | gui.main_window:log_message:1169 | 選擇產品名稱欄位: 無
2025-08-02 13:47:06 | INFO     | gui.main_window:log_message:1169 | 自動設定處理範圍: 1 到 14 行
2025-08-02 13:47:06 | INFO     | gui.main_window:log_message:1169 | 資料載入成功: 14 行, 7 欄
2025-08-02 13:47:06 | INFO     | gui.main_window:log_message:1169 | 警告: 部分欄位有缺失值
2025-08-02 13:47:17 | INFO     | gui.main_window:log_message:1169 | 選擇產品名稱欄位: 名称
2025-08-02 13:47:19 | INFO     | gui.main_window:log_message:1169 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:47:20 | INFO     | gui.main_window:log_message:1169 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 13:47:52 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号']
2025-08-02 13:47:52 | INFO     | gui.main_window:log_message:1169 | 排除欄位: 序号
2025-08-02 13:47:52 | INFO     | gui.main_window:log_message:1169 | 開始處理 4 個項目...
2025-08-02 13:47:52 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 4 到 7 (共 4 項)
2025-08-02 13:47:52 | INFO     | core.processing_engine:process_batch:379 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 13:47:52 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/4 (行 4)
2025-08-02 13:47:52 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['均衡', '滋養', '無香料', '持久', '即時']
2025-08-02 13:48:07 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 475 input + 570 output = 1045 tokens, 成本: $0.000000
2025-08-02 13:48:07 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1045,0.0
2025-08-02 13:48:07 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1045, 時間: 15.84s
2025-08-02 13:48:14 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1139 input + 310 output = 1449 tokens, 成本: $0.000000
2025-08-02 13:48:14 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1449,0.0
2025-08-02 13:48:14 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1449, 時間: 6.56s
2025-08-02 13:48:14 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2556... (22.40s)
2025-08-02 13:48:14 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/4 (行 5)
2025-08-02 13:48:14 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '均衡', '滋養', '無香料', '持久']
2025-08-02 13:48:28 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 447 input + 519 output = 966 tokens, 成本: $0.000000
2025-08-02 13:48:28 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,966,0.0
2025-08-02 13:48:28 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 966, 時間: 13.78s
2025-08-02 13:48:31 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1056 input + 130 output = 1186 tokens, 成本: $0.000000
2025-08-02 13:48:31 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1186,0.0
2025-08-02 13:48:31 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1186, 時間: 3.44s
2025-08-02 13:48:31 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Y168... (17.22s)
2025-08-02 13:48:31 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/4 (行 6)
2025-08-02 13:48:31 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['均衡', '滋養', '無香料', '持久', '即時']
2025-08-02 13:48:44 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 416 input + 494 output = 910 tokens, 成本: $0.000000
2025-08-02 13:48:44 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,910,0.0
2025-08-02 13:48:44 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 910, 時間: 12.98s
2025-08-02 13:48:50 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1023 input + 284 output = 1307 tokens, 成本: $0.000000
2025-08-02 13:48:50 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1307,0.0
2025-08-02 13:48:50 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1307, 時間: 5.76s
2025-08-02 13:48:50 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Y169... (18.74s)
2025-08-02 13:48:50 | INFO     | core.processing_engine:process_batch:382 | 處理項目 4/4 (行 7)
2025-08-02 13:48:50 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['均衡', '滋養', '無香料', '持久', '即時']
2025-08-02 13:49:03 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 431 input + 483 output = 914 tokens, 成本: $0.000000
2025-08-02 13:49:03 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,914,0.0
2025-08-02 13:49:03 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 914, 時間: 13.02s
2025-08-02 13:49:09 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1005 input + 170 output = 1175 tokens, 成本: $0.000000
2025-08-02 13:49:09 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1175,0.0
2025-08-02 13:49:09 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1175, 時間: 5.91s
2025-08-02 13:49:09 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: 24-48... (18.94s)
2025-08-02 13:49:09 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 4, 失敗 0
2025-08-02 13:49:09 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 13:49:09 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 13:49:09 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 13:49:09 | INFO     | gui.main_window:log_message:1169 | 處理完成！成功: 4, 失敗: 0, 總 Token: 8,952, 預估費用: $0.000000
2025-08-02 13:49:09 | INFO     | gui.main_window:log_message:1169 | 自動顯示 A2556... 的預覽
2025-08-02 13:54:44 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:54:44 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:54:44 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:54:44 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:54:44 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:54:44 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 13:59:23 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 13:59:23 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 13:59:23 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 13:59:23 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 13:59:24 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 13:59:24 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 13:59:24 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 13:59:24 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 13:59:24 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 13:59:24 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 13:59:24 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 13:59:24 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 13:59:24 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 13:59:24 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 13:59:24 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 13:59:24 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 13:59:24 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 13:59:25 | INFO     | gui.main_window:log_message:1189 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:59:25 | INFO     | gui.main_window:log_message:1189 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-02 13:59:25 | INFO     | gui.main_window:log_message:1189 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:59:25 | INFO     | gui.main_window:log_message:1189 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 13:59:25 | INFO     | gui.main_window:log_message:1189 | 選擇 Writer Prompt: default
2025-08-02 13:59:25 | INFO     | gui.main_window:log_message:1189 | 選擇 Reviewer Prompt: standard
2025-08-02 13:59:25 | INFO     | gui.main_window:log_message:1189 | 選擇工作表: Sheet1
2025-08-02 13:59:25 | INFO     | gui.main_window:log_message:1189 | ✅ 設定已載入
2025-08-02 13:59:25 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 13:59:26 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 14:02:52 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 14:02:52 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 14:02:52 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 14:02:52 | INFO     | gui.main_window:log_message:1189 | 選擇圖片欄位: 無
2025-08-02 14:02:52 | INFO     | gui.main_window:log_message:1189 | 選擇 SEO 欄位: 無
2025-08-02 14:02:52 | INFO     | gui.main_window:log_message:1189 | 選擇產品名稱欄位: 無
2025-08-02 14:02:52 | INFO     | gui.main_window:log_message:1189 | 自動設定處理範圍: 1 到 14 行
2025-08-02 14:02:52 | INFO     | gui.main_window:log_message:1189 | 資料載入成功: 14 行, 7 欄
2025-08-02 14:02:52 | INFO     | gui.main_window:log_message:1189 | 警告: 部分欄位有缺失值
2025-08-02 14:03:10 | INFO     | gui.main_window:log_message:1189 | 選擇圖片欄位: 产品图片                             
2025-08-02 14:03:15 | INFO     | gui.main_window:log_message:1189 | 選擇產品名稱欄位: 名称
2025-08-02 14:03:16 | INFO     | gui.main_window:log_message:1189 | 選擇 Writer Prompt: furniture
2025-08-02 14:03:18 | INFO     | gui.main_window:log_message:1189 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 14:03:22 | INFO     | gui.main_window:log_message:1189 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 14:03:23 | INFO     | gui.main_window:log_message:1189 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:03:53 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['序号']
2025-08-02 14:03:53 | INFO     | gui.main_window:log_message:1189 | 排除欄位: 序号
2025-08-02 14:03:53 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 14:03:53 | INFO     | gui.main_window:log_message:1189 | 開始處理 3 個項目...
2025-08-02 14:03:53 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 4 到 6 (共 3 項)
2025-08-02 14:03:53 | INFO     | core.processing_engine:process_batch:379 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 14:03:53 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/3 (行 4)
2025-08-02 14:03:53 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['持久', '高品質', '優質', '無人工色素', '滋養']
2025-08-02 14:03:53 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_4901209267F84A65B5B3C44C9FF10428' 找到 0 張圖片
2025-08-02 14:03:59 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 639 input + 198 output = 837 tokens, 成本: $0.000000
2025-08-02 14:03:59 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,837,0.0
2025-08-02 14:04:00 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 837, 時間: 6.50s
2025-08-02 14:04:04 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 759 input + 189 output = 948 tokens, 成本: $0.000000
2025-08-02 14:04:04 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,948,0.0
2025-08-02 14:04:04 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 948, 時間: 4.74s
2025-08-02 14:04:04 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: A2556... (11.25s)
2025-08-02 14:04:04 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/3 (行 5)
2025-08-02 14:04:04 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '持久', '優質', '滋養', '實用']
2025-08-02 14:04:04 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y168chair' 找到 0 張圖片
2025-08-02 14:04:10 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 639 input + 177 output = 816 tokens, 成本: $0.000000
2025-08-02 14:04:10 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,816,0.0
2025-08-02 14:04:10 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 816, 時間: 5.38s
2025-08-02 14:04:19 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 709 input + 347 output = 1056 tokens, 成本: $0.000000
2025-08-02 14:04:19 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1056,0.0
2025-08-02 14:04:19 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1056, 時間: 9.75s
2025-08-02 14:04:19 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Y168... (15.13s)
2025-08-02 14:04:19 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/3 (行 6)
2025-08-02 14:04:19 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['持久', '高品質', '優質', '滋養', '實用']
2025-08-02 14:04:19 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y169chair' 找到 0 張圖片
2025-08-02 14:04:24 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 639 input + 184 output = 823 tokens, 成本: $0.000000
2025-08-02 14:04:24 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,823,0.0
2025-08-02 14:04:24 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 823, 時間: 4.77s
2025-08-02 14:04:29 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 688 input + 152 output = 840 tokens, 成本: $0.000000
2025-08-02 14:04:29 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,840,0.0
2025-08-02 14:04:29 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 840, 時間: 4.59s
2025-08-02 14:04:29 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Y169... (9.37s)
2025-08-02 14:04:29 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 3, 失敗 0
2025-08-02 14:04:29 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 14:04:29 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 14:04:29 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 14:04:29 | INFO     | gui.main_window:log_message:1189 | 處理完成！成功: 3, 失敗: 0, 總 Token: 5,320, 預估費用: $0.000000
2025-08-02 14:04:29 | INFO     | gui.main_window:log_message:1189 | 自動顯示 A2556... 的預覽
2025-08-02 14:05:49 | INFO     | gui.main_window:log_message:1189 | ✅ 所有結果已重置
2025-08-02 14:07:02 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-02 14:07:02 | INFO     | gui.main_window:log_message:1189 | 排除欄位: 無
2025-08-02 14:07:02 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 14:07:02 | INFO     | gui.main_window:log_message:1189 | 開始處理 3 個項目...
2025-08-02 14:07:02 | INFO     | core.processing_engine:process_batch:378 | 開始批次處理: 行 4 到 6 (共 3 項)
2025-08-02 14:07:02 | INFO     | core.processing_engine:process_batch:379 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 14:07:02 | INFO     | core.processing_engine:process_batch:382 | 處理項目 1/3 (行 4)
2025-08-02 14:07:02 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['持久', '高品質', '優質', '無人工色素', '滋養']
2025-08-02 14:07:02 | DEBUG    | core.data_processor:find_images:327 | 檔名 'ID_4901209267F84A65B5B3C44C9FF10428' 找到 0 張圖片
2025-08-02 14:07:08 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 639 input + 179 output = 818 tokens, 成本: $0.000000
2025-08-02 14:07:08 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,818,0.0
2025-08-02 14:07:08 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 818, 時間: 5.82s
2025-08-02 14:07:11 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 748 input + 124 output = 872 tokens, 成本: $0.000000
2025-08-02 14:07:11 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,872,0.0
2025-08-02 14:07:11 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 872, 時間: 3.56s
2025-08-02 14:07:11 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Dinning Table... (9.38s)
2025-08-02 14:07:11 | INFO     | core.processing_engine:process_batch:382 | 處理項目 2/3 (行 5)
2025-08-02 14:07:11 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '持久', '優質', '滋養', '實用']
2025-08-02 14:07:11 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y168chair' 找到 0 張圖片
2025-08-02 14:07:17 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 639 input + 177 output = 816 tokens, 成本: $0.000000
2025-08-02 14:07:17 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,816,0.0
2025-08-02 14:07:17 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 816, 時間: 6.11s
2025-08-02 14:07:21 | INFO     | gui.main_window:log_message:1189 | 第 5 行沒有 HTML 內容，請先執行處理
2025-08-02 14:07:21 | INFO     | gui.main_window:log_message:1189 | 第 5 行沒有 HTML 內容，請先執行處理
2025-08-02 14:07:21 | INFO     | gui.main_window:log_message:1189 | 第 5 行沒有 HTML 內容，請先執行處理
2025-08-02 14:07:23 | INFO     | gui.main_window:log_message:1189 | 第 5 行沒有 HTML 內容，請先執行處理
2025-08-02 14:07:23 | INFO     | gui.main_window:log_message:1189 | 第 5 行沒有 HTML 內容，請先執行處理
2025-08-02 14:07:23 | INFO     | gui.main_window:log_message:1189 | 第 5 行沒有 HTML 內容，請先執行處理
2025-08-02 14:07:24 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 717 input + 201 output = 918 tokens, 成本: $0.000000
2025-08-02 14:07:24 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,918,0.0
2025-08-02 14:07:24 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 918, 時間: 6.46s
2025-08-02 14:07:24 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Dinning Chair... (12.57s)
2025-08-02 14:07:24 | INFO     | core.processing_engine:process_batch:382 | 處理項目 3/3 (行 6)
2025-08-02 14:07:24 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['持久', '高品質', '優質', '滋養', '實用']
2025-08-02 14:07:24 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y169chair' 找到 0 張圖片
2025-08-02 14:07:30 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 639 input + 201 output = 840 tokens, 成本: $0.000000
2025-08-02 14:07:30 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,840,0.0
2025-08-02 14:07:30 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 840, 時間: 5.77s
2025-08-02 14:07:34 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 712 input + 183 output = 895 tokens, 成本: $0.000000
2025-08-02 14:07:34 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,895,0.0
2025-08-02 14:07:34 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 895, 時間: 4.87s
2025-08-02 14:07:34 | INFO     | core.processing_engine:process_batch:397 | 項目處理成功: Dinning Chair... (10.64s)
2025-08-02 14:07:34 | INFO     | core.processing_engine:process_batch:401 | 批次處理完成: 成功 3, 失敗 0
2025-08-02 14:07:34 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 14:07:34 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 14:07:34 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 14:07:34 | INFO     | gui.main_window:log_message:1189 | 處理完成！成功: 3, 失敗: 0, 總 Token: 5,159, 預估費用: $0.000000
2025-08-02 14:07:34 | INFO     | gui.main_window:log_message:1189 | 自動顯示 Dinning Table... 的預覽
2025-08-02 14:07:56 | INFO     | gui.main_window:log_message:1189 | 已選擇圖片資料夾: picture (29 張圖片)
2025-08-02 14:09:19 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:09:19 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:09:19 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:09:19 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:09:19 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:09:19 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:24:25 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 14:24:25 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 14:24:25 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 14:24:25 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 14:24:26 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 14:24:27 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 14:24:27 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 14:24:27 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 14:24:27 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 14:24:27 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 14:24:27 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 14:24:27 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 14:24:27 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 14:24:27 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 14:24:27 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 14:24:27 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 14:24:27 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 14:24:28 | INFO     | gui.main_window:log_message:1194 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:24:28 | INFO     | gui.main_window:log_message:1194 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-02 14:24:28 | INFO     | gui.main_window:log_message:1194 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:24:28 | INFO     | gui.main_window:log_message:1194 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:24:28 | INFO     | gui.main_window:log_message:1194 | 選擇 Writer Prompt: default
2025-08-02 14:24:28 | INFO     | gui.main_window:log_message:1194 | 選擇 Reviewer Prompt: standard
2025-08-02 14:24:29 | INFO     | gui.main_window:log_message:1194 | 選擇工作表: Sheet1
2025-08-02 14:24:29 | INFO     | gui.main_window:log_message:1194 | ✅ 設定已載入
2025-08-02 14:24:29 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 14:24:30 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 14:30:07 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 14:30:07 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 14:30:07 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 14:30:07 | INFO     | gui.main_window:log_message:1194 | 選擇圖片欄位: 無
2025-08-02 14:30:07 | INFO     | gui.main_window:log_message:1194 | 選擇 SEO 欄位: 無
2025-08-02 14:30:07 | INFO     | gui.main_window:log_message:1194 | 選擇產品名稱欄位: 無
2025-08-02 14:30:07 | INFO     | gui.main_window:log_message:1194 | 自動設定處理範圍: 1 到 14 行
2025-08-02 14:30:07 | INFO     | gui.main_window:log_message:1194 | 資料載入成功: 14 行, 7 欄
2025-08-02 14:30:07 | INFO     | gui.main_window:log_message:1194 | 警告: 部分欄位有缺失值
2025-08-02 14:34:24 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:34:24 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:34:24 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:34:24 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:34:24 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:34:24 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:38:57 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 14:38:57 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 14:38:57 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 14:38:57 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 14:38:58 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 14:38:58 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 14:38:58 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 14:38:58 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 14:38:58 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 14:38:58 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 14:38:58 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 14:38:58 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 14:38:58 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 14:38:58 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 14:38:58 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 14:38:58 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 14:38:58 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 14:39:01 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:39:01 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-02 14:39:01 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:39:01 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:39:01 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-02 14:39:01 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-02 14:39:01 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-02 14:39:01 | INFO     | gui.main_window:log_message:1224 | ✅ 設定已載入
2025-08-02 14:39:01 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 14:39:01 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 14:40:03 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 14:40:03 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 14:40:03 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 14:40:03 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: 無
2025-08-02 14:40:03 | INFO     | gui.main_window:log_message:1224 | 選擇 SEO 欄位: 無
2025-08-02 14:40:03 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 無
2025-08-02 14:40:03 | INFO     | gui.main_window:log_message:1224 | 自動設定處理範圍: 1 到 14 行
2025-08-02 14:40:03 | INFO     | gui.main_window:log_message:1224 | 資料載入成功: 14 行, 7 欄
2025-08-02 14:40:03 | INFO     | gui.main_window:log_message:1224 | 警告: 部分欄位有缺失值
2025-08-02 14:40:13 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: 产品图片                             
2025-08-02 14:40:19 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 名称
2025-08-02 14:40:22 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:40:24 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 14:40:38 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['数量']
2025-08-02 14:40:38 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 数量
2025-08-02 14:40:38 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 14:40:38 | INFO     | gui.main_window:log_message:1224 | 開始處理 2 個項目...
2025-08-02 14:40:38 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 8 到 9 (共 2 項)
2025-08-02 14:40:38 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 14:40:38 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/2 (行 8)
2025-08-02 14:40:38 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '呵護', '多功能', '快速', '可持續']
2025-08-02 14:40:38 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y172chair' 找到 1 張圖片
2025-08-02 14:40:39 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:40:39 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:40:39 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:40:39 | INFO     | core.processing_engine:process_batch:383 | 處理項目 2/2 (行 9)
2025-08-02 14:40:39 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '呵護', '多功能', '快速', '可持續']
2025-08-02 14:40:39 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y173chair' 找到 1 張圖片
2025-08-02 14:40:39 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:40:39 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:40:39 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:40:39 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 0, 失敗 0
2025-08-02 14:40:39 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 14:40:39 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 14:40:39 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 14:40:39 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 0, 失敗: 2, 總 Token: 0, 預估費用: $0.000000
2025-08-02 14:41:03 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['数量']
2025-08-02 14:41:03 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 数量
2025-08-02 14:41:03 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 14:41:03 | INFO     | gui.main_window:log_message:1224 | 開始處理 2 個項目...
2025-08-02 14:41:03 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 8 到 9 (共 2 項)
2025-08-02 14:41:03 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 14:41:03 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/2 (行 8)
2025-08-02 14:41:03 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 1)
2025-08-02 14:41:03 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['多功能', '可持續', '高品質', '呵護', '品質']
2025-08-02 14:41:03 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y172chair' 找到 1 張圖片
2025-08-02 14:41:03 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:03 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:03 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:03 | INFO     | core.processing_engine:process_batch:383 | 處理項目 2/2 (行 9)
2025-08-02 14:41:03 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 1)
2025-08-02 14:41:03 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['多功能', '可持續', '高品質', '呵護', '品質']
2025-08-02 14:41:03 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y173chair' 找到 1 張圖片
2025-08-02 14:41:03 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:03 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:03 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:03 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 0, 失敗 0
2025-08-02 14:41:03 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 14:41:03 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 14:41:03 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 14:41:03 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 0, 失敗: 2, 總 Token: 0, 預估費用: $0.000000
2025-08-02 14:41:28 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: furniture
2025-08-02 14:41:48 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: ['数量']
2025-08-02 14:41:48 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 数量
2025-08-02 14:41:48 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 14:41:48 | INFO     | gui.main_window:log_message:1224 | 開始處理 3 個項目...
2025-08-02 14:41:48 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 8 到 10 (共 3 項)
2025-08-02 14:41:48 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 14:41:48 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/3 (行 8)
2025-08-02 14:41:48 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 1)
2025-08-02 14:41:48 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['多功能', '可持續', '高品質', '呵護', '品質']
2025-08-02 14:41:48 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y172chair' 找到 1 張圖片
2025-08-02 14:41:48 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:48 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:48 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:48 | INFO     | core.processing_engine:process_batch:383 | 處理項目 2/3 (行 9)
2025-08-02 14:41:48 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 1)
2025-08-02 14:41:48 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['多功能', '可持續', '高品質', '呵護', '品質']
2025-08-02 14:41:48 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y173chair' 找到 1 張圖片
2025-08-02 14:41:49 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:49 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:49 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:49 | INFO     | core.processing_engine:process_batch:383 | 處理項目 3/3 (行 10)
2025-08-02 14:41:49 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['呵護', '多功能', '快速', '可持續', '高品質']
2025-08-02 14:41:49 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y174chair' 找到 1 張圖片
2025-08-02 14:41:49 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:49 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:49 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:41:49 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 0, 失敗 0
2025-08-02 14:41:49 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 14:41:49 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 14:41:49 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 14:41:49 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 0, 失敗: 3, 總 Token: 0, 預估費用: $0.000000
2025-08-02 14:41:52 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:41:52 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:41:52 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:41:52 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:41:52 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:41:52 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:42:08 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 14:42:08 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 14:42:08 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 14:42:08 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 14:42:09 | INFO     | core.ai_models:setup_models:317 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 14:42:10 | INFO     | core.ai_models:setup_models:345 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 14:42:10 | INFO     | core.ai_models:setup_models:373 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 14:42:10 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 14:42:10 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 14:42:10 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 14:42:10 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 14:42:10 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 14:42:10 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 14:42:10 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 14:42:10 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 14:42:10 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 14:42:10 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 14:42:11 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:42:11 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-02 14:42:11 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:42:11 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:42:11 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-02 14:42:11 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-02 14:42:12 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-02 14:42:12 | INFO     | gui.main_window:log_message:1224 | ✅ 設定已載入
2025-08-02 14:42:12 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 14:42:12 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 14:42:27 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 14:42:27 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 14:42:27 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 14:42:27 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: 無
2025-08-02 14:42:27 | INFO     | gui.main_window:log_message:1224 | 選擇 SEO 欄位: 無
2025-08-02 14:42:27 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 無
2025-08-02 14:42:27 | INFO     | gui.main_window:log_message:1224 | 自動設定處理範圍: 1 到 14 行
2025-08-02 14:42:27 | INFO     | gui.main_window:log_message:1224 | 資料載入成功: 14 行, 7 欄
2025-08-02 14:42:27 | INFO     | gui.main_window:log_message:1224 | 警告: 部分欄位有缺失值
2025-08-02 14:42:34 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 名称
2025-08-02 14:42:37 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:42:39 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 14:42:49 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-02 14:42:49 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 無
2025-08-02 14:42:49 | INFO     | gui.main_window:log_message:1224 | 開始處理 2 個項目...
2025-08-02 14:42:49 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 3 到 4 (共 2 項)
2025-08-02 14:42:49 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 14:42:49 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/2 (行 3)
2025-08-02 14:42:49 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '熱銷', '保養', '高品質', '均衡']
2025-08-02 14:43:01 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 504 input + 555 output = 1059 tokens, 成本: $0.000000
2025-08-02 14:43:01 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1059,0.0
2025-08-02 14:43:01 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1059, 時間: 12.62s
2025-08-02 14:43:05 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1127 input + 136 output = 1263 tokens, 成本: $0.000000
2025-08-02 14:43:05 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1263,0.0
2025-08-02 14:43:05 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1263, 時間: 3.64s
2025-08-02 14:43:05 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Table... (16.27s)
2025-08-02 14:43:05 | INFO     | core.processing_engine:process_batch:383 | 處理項目 2/2 (行 4)
2025-08-02 14:43:05 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['熱銷', '保養', '高品質', '均衡', '全方位']
2025-08-02 14:43:18 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 506 input + 576 output = 1082 tokens, 成本: $0.000000
2025-08-02 14:43:18 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1082,0.0
2025-08-02 14:43:18 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1082, 時間: 13.40s
2025-08-02 14:43:23 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1188 input + 225 output = 1413 tokens, 成本: $0.000000
2025-08-02 14:43:23 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1413,0.0
2025-08-02 14:43:23 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1413, 時間: 4.62s
2025-08-02 14:43:23 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Table... (18.02s)
2025-08-02 14:43:23 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 2, 失敗 0
2025-08-02 14:43:23 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 14:43:23 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 14:43:23 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 14:43:23 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 2, 失敗: 0, 總 Token: 4,817, 預估費用: $0.000000
2025-08-02 14:43:23 | INFO     | gui.main_window:log_message:1224 | 自動顯示 Dinning Table... 的預覽
2025-08-02 14:43:55 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: furniture
2025-08-02 14:43:57 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-02 14:43:57 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 無
2025-08-02 14:43:57 | INFO     | gui.main_window:log_message:1224 | 開始處理 2 個項目...
2025-08-02 14:43:57 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 3 到 4 (共 2 項)
2025-08-02 14:43:57 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 14:43:57 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/2 (行 3)
2025-08-02 14:43:57 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 2)
2025-08-02 14:43:57 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '可持續', '熱銷', '保養', '均衡']
2025-08-02 14:44:03 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 665 input + 194 output = 859 tokens, 成本: $0.000000
2025-08-02 14:44:03 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,859,0.0
2025-08-02 14:44:03 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 859, 時間: 5.56s
2025-08-02 14:44:04 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1492 input + 136 output = 1628 tokens, 成本: $0.000000
2025-08-02 14:44:04 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1628,0.0
2025-08-02 14:44:04 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1628, 時間: 1.71s
2025-08-02 14:44:04 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Table... (7.28s)
2025-08-02 14:44:04 | INFO     | core.processing_engine:process_batch:383 | 處理項目 2/2 (行 4)
2025-08-02 14:44:04 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 2)
2025-08-02 14:44:04 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '全方位', '熱銷', '保養', '均衡']
2025-08-02 14:44:09 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 665 input + 188 output = 853 tokens, 成本: $0.000000
2025-08-02 14:44:09 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,853,0.0
2025-08-02 14:44:09 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 853, 時間: 4.86s
2025-08-02 14:44:14 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 1635 input + 285 output = 1920 tokens, 成本: $0.000000
2025-08-02 14:44:14 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1920,0.0
2025-08-02 14:44:14 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1920, 時間: 4.73s
2025-08-02 14:44:14 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Table... (9.60s)
2025-08-02 14:44:14 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 2, 失敗 0
2025-08-02 14:44:14 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 14:44:14 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 14:44:14 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 14:44:14 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 2, 失敗: 0, 總 Token: 5,260, 預估費用: $0.000000
2025-08-02 14:44:14 | INFO     | gui.main_window:log_message:1224 | 自動顯示 Dinning Table... 的預覽
2025-08-02 14:44:56 | INFO     | gui.main_window:log_message:1224 | ✅ 所有結果已重置
2025-08-02 14:45:03 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-02 14:45:03 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 無
2025-08-02 14:45:03 | INFO     | gui.main_window:log_message:1224 | 開始處理 3 個項目...
2025-08-02 14:45:03 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 5 到 7 (共 3 項)
2025-08-02 14:45:03 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 14:45:03 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/3 (行 5)
2025-08-02 14:45:03 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '熱銷', '保養', '均衡', '全方位']
2025-08-02 14:45:07 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 665 input + 160 output = 825 tokens, 成本: $0.000000
2025-08-02 14:45:07 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,825,0.0
2025-08-02 14:45:07 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 825, 時間: 4.23s
2025-08-02 14:45:13 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 700 input + 239 output = 939 tokens, 成本: $0.000000
2025-08-02 14:45:13 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,939,0.0
2025-08-02 14:45:13 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 939, 時間: 5.81s
2025-08-02 14:45:13 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (10.04s)
2025-08-02 14:45:13 | INFO     | core.processing_engine:process_batch:383 | 處理項目 2/3 (行 6)
2025-08-02 14:45:13 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['熱銷', '保養', '高品質', '均衡', '全方位']
2025-08-02 14:45:17 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 665 input + 171 output = 836 tokens, 成本: $0.000000
2025-08-02 14:45:17 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,836,0.0
2025-08-02 14:45:17 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 836, 時間: 4.27s
2025-08-02 14:45:22 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 681 input + 253 output = 934 tokens, 成本: $0.000000
2025-08-02 14:45:22 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,934,0.0
2025-08-02 14:45:22 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 934, 時間: 5.05s
2025-08-02 14:45:22 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (9.32s)
2025-08-02 14:45:22 | INFO     | core.processing_engine:process_batch:383 | 處理項目 3/3 (行 7)
2025-08-02 14:45:22 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['熱銷', '保養', '高品質', '均衡', '全方位']
2025-08-02 14:45:27 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 665 input + 184 output = 849 tokens, 成本: $0.000000
2025-08-02 14:45:27 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,849,0.0
2025-08-02 14:45:27 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 849, 時間: 4.59s
2025-08-02 14:45:31 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 708 input + 190 output = 898 tokens, 成本: $0.000000
2025-08-02 14:45:31 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,898,0.0
2025-08-02 14:45:31 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 898, 時間: 3.89s
2025-08-02 14:45:31 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (8.48s)
2025-08-02 14:45:31 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 3, 失敗 0
2025-08-02 14:45:31 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 14:45:31 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 14:45:31 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 14:45:31 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 3, 失敗: 0, 總 Token: 5,281, 預估費用: $0.000000
2025-08-02 14:45:31 | INFO     | gui.main_window:log_message:1224 | 自動顯示 Dinning Chair... 的預覽
2025-08-02 14:47:32 | INFO     | gui.main_window:log_message:1224 | ✅ 所有結果已重置
2025-08-02 14:47:39 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-02 14:47:39 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 無
2025-08-02 14:47:39 | INFO     | gui.main_window:log_message:1224 | 開始處理 3 個項目...
2025-08-02 14:47:39 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 5 到 7 (共 3 項)
2025-08-02 14:47:39 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 14:47:39 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/3 (行 5)
2025-08-02 14:47:39 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '熱銷', '保養', '均衡', '全方位']
2025-08-02 14:47:43 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 665 input + 171 output = 836 tokens, 成本: $0.000000
2025-08-02 14:47:43 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,836,0.0
2025-08-02 14:47:43 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 836, 時間: 4.28s
2025-08-02 14:47:50 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 711 input + 324 output = 1035 tokens, 成本: $0.000000
2025-08-02 14:47:50 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1035,0.0
2025-08-02 14:47:50 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1035, 時間: 7.18s
2025-08-02 14:47:50 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (11.47s)
2025-08-02 14:47:50 | INFO     | core.processing_engine:process_batch:383 | 處理項目 2/3 (行 6)
2025-08-02 14:47:50 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['熱銷', '保養', '高品質', '均衡', '全方位']
2025-08-02 14:47:53 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 665 input + 156 output = 821 tokens, 成本: $0.000000
2025-08-02 14:47:53 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,821,0.0
2025-08-02 14:47:53 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 821, 時間: 2.99s
2025-08-02 14:47:58 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 667 input + 203 output = 870 tokens, 成本: $0.000000
2025-08-02 14:47:58 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,870,0.0
2025-08-02 14:47:58 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 870, 時間: 4.75s
2025-08-02 14:47:58 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (7.74s)
2025-08-02 14:47:58 | INFO     | core.processing_engine:process_batch:383 | 處理項目 3/3 (行 7)
2025-08-02 14:47:58 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['熱銷', '保養', '高品質', '均衡', '全方位']
2025-08-02 14:48:01 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 665 input + 179 output = 844 tokens, 成本: $0.000000
2025-08-02 14:48:01 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,844,0.0
2025-08-02 14:48:01 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 844, 時間: 3.46s
2025-08-02 14:48:08 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 704 input + 362 output = 1066 tokens, 成本: $0.000000
2025-08-02 14:48:08 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1066,0.0
2025-08-02 14:48:08 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1066, 時間: 6.53s
2025-08-02 14:48:08 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (9.99s)
2025-08-02 14:48:08 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 3, 失敗 0
2025-08-02 14:48:08 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 14:48:08 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 14:48:08 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 14:48:08 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 3, 失敗: 0, 總 Token: 5,472, 預估費用: $0.000000
2025-08-02 14:48:08 | INFO     | gui.main_window:log_message:1224 | 自動顯示 Dinning Chair... 的預覽
2025-08-02 14:49:54 | INFO     | core.prompt_manager:add_reviewer_prompt:251 | 新增 Reviewer Prompt: standard
2025-08-02 14:49:54 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-02 14:49:54 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-02 14:49:54 | INFO     | gui.main_window:log_message:1224 | 已儲存 Reviewer Prompt: standard
2025-08-02 14:50:27 | INFO     | core.prompt_manager:add_writer_prompt:224 | 新增 Writer Prompt: furniture
2025-08-02 14:50:27 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-02 14:50:27 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-02 14:50:27 | INFO     | gui.main_window:log_message:1224 | 已儲存 Writer Prompt: furniture
2025-08-02 14:50:54 | INFO     | core.prompt_manager:add_writer_prompt:224 | 新增 Writer Prompt: furniture
2025-08-02 14:50:54 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-02 14:50:54 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-02 14:50:54 | INFO     | gui.main_window:log_message:1224 | 已儲存 Writer Prompt: furniture
2025-08-02 14:51:02 | INFO     | gui.main_window:log_message:1224 | ✅ 所有結果已重置
2025-08-02 14:51:05 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 产品图片                             
2025-08-02 14:51:09 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: furniture
2025-08-02 14:51:24 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-02 14:51:24 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 無
2025-08-02 14:51:24 | INFO     | gui.main_window:log_message:1224 | 開始處理 3 個項目...
2025-08-02 14:51:24 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 7 到 9 (共 3 項)
2025-08-02 14:51:24 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 14:51:24 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/3 (行 7)
2025-08-02 14:51:24 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['熱銷', '保養', '高品質', '均衡', '全方位']
2025-08-02 14:51:29 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 665 input + 188 output = 853 tokens, 成本: $0.000000
2025-08-02 14:51:29 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,853,0.0
2025-08-02 14:51:29 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 853, 時間: 4.90s
2025-08-02 14:51:37 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 713 input + 385 output = 1098 tokens, 成本: $0.000000
2025-08-02 14:51:37 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1098,0.0
2025-08-02 14:51:37 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1098, 時間: 7.78s
2025-08-02 14:51:37 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (12.69s)
2025-08-02 14:51:37 | INFO     | core.processing_engine:process_batch:383 | 處理項目 2/3 (行 8)
2025-08-02 14:51:37 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '熱銷', '保養', '均衡', '全方位']
2025-08-02 14:51:41 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 665 input + 180 output = 845 tokens, 成本: $0.000000
2025-08-02 14:51:41 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,845,0.0
2025-08-02 14:51:41 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 845, 時間: 4.24s
2025-08-02 14:51:51 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 736 input + 381 output = 1117 tokens, 成本: $0.000000
2025-08-02 14:51:51 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1117,0.0
2025-08-02 14:51:51 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1117, 時間: 9.96s
2025-08-02 14:51:51 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (14.21s)
2025-08-02 14:51:51 | INFO     | core.processing_engine:process_batch:383 | 處理項目 3/3 (行 9)
2025-08-02 14:51:51 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '熱銷', '保養', '均衡', '全方位']
2025-08-02 14:51:55 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 665 input + 183 output = 848 tokens, 成本: $0.000000
2025-08-02 14:51:55 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,848,0.0
2025-08-02 14:51:55 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 848, 時間: 4.31s
2025-08-02 14:52:02 | INFO     | core.ai_models:generate_text:118 | OpenAI 使用記錄: 725 input + 361 output = 1086 tokens, 成本: $0.000000
2025-08-02 14:52:02 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1086,0.0
2025-08-02 14:52:02 | INFO     | core.ai_models:generate_text:417 | AI 生成完成 - 模型: openai-gpt4o, Token: 1086, 時間: 6.50s
2025-08-02 14:52:02 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (10.81s)
2025-08-02 14:52:02 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 3, 失敗 0
2025-08-02 14:52:02 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 14:52:02 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 14:52:02 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 14:52:02 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 3, 失敗: 0, 總 Token: 5,847, 預估費用: $0.000000
2025-08-02 14:52:02 | INFO     | gui.main_window:log_message:1224 | 自動顯示 Dinning Chair... 的預覽
2025-08-02 14:52:55 | INFO     | core.prompt_manager:add_reviewer_prompt:251 | 新增 Reviewer Prompt: standard
2025-08-02 14:52:55 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-02 14:52:55 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-02 14:52:55 | INFO     | gui.main_window:log_message:1224 | 已儲存 Reviewer Prompt: standard
2025-08-02 14:53:30 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 名称
2025-08-02 14:53:34 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: 产品图片                             
2025-08-02 14:53:39 | INFO     | gui.main_window:log_message:1224 | ✅ 所有結果已重置
2025-08-02 14:53:42 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-02 14:53:42 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 無
2025-08-02 14:53:42 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 14:53:42 | INFO     | gui.main_window:log_message:1224 | 開始處理 3 個項目...
2025-08-02 14:53:42 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 7 到 9 (共 3 項)
2025-08-02 14:53:42 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 14:53:42 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/3 (行 7)
2025-08-02 14:53:42 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['熱銷', '保養', '高品質', '均衡', '全方位']
2025-08-02 14:53:42 | DEBUG    | core.data_processor:find_images:327 | 檔名 '24-48chair' 找到 1 張圖片
2025-08-02 14:53:43 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:43 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:43 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:43 | INFO     | core.processing_engine:process_batch:383 | 處理項目 2/3 (行 8)
2025-08-02 14:53:43 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '熱銷', '保養', '均衡', '全方位']
2025-08-02 14:53:43 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y172chair' 找到 1 張圖片
2025-08-02 14:53:43 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:43 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:43 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:43 | INFO     | core.processing_engine:process_batch:383 | 處理項目 3/3 (行 9)
2025-08-02 14:53:43 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '熱銷', '保養', '均衡', '全方位']
2025-08-02 14:53:43 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y173chair' 找到 1 張圖片
2025-08-02 14:53:43 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:43 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:43 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:43 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 0, 失敗 0
2025-08-02 14:53:43 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 14:53:43 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 14:53:43 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 14:53:43 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 0, 失敗: 3, 總 Token: 0, 預估費用: $0.000000
2025-08-02 14:53:51 | INFO     | gui.main_window:log_message:1224 | ✅ 所有結果已重置
2025-08-02 14:53:58 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-02 14:53:58 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 無
2025-08-02 14:53:58 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 14:53:58 | INFO     | gui.main_window:log_message:1224 | 開始處理 3 個項目...
2025-08-02 14:53:58 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 7 到 9 (共 3 項)
2025-08-02 14:53:58 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 14:53:58 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/3 (行 7)
2025-08-02 14:53:58 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['熱銷', '保養', '高品質', '均衡', '全方位']
2025-08-02 14:53:58 | DEBUG    | core.data_processor:find_images:327 | 檔名 '24-48chair' 找到 1 張圖片
2025-08-02 14:53:59 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:59 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:59 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:59 | INFO     | core.processing_engine:process_batch:383 | 處理項目 2/3 (行 8)
2025-08-02 14:53:59 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '熱銷', '保養', '均衡', '全方位']
2025-08-02 14:53:59 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y172chair' 找到 1 張圖片
2025-08-02 14:53:59 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:59 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:59 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:59 | INFO     | core.processing_engine:process_batch:383 | 處理項目 3/3 (行 9)
2025-08-02 14:53:59 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '熱銷', '保養', '均衡', '全方位']
2025-08-02 14:53:59 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y173chair' 找到 1 張圖片
2025-08-02 14:53:59 | ERROR    | core.ai_models:generate_text:131 | OpenAI API 呼叫失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:59 | ERROR    | core.ai_models:generate_text:423 | AI 生成失敗 - 模型: openai-gpt4o, 錯誤: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:59 | ERROR    | core.processing_engine:process_batch:400 | 項目處理失敗: Writer 階段失敗: Error code: 404 - {'error': {'message': 'The model `gpt-4-vision-preview` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}
2025-08-02 14:53:59 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 0, 失敗 0
2025-08-02 14:53:59 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 14:53:59 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 14:53:59 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 14:53:59 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 0, 失敗: 3, 總 Token: 0, 預估費用: $0.000000
2025-08-02 14:54:06 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:54:06 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:54:06 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:54:06 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:54:06 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:54:06 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:56:02 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 14:56:02 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 14:56:02 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 14:56:02 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 14:56:03 | INFO     | core.ai_models:setup_models:318 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 14:56:04 | INFO     | core.ai_models:setup_models:346 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 14:56:04 | INFO     | core.ai_models:setup_models:374 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 14:56:04 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 14:56:04 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 14:56:04 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 14:56:04 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 14:56:05 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 14:56:05 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 14:56:05 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 14:56:05 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 14:56:05 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 14:56:05 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 14:56:07 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:56:07 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-02 14:56:07 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:56:07 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:56:07 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-02 14:56:07 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-02 14:56:07 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-02 14:56:07 | INFO     | gui.main_window:log_message:1224 | ✅ 設定已載入
2025-08-02 14:56:07 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 14:56:07 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 14:57:47 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 14:57:47 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 14:57:47 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 14:57:47 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 14:57:47 | INFO     | core.ai_models:setup_models:318 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 14:57:48 | INFO     | core.ai_models:setup_models:346 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 14:57:48 | INFO     | core.ai_models:setup_models:374 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 14:57:48 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 14:57:48 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 14:57:48 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 14:57:48 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 14:57:48 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 14:57:48 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 14:57:48 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 14:57:48 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 14:57:48 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 14:57:48 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 14:57:50 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:57:50 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-02 14:57:50 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:57:50 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:57:50 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-02 14:57:50 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-02 14:57:51 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-02 14:57:51 | INFO     | gui.main_window:log_message:1224 | ✅ 設定已載入
2025-08-02 14:57:51 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 14:57:51 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 14:58:45 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:58:45 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:58:45 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:58:45 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:58:45 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:58:45 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:58:46 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 14:58:46 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 14:58:46 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 14:58:46 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 14:58:47 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:58:47 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:58:47 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:58:47 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:58:47 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:58:47 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 14:58:48 | INFO     | core.ai_models:setup_models:318 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 14:58:49 | INFO     | core.ai_models:setup_models:346 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 14:58:49 | INFO     | core.ai_models:setup_models:374 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 14:58:49 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 14:58:49 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 14:58:49 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 14:58:49 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 14:58:49 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 14:58:49 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 14:58:49 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 14:58:49 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 14:58:49 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 14:58:49 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 14:58:51 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:58:51 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-02 14:58:51 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:58:51 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 14:58:51 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-02 14:58:51 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-02 14:58:51 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-02 14:58:51 | INFO     | gui.main_window:log_message:1224 | ✅ 設定已載入
2025-08-02 14:58:51 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 14:58:51 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 15:01:14 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 15:01:14 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 15:01:14 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 15:01:14 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 15:01:14 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 15:01:14 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 15:01:22 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 15:01:22 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 15:01:22 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 15:01:22 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 15:01:23 | INFO     | core.ai_models:setup_models:318 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 15:01:23 | INFO     | core.ai_models:setup_models:346 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 15:01:23 | INFO     | core.ai_models:setup_models:374 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 15:01:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 15:01:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 15:01:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 15:01:23 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 15:01:23 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 15:01:23 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 15:01:23 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 15:01:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 15:01:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 15:01:23 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 15:01:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 15:01:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-02 15:01:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 15:01:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 15:01:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-02 15:01:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-02 15:01:25 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-02 15:01:25 | INFO     | gui.main_window:log_message:1224 | ✅ 設定已載入
2025-08-02 15:01:25 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 15:01:25 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 15:01:34 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 15:01:34 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 15:01:34 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 15:01:34 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: 無
2025-08-02 15:01:34 | INFO     | gui.main_window:log_message:1224 | 選擇 SEO 欄位: 無
2025-08-02 15:01:34 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 無
2025-08-02 15:01:34 | INFO     | gui.main_window:log_message:1224 | 自動設定處理範圍: 1 到 14 行
2025-08-02 15:01:34 | INFO     | gui.main_window:log_message:1224 | 資料載入成功: 14 行, 7 欄
2025-08-02 15:01:34 | INFO     | gui.main_window:log_message:1224 | 警告: 部分欄位有缺失值
2025-08-02 15:01:41 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: 产品图片                             
2025-08-02 15:01:44 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 名称
2025-08-02 15:03:16 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 15:03:16 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 15:03:16 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 15:03:16 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 15:03:16 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 15:03:16 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-02 15:04:50 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-02 15:04:50 | INFO     | __main__:main:35 | 配置載入完成
2025-08-02 15:04:50 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-02 15:04:50 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-02 15:04:50 | INFO     | core.ai_models:setup_models:318 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-02 15:04:51 | INFO     | core.ai_models:setup_models:346 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-02 15:04:51 | INFO     | core.ai_models:setup_models:374 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-02 15:04:51 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-02 15:04:51 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-02 15:04:51 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-02 15:04:51 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-02 15:04:51 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-02 15:04:51 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-02 15:04:51 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-02 15:04:51 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-02 15:04:51 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-02 15:04:51 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-02 15:04:52 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 15:04:52 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-02 15:04:52 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 15:04:52 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 15:04:52 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-02 15:04:52 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-02 15:04:52 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-02 15:04:52 | INFO     | gui.main_window:log_message:1224 | ✅ 設定已載入
2025-08-02 15:04:52 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-02 15:04:52 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-02 15:05:10 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Trizenith Product detail Raw data.xlsx
2025-08-02 15:05:10 | INFO     | core.data_processor:load_excel:46 | 成功載入 14 行資料，7 個欄位
2025-08-02 15:05:10 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['序号', '产品型号                       ', '产品图片                             ', '规格尺寸', '材质', '数量', '名称']
2025-08-02 15:05:10 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: 無
2025-08-02 15:05:10 | INFO     | gui.main_window:log_message:1224 | 選擇 SEO 欄位: 無
2025-08-02 15:05:10 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 無
2025-08-02 15:05:10 | INFO     | gui.main_window:log_message:1224 | 自動設定處理範圍: 1 到 14 行
2025-08-02 15:05:10 | INFO     | gui.main_window:log_message:1224 | 資料載入成功: 14 行, 7 欄
2025-08-02 15:05:10 | INFO     | gui.main_window:log_message:1224 | 警告: 部分欄位有缺失值
2025-08-02 15:05:16 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: 产品图片                             
2025-08-02 15:05:22 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 名称
2025-08-02 15:05:24 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-02 15:05:26 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-02 15:05:36 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-02 15:05:36 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 無
2025-08-02 15:05:36 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 15:05:36 | INFO     | gui.main_window:log_message:1224 | 開始處理 2 個項目...
2025-08-02 15:05:36 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 8 到 9 (共 2 項)
2025-08-02 15:05:36 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 15:05:36 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/2 (行 8)
2025-08-02 15:05:36 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '多功能', '快速', '認證', '經典']
2025-08-02 15:05:36 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y172chair' 找到 1 張圖片
2025-08-02 15:05:55 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 748 input + 520 output = 1268 tokens, 成本: $0.000000
2025-08-02 15:05:55 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1268,0.0
2025-08-02 15:05:55 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1268, 時間: 18.96s
2025-08-02 15:06:00 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1122 input + 245 output = 1367 tokens, 成本: $0.000000
2025-08-02 15:06:00 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1367,0.0
2025-08-02 15:06:00 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1367, 時間: 5.92s
2025-08-02 15:06:00 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (24.89s)
2025-08-02 15:06:00 | INFO     | core.processing_engine:process_batch:383 | 處理項目 2/2 (行 9)
2025-08-02 15:06:00 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '多功能', '快速', '認證', '經典']
2025-08-02 15:06:00 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y173chair' 找到 1 張圖片
2025-08-02 15:06:17 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 733 input + 409 output = 1142 tokens, 成本: $0.000000
2025-08-02 15:06:17 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1142,0.0
2025-08-02 15:06:17 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1142, 時間: 16.60s
2025-08-02 15:06:22 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 977 input + 235 output = 1212 tokens, 成本: $0.000000
2025-08-02 15:06:22 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1212,0.0
2025-08-02 15:06:22 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1212, 時間: 5.20s
2025-08-02 15:06:22 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (21.80s)
2025-08-02 15:06:22 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 2, 失敗 0
2025-08-02 15:06:22 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 15:06:22 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 15:06:22 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 15:06:22 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 2, 失敗: 0, 總 Token: 4,989, 預估費用: $0.000000
2025-08-02 15:06:22 | INFO     | gui.main_window:log_message:1224 | 自動顯示 Dinning Chair... 的預覽
2025-08-02 15:06:54 | INFO     | gui.main_window:log_message:1224 | ✅ 所有結果已重置
2025-08-02 15:06:56 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: furniture
2025-08-02 15:06:59 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-02 15:06:59 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 無
2025-08-02 15:06:59 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: 产品图片                             
2025-08-02 15:06:59 | INFO     | gui.main_window:log_message:1224 | 開始處理 2 個項目...
2025-08-02 15:06:59 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 8 到 9 (共 2 項)
2025-08-02 15:06:59 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-02 15:06:59 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/2 (行 8)
2025-08-02 15:06:59 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '多功能', '快速', '認證', '經典']
2025-08-02 15:06:59 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y172chair' 找到 1 張圖片
2025-08-02 15:07:04 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 920 input + 158 output = 1078 tokens, 成本: $0.000000
2025-08-02 15:07:04 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1078,0.0
2025-08-02 15:07:04 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1078, 時間: 5.72s
2025-08-02 15:07:08 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 715 input + 142 output = 857 tokens, 成本: $0.000000
2025-08-02 15:07:08 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,857,0.0
2025-08-02 15:07:08 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 857, 時間: 3.68s
2025-08-02 15:07:08 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (9.41s)
2025-08-02 15:07:08 | INFO     | core.processing_engine:process_batch:383 | 處理項目 2/2 (行 9)
2025-08-02 15:07:08 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '多功能', '快速', '認證', '經典']
2025-08-02 15:07:08 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Y173chair' 找到 1 張圖片
2025-08-02 15:07:14 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 920 input + 156 output = 1076 tokens, 成本: $0.000000
2025-08-02 15:07:14 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1076,0.0
2025-08-02 15:07:14 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1076, 時間: 6.09s
2025-08-02 15:07:23 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 696 input + 422 output = 1118 tokens, 成本: $0.000000
2025-08-02 15:07:23 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1118,0.0
2025-08-02 15:07:23 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1118, 時間: 9.15s
2025-08-02 15:07:23 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: Dinning Chair... (15.24s)
2025-08-02 15:07:23 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 2, 失敗 0
2025-08-02 15:07:23 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-02 15:07:23 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-02 15:07:23 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-02 15:07:23 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 2, 失敗: 0, 總 Token: 4,129, 預估費用: $0.000000
2025-08-02 15:07:23 | INFO     | gui.main_window:log_message:1224 | 自動顯示 Dinning Chair... 的預覽
2025-08-02 15:08:19 | INFO     | gui.main_window:log_message:1224 | 顯示第 10 行的 HTML 預覽和 Reviewer 結果
2025-08-02 15:08:19 | INFO     | gui.main_window:log_message:1224 | 顯示第 10 行的 HTML 預覽和 Reviewer 結果
2025-08-02 15:08:19 | INFO     | gui.main_window:log_message:1224 | 顯示第 10 行的 HTML 預覽和 Reviewer 結果
2025-08-02 15:08:19 | INFO     | gui.main_window:log_message:1224 | 顯示第 10 行的 HTML 預覽和 Reviewer 結果
