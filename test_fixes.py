#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for Bug Fixes
測試修復的功能
"""

import sys
import os
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_html_preview():
    """測試 HTML 預覽功能"""
    print("🔍 測試 HTML 預覽功能...")
    
    try:
        from core.html_generator import HTMLGenerator
        
        generator = HTMLGenerator()
        
        # 測試 HTML 內容
        test_html = """
        <h1>測試產品名稱</h1>
        <h2>Description</h2>
        <h3>Product Overview</h3>
        <p>這是一個測試產品的概述。</p>
        <h3>Main Benefits</h3>
        <ul>
            <li>效益一：提升健康</li>
            <li>效益二：增強免疫力</li>
        </ul>
        <h2>Ingredients</h2>
        <h3>Active Ingredients</h3>
        <p>主要成分：維生素C 1000mg</p>
        """
        
        # 生成預覽 HTML
        preview_html = generator.generate_preview_html(test_html)
        
        # 檢查是否包含必要元素
        checks = [
            "<!DOCTYPE html>" in preview_html,
            "Microsoft YaHei" in preview_html,
            "container" in preview_html,
            "測試產品名稱" in preview_html,
            "preview-header" in preview_html
        ]
        
        if all(checks):
            print("✅ HTML 預覽功能正常")
            
            # 儲存測試檔案
            test_file = project_root / "test_preview.html"
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(preview_html)
            print(f"   - 測試檔案已儲存: {test_file}")
            
            return True
        else:
            print("❌ HTML 預覽功能有問題")
            return False
            
    except Exception as e:
        print(f"❌ HTML 預覽測試失敗: {e}")
        return False


def test_product_name_generation():
    """測試產品名稱生成功能"""
    print("\n🔍 測試產品名稱生成...")
    
    try:
        from core.processing_engine import ProcessingEngine
        from core.data_processor import ExcelProcessor
        from core.ai_models import AIModelManager
        from core.prompt_manager import PromptManager
        from core.keyword_manager import KeywordManager
        from core.html_generator import HTMLGenerator
        from config.settings import load_config
        
        # 建立測試用的處理引擎
        config = load_config()
        excel_processor = ExcelProcessor()
        ai_manager = AIModelManager(config)
        prompt_manager = PromptManager(config.get('paths.prompts_dir'))
        keyword_manager = KeywordManager(config.get('paths.categories_dir'))
        html_generator = HTMLGenerator()
        
        engine = ProcessingEngine(
            excel_processor, ai_manager, prompt_manager, 
            keyword_manager, html_generator
        )
        
        # 測試案例 1: 有品牌和分類
        test_data_1 = {
            "Brand": "IKEA",
            "Category": "Furniture",
            "Description": "舒適的辦公椅"
        }
        
        name_1 = engine._generate_product_name(test_data_1)
        print(f"   - 測試案例 1: {name_1}")
        
        # 測試案例 2: 只有描述
        test_data_2 = {
            "Description": "高效維生素C補充劑",
            "Main Ingredients": "維生素C 1000mg"
        }
        
        name_2 = engine._generate_product_name(test_data_2)
        print(f"   - 測試案例 2: {name_2}")
        
        # 測試案例 3: 空資料
        test_data_3 = {}
        
        name_3 = engine._generate_product_name(test_data_3)
        print(f"   - 測試案例 3: {name_3}")
        
        if name_1 != "未知產品" and name_2 != "未知產品":
            print("✅ 產品名稱生成功能正常")
            return True
        else:
            print("❌ 產品名稱生成功能有問題")
            return False
            
    except Exception as e:
        print(f"❌ 產品名稱生成測試失敗: {e}")
        return False


def test_excluded_columns():
    """測試排除欄位功能"""
    print("\n🔍 測試排除欄位功能...")
    
    try:
        from core.data_processor import ExcelProcessor
        
        # 載入範例資料
        sample_file = project_root / "sample_products.xlsx"
        if not sample_file.exists():
            print("⚠️ 範例檔案不存在，跳過測試")
            return True
        
        processor = ExcelProcessor()
        success = processor.load_excel(str(sample_file))
        
        if not success:
            print("❌ 無法載入範例檔案")
            return False
        
        # 測試排除欄位
        all_columns = processor.get_columns()
        print(f"   - 所有欄位: {all_columns}")
        
        # 設定排除欄位
        excluded = ["ID", "Image"]
        processor.set_excluded_columns(excluded)
        
        # 取得處理欄位
        processing_columns = processor.get_processing_columns()
        print(f"   - 處理欄位: {processing_columns}")
        print(f"   - 排除欄位: {excluded}")
        
        # 檢查排除是否生效
        for col in excluded:
            if col in processing_columns:
                print(f"❌ 欄位 '{col}' 應該被排除但仍在處理列表中")
                return False
        
        print("✅ 排除欄位功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 排除欄位測試失敗: {e}")
        return False


def test_table_interaction():
    """測試表格互動功能"""
    print("\n🔍 測試表格互動功能...")
    
    try:
        # 這個測試主要檢查程式碼邏輯，實際 GUI 互動需要手動測試
        from gui.main_window import MainWindow
        from config.settings import load_config
        
        # 檢查相關方法是否存在
        methods_to_check = [
            'on_table_cell_clicked',
            'show_html_preview',
            'update_data_table'
        ]
        
        for method in methods_to_check:
            if not hasattr(MainWindow, method):
                print(f"❌ 缺少方法: {method}")
                return False
        
        print("✅ 表格互動方法已實作")
        print("   - 需要手動測試: 點擊表格單元格查看 HTML 預覽")
        return True
        
    except Exception as e:
        print(f"❌ 表格互動測試失敗: {e}")
        return False


def main():
    """主測試函數"""
    print("🔧 AI 商品描述優化系統 - 修復功能測試")
    print("=" * 50)
    
    tests = [
        ("HTML 預覽功能", test_html_preview),
        ("產品名稱生成", test_product_name_generation),
        ("排除欄位功能", test_excluded_columns),
        ("表格互動功能", test_table_interaction),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 測試異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 修復測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有修復功能測試通過！")
        print("\n📋 修復內容:")
        print("1. ✅ HTML 預覽功能 - 支援完整樣式和 WebEngine")
        print("2. ✅ 表格點擊預覽 - 點擊表格查看 HTML 結果")
        print("3. ✅ AI 產品名稱生成 - 自動生成缺失的產品名稱")
        print("4. ✅ 排除欄位功能 - 可勾選要排除的欄位")
        print("\n🚀 現在可以啟動程式測試: python main.py")
    else:
        print("⚠️ 部分修復功能測試失敗，請檢查錯誤訊息。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
