# 📁 Project Structure Documentation

## 🏗️ Complete Project Architecture

```
AI_Description_Editor/
├── 📄 main.py                          # Application entry point
├── 📄 requirements.txt                 # Python dependencies
├── 📄 README.md                        # Comprehensive documentation
├── 📄 LICENSE                          # MIT License
├── 📄 .gitignore                       # Git ignore rules
│
├── 📁 config/                          # Configuration files
│   ├── 📄 __init__.py
│   ├── 📄 settings.py                  # Application settings loader
│   ├── 📄 config.yaml                  # Main configuration file
│   └── 📄 ai_keys.env                  # API keys (not in git)
│
├── 📁 core/                            # Core business logic
│   ├── 📄 __init__.py
│   ├── 📄 ai_models.py                 # AI model management & cost tracking
│   ├── 📄 cost_calculator.py           # Comprehensive cost tracking system
│   ├── 📄 data_processor.py            # Excel data processing
│   ├── 📄 html_generator.py            # HTML generation and formatting
│   ├── 📄 image_processor.py           # Image analysis and processing
│   ├── 📄 keyword_manager.py           # SEO keyword management
│   ├── 📄 processing_engine.py         # Main processing orchestration
│   ├── 📄 prompt_manager.py            # Prompt template management
│   └── 📄 review_engine.py             # Quality review and assessment
│
├── 📁 gui/                             # User interface components
│   ├── 📄 __init__.py
│   ├── 📄 main_window.py               # Main application window
│   ├── 📄 dialogs.py                   # Custom dialog boxes
│   ├── 📄 widgets.py                   # Custom UI widgets
│   └── 📄 styles.py                    # UI styling and themes
│
├── 📁 prompts/                         # Prompt template library
│   ├── 📁 writer/                      # Writer prompt templates
│   │   ├── 📄 default.txt              # Default writer prompt
│   │   ├── 📄 furniture.txt            # Furniture-specific prompt
│   │   ├── 📄 pharmacy.txt             # Pharmacy-specific prompt
│   │   └── 📄 test_writer.txt          # Test prompt (auto-generated)
│   │
│   └── 📁 reviewer/                    # Reviewer prompt templates
│       └── 📄 standard.txt             # Standard review criteria
│
├── 📁 categories/                      # Product category keywords
│   ├── 📄 general_keywords.txt         # Universal keywords
│   ├── 📄 Furniture.txt               # Furniture category keywords
│   └── 📄 Immunity.txt                # Health/immunity keywords
│
├── 📁 templates/                       # HTML and document templates
│   ├── 📄 product_description.html     # Product description template
│   ├── 📄 preview_template.html        # Preview page template
│   └── 📄 export_template.html         # Export format template
│
├── 📁 tests/                           # Test suite
│   ├── 📄 __init__.py
│   ├── 📄 test_ai_models.py            # AI model integration tests
│   ├── 📄 test_cost_calculator.py      # Cost tracking tests
│   ├── 📄 test_data_processor.py       # Data processing tests
│   ├── 📄 test_processing_engine.py    # Core processing tests
│   ├── 📄 test_prompt_manager.py       # Prompt management tests
│   └── 📄 test_gui.py                  # GUI component tests
│
├── 📁 docs/                            # Documentation
│   ├── 📄 API_REFERENCE.md             # API documentation
│   ├── 📄 CONFIGURATION_GUIDE.md       # Configuration instructions
│   ├── 📄 COST_OPTIMIZATION.md         # Cost management guide
│   ├── 📄 PROMPT_WRITING_GUIDE.md      # Prompt creation guide
│   ├── 📄 TROUBLESHOOTING.md           # Common issues and solutions
│   └── 📄 CHANGELOG.md                 # Version history
│
├── 📁 examples/                        # Example files and data
│   ├── 📄 sample_products.xlsx         # Sample product data
│   ├── 📄 config.example.yaml          # Example configuration
│   └── 📁 sample_outputs/              # Example generated outputs
│       ├── 📄 sample_description.html
│       └── 📄 sample_report.xlsx
│
├── 📁 logs/                            # Application logs
│   ├── 📄 app.log                      # Main application log
│   ├── 📄 cost_tracking.log            # Cost tracking log
│   └── 📄 processing.log               # Processing activity log
│
├── 📁 exports/                         # Generated exports
│   ├── 📁 html/                        # HTML exports
│   ├── 📁 excel/                       # Excel exports
│   └── 📁 reports/                     # Cost and usage reports
│
└── 📁 utils/                           # Utility scripts
    ├── 📄 __init__.py
    ├── 📄 backup_manager.py             # Data backup utilities
    ├── 📄 config_validator.py           # Configuration validation
    ├── 📄 data_migrator.py              # Data migration tools
    └── 📄 performance_monitor.py        # Performance monitoring
```

## 🔧 Core Components

### **main.py**
- Application entry point
- Initializes logging and configuration
- Launches the GUI application
- Handles global exception handling

### **config/**
- **settings.py**: Configuration loader and validator
- **config.yaml**: Main application configuration
- **ai_keys.env**: Secure API key storage (gitignored)

### **core/**
- **ai_models.py**: Multi-provider AI model management with cost tracking
- **cost_calculator.py**: Real-time cost calculation and optimization
- **processing_engine.py**: Main processing orchestration
- **prompt_manager.py**: Template management system
- **data_processor.py**: Excel data handling
- **html_generator.py**: HTML output generation

### **gui/**
- **main_window.py**: Primary application interface
- **dialogs.py**: Custom dialog components
- **widgets.py**: Reusable UI components
- **styles.py**: Application theming

## 📊 Data Flow Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Excel Data   │───▶│  Data Processor  │───▶│ Processing Queue│
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────▼─────────┐
│  Cost Tracker  │◀───│   AI Models      │◀───│ Processing Engine│
└─────────────────┘    └──────────────────┘    └─────────┬─────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Cost Calculator │    │  HTML Generator  │    │ Review Engine   │
│      Tab        │    └──────────────────┘    └─────────────────┘
└─────────────────┘             │                       │
                                 ▼                       ▼
                        ┌──────────────────┐    ┌─────────────────┐
                        │  HTML Preview    │    │ Review Results  │
                        │   (Split View)   │    │   (Split View)  │
                        └──────────────────┘    └─────────────────┘
```

## 🎯 Feature Implementation Map

### **Separate AI Model Selection**
- **Files**: `core/ai_models.py`, `gui/main_window.py`
- **Components**: Writer/Reviewer AI dropdowns, model initialization
- **Integration**: Processing engine, cost tracking

### **Cost Tracking System**
- **Files**: `core/cost_calculator.py`, `core/ai_models.py`, `gui/main_window.py`
- **Components**: Real-time tracking, usage statistics, optimization recommendations
- **Features**: Per-model breakdown, free tier monitoring, cost alerts

### **Prompt Management**
- **Files**: `core/prompt_manager.py`, `gui/main_window.py`, `prompts/`
- **Components**: CRUD operations, template library, visual editor
- **Features**: Writer/Reviewer separation, real-time editing, validation

### **Split HTML Preview**
- **Files**: `gui/main_window.py`, `core/html_generator.py`
- **Components**: Split window layout, HTML rendering, reviewer display
- **Features**: Synchronized updates, responsive design, export options

### **Auto Processing Range**
- **Files**: `core/data_processor.py`, `gui/main_window.py`
- **Components**: Data validation, range detection, UI updates
- **Features**: Smart defaults, manual override, progress tracking

### **Model Recommendations**
- **Files**: `core/cost_calculator.py`, `gui/main_window.py`
- **Components**: Pricing analysis, recommendation engine, UI indicators
- **Features**: Economy badges, quality indicators, cost comparisons

## 🔄 Processing Workflow

```
1. Data Loading
   ├── Excel file validation
   ├── Column mapping
   ├── Range detection
   └── Preview generation

2. Configuration
   ├── AI model selection (Writer/Reviewer)
   ├── Prompt selection
   ├── Processing parameters
   └── Cost limits

3. Processing
   ├── Batch preparation
   ├── Writer stage (AI generation)
   ├── Cost tracking
   ├── Reviewer stage (Quality assessment)
   └── Result compilation

4. Output
   ├── HTML preview (split view)
   ├── Cost statistics update
   ├── Data table refresh
   └── Export preparation
```

## 🛠️ Configuration Management

### **config.yaml Structure**
```yaml
# Application settings
app:
  name: "AI Description Editor"
  version: "2.0.0"
  debug: false

# AI model configurations
ai_models:
  openai:
    api_key: "${OPENAI_API_KEY}"
    model: "gpt-4o-mini"
    max_tokens: 2000
    temperature: 0.7
  
  anthropic:
    api_key: "${ANTHROPIC_API_KEY}"
    model: "claude-3-5-sonnet-20241022"
    max_tokens: 2000
    temperature: 0.7
  
  google:
    api_key: "${GOOGLE_API_KEY}"
    model: "gemini-pro"
    max_tokens: 2000
    temperature: 0.7

# Processing settings
processing:
  max_concurrent: 5
  timeout_seconds: 120
  retry_attempts: 3
  default_batch_size: 10

# Cost management
cost_limits:
  daily_budget: 10.00
  per_request_limit: 0.10
  warning_threshold: 0.80

# File paths
paths:
  prompts_dir: "prompts"
  categories_dir: "categories"
  templates_dir: "templates"
  exports_dir: "exports"
  logs_dir: "logs"

# UI settings
ui:
  theme: "default"
  window_size: [1200, 800]
  auto_save: true
  preview_refresh_rate: 1000
```

## 📈 Performance Considerations

### **Memory Management**
- Lazy loading of AI models
- Efficient data processing with pandas
- Image caching and optimization
- Garbage collection for large batches

### **Processing Optimization**
- Asynchronous AI API calls
- Batch processing with progress tracking
- Error handling and retry logic
- Cost-aware processing limits

### **UI Responsiveness**
- Threading for long operations
- Progressive loading of results
- Real-time updates without blocking
- Efficient table rendering

## 🔒 Security Features

### **API Key Management**
- Environment variable storage
- Encrypted configuration options
- Key validation and testing
- Secure transmission protocols

### **Data Protection**
- Local data processing
- No cloud storage of sensitive data
- Audit logging for all operations
- User consent for data usage

## 🧪 Testing Strategy

### **Unit Tests**
- Core component functionality
- AI model integration
- Cost calculation accuracy
- Data processing reliability

### **Integration Tests**
- End-to-end workflow testing
- GUI component interaction
- API integration validation
- Performance benchmarking

### **User Acceptance Tests**
- Real-world usage scenarios
- Cost optimization validation
- Quality assessment accuracy
- User interface usability

This comprehensive project structure supports all the enhanced features while maintaining clean separation of concerns and scalable architecture.
