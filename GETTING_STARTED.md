# 🚀 AI 商品描述優化系統 - 快速開始指南

歡迎使用 AI 商品描述優化系統！這份指南將幫助您快速上手。

## 📋 系統需求

- **Python**: 3.8 或更高版本
- **作業系統**: Windows 10/11, macOS, Linux
- **記憶體**: 建議 4GB 以上
- **網路**: 需要網路連線以使用 AI API

## 🔧 安裝步驟

### 1. 安裝 Python 依賴套件

```bash
pip install -r requirements.txt
```

### 2. 設定 AI API 金鑰

編輯 `config/ai_keys.env` 檔案，填入您的 API 金鑰：

```env
# OpenAI API Key (ChatGPT)
OPENAI_API_KEY=sk-your-openai-key-here

# Anthropic Claude API Key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here

# Google Gemini API Key
GOOGLE_API_KEY=your-google-api-key-here
```

**注意**: 至少需要設定一個 API 金鑰才能使用系統。

### 3. 測試系統

執行系統測試確保一切正常：

```bash
python test_system.py
```

如果看到 "🎉 所有測試通過！" 表示系統準備就緒。

## 🎯 快速開始

### 1. 啟動程式

```bash
python main.py
```

### 2. 載入範例資料

1. 點擊「選擇 Excel」按鈕
2. 選擇 `sample_products.xlsx` 檔案
3. 點擊「載入資料」

### 3. 設定處理參數

1. **AI 模型**: 選擇可用的 AI 模型（Claude/ChatGPT/Gemini）
2. **Writer Prompt**: 選擇描述風格
   - `pharmacy`: 適合藥妝保健品
   - `furniture`: 適合傢俱產品
   - `default`: 通用模板
3. **Reviewer Prompt**: 選擇 `standard` 進行品質審查
4. **處理範圍**: 設定要處理的行數（建議先測試 1-2 行）

### 4. 開始處理

1. 點擊「開始處理」
2. 觀察處理進度和即時日誌
3. 處理完成後查看結果

### 5. 匯出結果

點擊「匯出結果」儲存包含 AI 生成描述的完整 Excel 檔案。

## 📊 輸出欄位說明

處理完成後，Excel 檔案會新增以下欄位：

- **HTML Output**: AI 生成的 HTML 商品描述
- **Used Keywords**: 使用的 SEO 關鍵字
- **Review Notes**: AI 審查員的品質評估

## 🔄 Review-Only 模式

如果某些描述需要改善：

1. 載入已處理的 Excel 檔案
2. 點擊「Review-Only 模式」
3. 系統會自動識別需要改善的項目（標記為 ⚠️ 🛠️ ❌）
4. 重新處理並生成 `Review Notes (v2)` 欄位

## 🎨 自訂設定

### 新增 Prompt 模板

1. 在 `prompts/writer/` 建立新的 `.txt` 檔案
2. 使用變數 `{product_data}` 和 `{keywords}`
3. 重新啟動程式載入新模板

### 新增產品分類關鍵字

1. 在 `categories/` 建立新資料夾
2. 新增 `keywords.txt` 檔案，每行一個關鍵字
3. 系統會自動載入新分類

### 調整 HTML 結構

修改 `config/settings.py` 中的 `html_template.structure` 設定。

## 🖼️ 圖片輔助功能

1. 勾選「啟用圖片輔助」
2. 選擇包含產品圖片的資料夾
3. 確保圖片檔名與 Excel 中的圖片欄位對應
4. AI 會分析圖片內容來改善描述

## 📝 範例工作流程

### 藥妝產品處理流程

1. 準備包含產品資訊的 Excel 檔案
2. 選擇 `pharmacy` Writer Prompt
3. 設定分類為 `Immunity`（如果是免疫力產品）
4. 啟動處理並檢查結果
5. 使用 Review-Only 模式改善品質較差的描述

### 傢俱產品處理流程

1. 準備產品規格和材質資訊
2. 選擇 `furniture` Writer Prompt
3. 啟用圖片輔助功能
4. 設定分類為 `Furniture`
5. 批次處理並匯出結果

## 🛠️ 故障排除

### 常見問題

**Q: 程式無法啟動**
A: 檢查是否已安裝所有依賴套件：`pip install -r requirements.txt`

**Q: API 呼叫失敗**
A: 確認 API 金鑰設定正確且有足夠額度

**Q: Excel 載入失敗**
A: 確保檔案格式為 `.xlsx` 且沒有被其他程式開啟

**Q: 圖片無法載入**
A: 檢查圖片檔案路徑和格式（支援 jpg, png, bmp, gif, webp）

### 日誌檔案

查看 `logs/` 目錄中的日誌檔案來診斷問題：

- `app_YYYYMMDD.log`: 一般操作日誌
- `error_YYYYMMDD.log`: 錯誤日誌
- `usage_stats.csv`: API 使用統計

## 💡 最佳實踐

### 提升描述品質

1. **準備完整的產品資料**: 包含詳細的成分、功效、使用方法等
2. **選擇合適的 Prompt**: 根據產品類型選擇專用模板
3. **使用圖片輔助**: 高品質的產品圖片能顯著改善描述
4. **分批處理**: 先處理少量產品測試效果，再進行大批量處理
5. **善用 Review-Only**: 對品質不滿意的描述進行二次優化

### 節省 API 費用

1. **精確設定處理範圍**: 只處理需要的行數
2. **選擇合適的模型**: 根據需求平衡品質和成本
3. **優化 Prompt**: 清晰的指令能減少重複處理
4. **監控使用量**: 定期檢查 `usage_stats.csv`

## 📞 技術支援

如果遇到問題：

1. 查看 `logs/` 目錄中的錯誤日誌
2. 執行 `python test_system.py` 檢查系統狀態
3. 確認 API 金鑰和網路連線正常
4. 參考 `README.md` 中的詳細說明

## 🎉 開始使用

現在您已經準備好使用 AI 商品描述優化系統了！

建議從範例資料開始，熟悉系統操作後再處理您的實際產品資料。

祝您使用愉快！ 🚀
