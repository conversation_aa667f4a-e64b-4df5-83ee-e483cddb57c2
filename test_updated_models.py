#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Updated AI Models
測試更新的 AI 模型
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_updated_models():
    """測試更新的 AI 模型"""
    print("🔍 測試更新的 AI 模型...")
    
    try:
        from core.cost_calculator import CostCalculator
        from core.ai_models import AIModelManager
        from config.settings import load_config
        
        # 測試成本計算器的新模型
        calculator = CostCalculator()
        print("✅ 成本計算器已載入")
        
        print("\n📊 更新後的 AI 模型和定價:")
        
        # 按廠商分組顯示
        openai_models = {k: v for k, v in calculator.pricing.items() if k.startswith('openai')}
        anthropic_models = {k: v for k, v in calculator.pricing.items() if k.startswith('anthropic')}
        google_models = {k: v for k, v in calculator.pricing.items() if k.startswith('google')}
        
        print("\n🔵 **OpenAI 模型系列**:")
        for model_key, pricing in openai_models.items():
            display_name = pricing['display_name']
            features = pricing.get('features', '')
            roles = ' + '.join(pricing.get('roles', []))
            input_cost = pricing['input'] * 1000
            output_cost = pricing['output'] * 1000
            
            print(f"   🤖 {display_name}")
            print(f"      特色: {features}")
            print(f"      角色: {roles}")
            print(f"      成本: ${input_cost:.4f} input + ${output_cost:.4f} output (per 1K tokens)")
        
        print("\n🟣 **Anthropic Claude 系列**:")
        for model_key, pricing in anthropic_models.items():
            display_name = pricing['display_name']
            features = pricing.get('features', '')
            roles = ' + '.join(pricing.get('roles', []))
            input_cost = pricing['input'] * 1000
            output_cost = pricing['output'] * 1000
            
            print(f"   🤖 {display_name}")
            print(f"      特色: {features}")
            print(f"      角色: {roles}")
            print(f"      成本: ${input_cost:.4f} input + ${output_cost:.4f} output (per 1K tokens)")
        
        print("\n🟢 **Google Gemini 系列**:")
        for model_key, pricing in google_models.items():
            display_name = pricing['display_name']
            features = pricing.get('features', '')
            roles = ' + '.join(pricing.get('roles', []))
            input_cost = pricing['input'] * 1000
            output_cost = pricing['output'] * 1000
            
            print(f"   🤖 {display_name}")
            print(f"      特色: {features}")
            print(f"      角色: {roles}")
            print(f"      成本: ${input_cost:.4f} input + ${output_cost:.4f} output (per 1K tokens)")
            
            if pricing.get('free_tier'):
                free_limit = pricing.get('free_limit', 0)
                print(f"      免費額度: {free_limit:,} tokens/月")
        
        # 測試成本計算
        print("\n💰 成本計算測試 (1000 input + 500 output tokens):")
        test_models = [
            ('openai-gpt4o', 'GPT-4o'),
            ('openai-gpt4o-mini', 'GPT-4o mini'),
            ('anthropic-opus4', 'Claude Opus 4'),
            ('anthropic-sonnet4', 'Claude Sonnet 4'),
            ('google-pro25', 'Gemini 2.5 Pro'),
            ('google-flash25', 'Gemini 2.5 Flash')
        ]
        
        costs = []
        for model_key, display_name in test_models:
            if model_key in calculator.pricing:
                cost = calculator.calculate_cost(model_key, 1000, 500)
                costs.append((display_name, cost))
                print(f"   {display_name}: ${cost:.6f}")
        
        # 排序顯示經濟性
        costs.sort(key=lambda x: x[1])
        print(f"\n🏆 經濟性排名:")
        for i, (name, cost) in enumerate(costs, 1):
            if i == 1:
                print(f"   🥇 {name}: ${cost:.6f} (最經濟)")
            elif i == 2:
                print(f"   🥈 {name}: ${cost:.6f}")
            elif i == 3:
                print(f"   🥉 {name}: ${cost:.6f}")
            else:
                print(f"   {i}. {name}: ${cost:.6f}")
        
        # 測試 AI 模型管理器
        config = load_config()
        ai_manager = AIModelManager(config, calculator)
        print(f"\n✅ AI 模型管理器已載入")
        
        available_models = ai_manager.get_available_models()
        print(f"📋 可用模型鍵值: {available_models}")
        
        print("\n🎉 模型更新測試完成！")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_model_comparison():
    """顯示新舊模型對比"""
    print("\n📊 新舊模型對比:")
    
    print("\n🔄 **OpenAI 變更**:")
    print("   ❌ 移除: GPT-3.5 Turbo")
    print("   ✅ 新增: GPT-4o mini (最經濟選擇)")
    print("   ✅ 保留: GPT-4o (最佳圖片理解)")
    
    print("\n🔄 **Anthropic 變更**:")
    print("   ❌ 移除: Claude 3 Haiku")
    print("   ✅ 升級: Claude 3 Opus → Claude Opus 4")
    print("   ✅ 升級: Claude 3 Sonnet → Claude Sonnet 4")
    
    print("\n🔄 **Google 變更**:")
    print("   ✅ 升級: Gemini 1.5 Pro → Gemini 2.5 Pro")
    print("   ✅ 升級: Gemini 1.5 Flash → Gemini 2.5 Flash")
    print("   ✅ 保留: 免費額度政策")

def show_recommendations():
    """顯示新的推薦配置"""
    print("\n💡 更新後的推薦配置:")
    
    print("\n🥇 **最經濟配置** (日常使用):")
    print("   Writer: GPT-4o mini (最低成本)")
    print("   Reviewer: Gemini 2.5 Flash (經濟快速)")
    print("   預估成本: ~$0.0005 per product")
    print("   適用: 大量產品處理，預算緊張")
    
    print("\n🥈 **平衡配置** (推薦):")
    print("   Writer: Claude Sonnet 4 (平衡品質)")
    print("   Reviewer: Gemini 2.5 Pro (強大分析)")
    print("   預估成本: ~$0.003-0.005 per product")
    print("   適用: 品質與成本平衡")
    
    print("\n🥉 **高品質配置** (精品內容):")
    print("   Writer: Claude Opus 4 (最高品質)")
    print("   Reviewer: GPT-4o (最佳圖片理解)")
    print("   預估成本: ~$0.020-0.030 per product")
    print("   適用: 高價值產品，品質要求極高")
    
    print("\n🆓 **免費測試配置**:")
    print("   Writer: Gemini 2.5 Pro (免費額度)")
    print("   Reviewer: Gemini 2.5 Flash (免費額度)")
    print("   預估成本: $0.00 (免費額度內)")
    print("   適用: 測試和開發階段")
    
    print("\n📸 **圖片處理配置**:")
    print("   Writer: GPT-4o (最佳圖片理解)")
    print("   Reviewer: Claude Opus 4 (精準分析)")
    print("   預估成本: ~$0.025-0.040 per product")
    print("   適用: 需要圖片分析的產品")

def main():
    """主測試函數"""
    print("🚀 AI 模型更新測試")
    print("=" * 60)
    
    success = test_updated_models()
    show_model_comparison()
    show_recommendations()
    
    if success:
        print("\n✅ 模型更新測試成功！")
        print("\n🎯 更新亮點:")
        print("✅ 6 個最新 AI 模型")
        print("✅ 移除過時模型 (GPT-3.5, Claude 3 Haiku)")
        print("✅ 升級到最新版本 (Claude 4, Gemini 2.5)")
        print("✅ 新增超經濟選擇 (GPT-4o mini)")
        print("✅ 精確的角色推薦")
        print("✅ 詳細的特色說明")
        
        print("\n🚀 立即體驗:")
        print("1. 啟動程式: python main.py")
        print("2. 查看更新的 AI 模型選擇")
        print("3. 體驗新的模型特色和推薦")
        print("4. 享受更精確的成本計算")
    else:
        print("\n❌ 測試過程中遇到問題，請檢查錯誤訊息。")
    
    return success

if __name__ == "__main__":
    main()
