2025-08-03 14:40:22 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-03 14:40:22 | INFO     | __main__:main:35 | 配置載入完成
2025-08-03 14:40:22 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-03 14:40:22 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-03 14:40:22 | INFO     | core.ai_models:setup_models:318 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-03 14:40:23 | INFO     | core.ai_models:setup_models:346 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-03 14:40:23 | INFO     | core.ai_models:setup_models:374 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-03 14:40:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-03 14:40:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-03 14:40:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-03 14:40:23 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-03 14:40:23 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-03 14:40:23 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-03 14:40:23 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-03 14:40:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-03 14:40:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-03 14:40:23 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | ✅ 設定已載入
2025-08-03 14:40:25 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-03 14:40:25 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-03 14:41:03 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-03 14:41:03 | INFO     | gui.main_window:log_message:1224 | 已選擇 Excel 檔案: Final Data.xlsx
2025-08-03 14:41:17 | INFO     | gui.main_window:log_message:1224 | 已選擇圖片資料夾: New Product Picture (33 張圖片)
2025-08-03 14:41:19 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Final Data.xlsx
2025-08-03 14:41:19 | INFO     | core.data_processor:load_excel:46 | 成功載入 34 行資料，7 個欄位
2025-08-03 14:41:19 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['Product Type', '\tSerial Number', 'Image Ref', '\tProduct Image', '\tSpecifications / Dimensions', '\tMaterial', '\tName']
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: 無
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 選擇 SEO 欄位: 無
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 無
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: Product Type
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 自動設定處理範圍: 1 到 34 行
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 資料載入成功: 34 行, 7 欄
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 警告: 部分欄位有缺失值
2025-08-03 14:42:41 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-03 14:42:41 | INFO     | gui.main_window:log_message:1224 | 已選擇 Excel 檔案: Final Data.xlsx
2025-08-03 14:42:42 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Final Data.xlsx
2025-08-03 14:42:42 | INFO     | core.data_processor:load_excel:46 | 成功載入 34 行資料，7 個欄位
2025-08-03 14:42:42 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['Product Type', 'Serial Number', 'Image Ref', 'Product Image', 'Specifications / Dimensions', 'Material', 'Name']
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: 無
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 選擇 SEO 欄位: 無
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 無
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: Product Type
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 自動設定處理範圍: 1 到 34 行
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 資料載入成功: 34 行, 7 欄
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 警告: 部分欄位有缺失值
2025-08-03 14:42:48 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: Image Ref
2025-08-03 14:42:51 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: Name
2025-08-03 14:42:53 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 14:42:55 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:42:59 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 14:43:10 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-03 14:43:10 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 無
2025-08-03 14:43:10 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image Ref
2025-08-03 14:43:10 | INFO     | gui.main_window:log_message:1224 | 開始處理 1 個項目...
2025-08-03 14:43:10 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 0 到 0 (共 1 項)
2025-08-03 14:43:10 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-03 14:43:10 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/1 (行 0)
2025-08-03 14:43:10 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['天然', '熱銷', '完整', '全方位', '滋養']
2025-08-03 14:43:10 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Screenshot 2025-08-03 140919' 找到 1 張圖片
2025-08-03 14:43:29 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1027 input + 437 output = 1464 tokens, 成本: $0.000000
2025-08-03 14:43:29 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1464,0.0
2025-08-03 14:43:29 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1464, 時間: 19.33s
2025-08-03 14:43:33 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 988 input + 148 output = 1136 tokens, 成本: $0.000000
2025-08-03 14:43:33 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1136,0.0
2025-08-03 14:43:33 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1136, 時間: 4.20s
2025-08-03 14:43:33 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: SATURA Dining Table (23.54s)
2025-08-03 14:43:33 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 1, 失敗 0
2025-08-03 14:43:33 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-03 14:43:33 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-03 14:43:33 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-03 14:43:33 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 1, 失敗: 0, 總 Token: 2,600, 預估費用: $0.000000
2025-08-03 14:43:33 | INFO     | gui.main_window:log_message:1224 | 自動顯示 SATURA Dining Table 的預覽
2025-08-03 14:46:39 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:46:39 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:46:39 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:46:39 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:46:39 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:46:39 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:52:38 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-03 14:52:38 | INFO     | __main__:main:35 | 配置載入完成
2025-08-03 14:52:38 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-03 14:52:38 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-03 14:52:39 | INFO     | core.ai_models:setup_models:318 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-03 14:52:40 | INFO     | core.ai_models:setup_models:346 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-03 14:52:40 | INFO     | core.ai_models:setup_models:374 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-03 14:52:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-03 14:52:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-03 14:52:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-03 14:52:40 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-03 14:52:40 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-03 14:52:40 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-03 14:52:40 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-03 14:52:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-03 14:52:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-03 14:52:40 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | ✅ 設定已載入
2025-08-03 14:52:42 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-03 14:52:42 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-03 14:55:56 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:55:56 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:55:56 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:55:56 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:55:56 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:55:56 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:58:54 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-03 14:58:54 | INFO     | __main__:main:35 | 配置載入完成
2025-08-03 14:58:54 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-03 14:58:54 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-03 14:58:55 | INFO     | core.ai_models:setup_models:318 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-03 14:58:56 | INFO     | core.ai_models:setup_models:346 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-03 14:58:56 | INFO     | core.ai_models:setup_models:374 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-03 14:58:56 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-03 14:58:56 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-03 14:58:56 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-03 14:58:56 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-03 14:58:56 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-03 14:58:56 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-03 14:58:56 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-03 14:58:56 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-03 14:58:56 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-03 14:58:56 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-03 14:58:57 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:58:57 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-03 14:58:57 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:58:57 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:58:57 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer Prompt: default
2025-08-03 14:58:57 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer Prompt: standard
2025-08-03 14:58:58 | INFO     | gui.main_window:log_message:1228 | 選擇工作表: Sheet1
2025-08-03 14:58:58 | INFO     | gui.main_window:log_message:1228 | ✅ 設定已載入
2025-08-03 14:58:58 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-03 14:58:58 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-03 14:59:06 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ✅圖片理解 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 14:59:14 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ✅圖片理解 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 14:59:16 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer Prompt: furniture
2025-08-03 14:59:21 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Final Data.xlsx
2025-08-03 14:59:21 | INFO     | core.data_processor:load_excel:46 | 成功載入 34 行資料，7 個欄位
2025-08-03 14:59:21 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['Product Type', 'Serial Number', 'Image Ref', 'Product Image', 'Specifications / Dimensions', 'Material', 'Name']
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 選擇圖片欄位: 無
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 選擇 SEO 欄位: 無
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 選擇產品名稱欄位: 無
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 選擇產品名稱欄位: Product Type
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 自動設定處理範圍: 1 到 34 行
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 資料載入成功: 34 行, 7 欄
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 警告: 部分欄位有缺失值
2025-08-03 14:59:25 | INFO     | gui.main_window:log_message:1228 | 選擇圖片欄位: Image Ref
2025-08-03 14:59:28 | INFO     | gui.main_window:log_message:1228 | 選擇產品名稱欄位: Name
2025-08-03 14:59:39 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-03 14:59:39 | INFO     | gui.main_window:log_message:1228 | 排除欄位: 無
2025-08-03 14:59:39 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image Ref
2025-08-03 14:59:39 | INFO     | gui.main_window:log_message:1228 | 開始處理 1 個項目...
2025-08-03 14:59:39 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 0 到 0 (共 1 項)
2025-08-03 14:59:39 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o-mini, Reviewer AI: openai-gpt4o-mini
2025-08-03 14:59:39 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/1 (行 0)
2025-08-03 14:59:39 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['簡單', '低敏', '優質', '無香料', '長效']
2025-08-03 14:59:39 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Screenshot 2025-08-03 140919' 找到 1 張圖片
2025-08-03 14:59:53 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1507 input + 494 output = 2001 tokens, 成本: $0.000000
2025-08-03 14:59:53 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2001,0.0
2025-08-03 14:59:53 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 2001, 時間: 14.56s
2025-08-03 14:59:59 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1056 input + 242 output = 1298 tokens, 成本: $0.000000
2025-08-03 14:59:59 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1298,0.0
2025-08-03 14:59:59 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1298, 時間: 5.97s
2025-08-03 14:59:59 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: SATURA Dining Table (20.54s)
2025-08-03 14:59:59 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 1, 失敗 0
2025-08-03 14:59:59 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-03 14:59:59 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-03 14:59:59 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-03 14:59:59 | INFO     | gui.main_window:log_message:1228 | 處理完成！成功: 1, 失敗: 0, 總 Token: 3,299, 預估費用: $0.000000
2025-08-03 14:59:59 | INFO     | gui.main_window:log_message:1228 | 使用現有名稱: SATURA Dining Table
2025-08-03 14:59:59 | INFO     | gui.main_window:log_message:1228 | ✅ 載入了 1 個現有的 HTML 結果
2025-08-03 14:59:59 | INFO     | gui.main_window:log_message:1228 | 自動顯示 SATURA Dining Table 的預覽
2025-08-03 15:01:40 | INFO     | core.prompt_manager:add_reviewer_prompt:251 | 新增 Reviewer Prompt: standard
2025-08-03 15:01:40 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer Prompt: default
2025-08-03 15:01:40 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer Prompt: standard
2025-08-03 15:01:40 | INFO     | gui.main_window:log_message:1228 | 已儲存 Reviewer Prompt: standard
