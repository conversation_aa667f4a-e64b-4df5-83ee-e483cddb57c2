2025-08-03 14:40:22 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-03 14:40:22 | INFO     | __main__:main:35 | 配置載入完成
2025-08-03 14:40:22 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-03 14:40:22 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-03 14:40:22 | INFO     | core.ai_models:setup_models:318 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-03 14:40:23 | INFO     | core.ai_models:setup_models:346 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-03 14:40:23 | INFO     | core.ai_models:setup_models:374 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-03 14:40:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-03 14:40:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-03 14:40:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-03 14:40:23 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-03 14:40:23 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-03 14:40:23 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-03 14:40:23 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-03 14:40:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-03 14:40:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-03 14:40:23 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-03 14:40:25 | INFO     | gui.main_window:log_message:1224 | ✅ 設定已載入
2025-08-03 14:40:25 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-03 14:40:25 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-03 14:41:03 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-03 14:41:03 | INFO     | gui.main_window:log_message:1224 | 已選擇 Excel 檔案: Final Data.xlsx
2025-08-03 14:41:17 | INFO     | gui.main_window:log_message:1224 | 已選擇圖片資料夾: New Product Picture (33 張圖片)
2025-08-03 14:41:19 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Final Data.xlsx
2025-08-03 14:41:19 | INFO     | core.data_processor:load_excel:46 | 成功載入 34 行資料，7 個欄位
2025-08-03 14:41:19 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['Product Type', '\tSerial Number', 'Image Ref', '\tProduct Image', '\tSpecifications / Dimensions', '\tMaterial', '\tName']
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: 無
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 選擇 SEO 欄位: 無
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 無
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: Product Type
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 自動設定處理範圍: 1 到 34 行
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 資料載入成功: 34 行, 7 欄
2025-08-03 14:41:19 | INFO     | gui.main_window:log_message:1224 | 警告: 部分欄位有缺失值
2025-08-03 14:42:41 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-03 14:42:41 | INFO     | gui.main_window:log_message:1224 | 已選擇 Excel 檔案: Final Data.xlsx
2025-08-03 14:42:42 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Final Data.xlsx
2025-08-03 14:42:42 | INFO     | core.data_processor:load_excel:46 | 成功載入 34 行資料，7 個欄位
2025-08-03 14:42:42 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['Product Type', 'Serial Number', 'Image Ref', 'Product Image', 'Specifications / Dimensions', 'Material', 'Name']
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: 無
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 選擇 SEO 欄位: 無
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: 無
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: Product Type
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 自動設定處理範圍: 1 到 34 行
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 資料載入成功: 34 行, 7 欄
2025-08-03 14:42:42 | INFO     | gui.main_window:log_message:1224 | 警告: 部分欄位有缺失值
2025-08-03 14:42:48 | INFO     | gui.main_window:log_message:1224 | 選擇圖片欄位: Image Ref
2025-08-03 14:42:51 | INFO     | gui.main_window:log_message:1224 | 選擇產品名稱欄位: Name
2025-08-03 14:42:53 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 14:42:55 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:42:59 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 14:43:10 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-03 14:43:10 | INFO     | gui.main_window:log_message:1224 | 排除欄位: 無
2025-08-03 14:43:10 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image Ref
2025-08-03 14:43:10 | INFO     | gui.main_window:log_message:1224 | 開始處理 1 個項目...
2025-08-03 14:43:10 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 0 到 0 (共 1 項)
2025-08-03 14:43:10 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-03 14:43:10 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/1 (行 0)
2025-08-03 14:43:10 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['天然', '熱銷', '完整', '全方位', '滋養']
2025-08-03 14:43:10 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Screenshot 2025-08-03 140919' 找到 1 張圖片
2025-08-03 14:43:29 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1027 input + 437 output = 1464 tokens, 成本: $0.000000
2025-08-03 14:43:29 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1464,0.0
2025-08-03 14:43:29 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1464, 時間: 19.33s
2025-08-03 14:43:33 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 988 input + 148 output = 1136 tokens, 成本: $0.000000
2025-08-03 14:43:33 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1136,0.0
2025-08-03 14:43:33 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1136, 時間: 4.20s
2025-08-03 14:43:33 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: SATURA Dining Table (23.54s)
2025-08-03 14:43:33 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 1, 失敗 0
2025-08-03 14:43:33 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-03 14:43:33 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-03 14:43:33 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-03 14:43:33 | INFO     | gui.main_window:log_message:1224 | 處理完成！成功: 1, 失敗: 0, 總 Token: 2,600, 預估費用: $0.000000
2025-08-03 14:43:33 | INFO     | gui.main_window:log_message:1224 | 自動顯示 SATURA Dining Table 的預覽
2025-08-03 14:46:39 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:46:39 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:46:39 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:46:39 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:46:39 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:46:39 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:52:38 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-03 14:52:38 | INFO     | __main__:main:35 | 配置載入完成
2025-08-03 14:52:38 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-03 14:52:38 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-03 14:52:39 | INFO     | core.ai_models:setup_models:318 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-03 14:52:40 | INFO     | core.ai_models:setup_models:346 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-03 14:52:40 | INFO     | core.ai_models:setup_models:374 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-03 14:52:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-03 14:52:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-03 14:52:40 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-03 14:52:40 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-03 14:52:40 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-03 14:52:40 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-03 14:52:40 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-03 14:52:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-03 14:52:40 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-03 14:52:40 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇 Writer Prompt: default
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇 Reviewer Prompt: standard
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | 選擇工作表: Sheet1
2025-08-03 14:52:42 | INFO     | gui.main_window:log_message:1224 | ✅ 設定已載入
2025-08-03 14:52:42 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-03 14:52:42 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-03 14:55:56 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:55:56 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:55:56 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:55:56 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:55:56 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:55:56 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 14:58:54 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-03 14:58:54 | INFO     | __main__:main:35 | 配置載入完成
2025-08-03 14:58:54 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-03 14:58:54 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-03 14:58:55 | INFO     | core.ai_models:setup_models:318 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-03 14:58:56 | INFO     | core.ai_models:setup_models:346 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-03 14:58:56 | INFO     | core.ai_models:setup_models:374 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-03 14:58:56 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-03 14:58:56 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-03 14:58:56 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-03 14:58:56 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-03 14:58:56 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-03 14:58:56 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-03 14:58:56 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-03 14:58:56 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-03 14:58:56 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-03 14:58:56 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-03 14:58:57 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:58:57 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-03 14:58:57 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:58:57 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 14:58:57 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer Prompt: default
2025-08-03 14:58:57 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer Prompt: standard
2025-08-03 14:58:58 | INFO     | gui.main_window:log_message:1228 | 選擇工作表: Sheet1
2025-08-03 14:58:58 | INFO     | gui.main_window:log_message:1228 | ✅ 設定已載入
2025-08-03 14:58:58 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-03 14:58:58 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-03 14:59:06 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ✅圖片理解 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 14:59:14 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ✅圖片理解 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 14:59:16 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer Prompt: furniture
2025-08-03 14:59:21 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Final Data.xlsx
2025-08-03 14:59:21 | INFO     | core.data_processor:load_excel:46 | 成功載入 34 行資料，7 個欄位
2025-08-03 14:59:21 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['Product Type', 'Serial Number', 'Image Ref', 'Product Image', 'Specifications / Dimensions', 'Material', 'Name']
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 選擇圖片欄位: 無
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 選擇 SEO 欄位: 無
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 選擇產品名稱欄位: 無
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 選擇產品名稱欄位: Product Type
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 自動設定處理範圍: 1 到 34 行
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 資料載入成功: 34 行, 7 欄
2025-08-03 14:59:21 | INFO     | gui.main_window:log_message:1228 | 警告: 部分欄位有缺失值
2025-08-03 14:59:25 | INFO     | gui.main_window:log_message:1228 | 選擇圖片欄位: Image Ref
2025-08-03 14:59:28 | INFO     | gui.main_window:log_message:1228 | 選擇產品名稱欄位: Name
2025-08-03 14:59:39 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-03 14:59:39 | INFO     | gui.main_window:log_message:1228 | 排除欄位: 無
2025-08-03 14:59:39 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image Ref
2025-08-03 14:59:39 | INFO     | gui.main_window:log_message:1228 | 開始處理 1 個項目...
2025-08-03 14:59:39 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 0 到 0 (共 1 項)
2025-08-03 14:59:39 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o-mini, Reviewer AI: openai-gpt4o-mini
2025-08-03 14:59:39 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/1 (行 0)
2025-08-03 14:59:39 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['簡單', '低敏', '優質', '無香料', '長效']
2025-08-03 14:59:39 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Screenshot 2025-08-03 140919' 找到 1 張圖片
2025-08-03 14:59:53 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1507 input + 494 output = 2001 tokens, 成本: $0.000000
2025-08-03 14:59:53 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2001,0.0
2025-08-03 14:59:53 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 2001, 時間: 14.56s
2025-08-03 14:59:59 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1056 input + 242 output = 1298 tokens, 成本: $0.000000
2025-08-03 14:59:59 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1298,0.0
2025-08-03 14:59:59 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1298, 時間: 5.97s
2025-08-03 14:59:59 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: SATURA Dining Table (20.54s)
2025-08-03 14:59:59 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 1, 失敗 0
2025-08-03 14:59:59 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-03 14:59:59 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-03 14:59:59 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-03 14:59:59 | INFO     | gui.main_window:log_message:1228 | 處理完成！成功: 1, 失敗: 0, 總 Token: 3,299, 預估費用: $0.000000
2025-08-03 14:59:59 | INFO     | gui.main_window:log_message:1228 | 使用現有名稱: SATURA Dining Table
2025-08-03 14:59:59 | INFO     | gui.main_window:log_message:1228 | ✅ 載入了 1 個現有的 HTML 結果
2025-08-03 14:59:59 | INFO     | gui.main_window:log_message:1228 | 自動顯示 SATURA Dining Table 的預覽
2025-08-03 15:01:40 | INFO     | core.prompt_manager:add_reviewer_prompt:251 | 新增 Reviewer Prompt: standard
2025-08-03 15:01:40 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer Prompt: default
2025-08-03 15:01:40 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer Prompt: standard
2025-08-03 15:01:40 | INFO     | gui.main_window:log_message:1228 | 已儲存 Reviewer Prompt: standard
2025-08-03 15:05:25 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:05:25 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:05:25 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:05:25 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:05:25 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:05:25 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:37:51 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-03 15:37:51 | INFO     | __main__:main:35 | 配置載入完成
2025-08-03 15:37:51 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-03 15:37:51 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-03 15:37:52 | INFO     | core.ai_models:setup_models:318 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-03 15:37:52 | INFO     | core.ai_models:setup_models:346 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-03 15:37:52 | INFO     | core.ai_models:setup_models:374 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-03 15:37:52 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-03 15:37:52 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-03 15:37:52 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-03 15:37:52 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-03 15:37:52 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-03 15:37:52 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-03 15:37:52 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-03 15:37:52 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-03 15:37:52 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-03 15:37:52 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-03 15:37:54 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 15:37:54 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-03 15:37:54 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 15:37:54 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 15:37:54 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer Prompt: default
2025-08-03 15:37:54 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer Prompt: standard
2025-08-03 15:37:54 | INFO     | gui.main_window:log_message:1228 | 選擇工作表: Sheet1
2025-08-03 15:37:54 | INFO     | gui.main_window:log_message:1228 | ✅ 設定已載入
2025-08-03 15:37:54 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-03 15:37:55 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-03 15:38:07 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Final Data.xlsx
2025-08-03 15:38:07 | INFO     | core.data_processor:load_excel:46 | 成功載入 34 行資料，7 個欄位
2025-08-03 15:38:07 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['Product Type', 'Serial Number', 'Image Ref', 'Product Image', 'Specifications / Dimensions', 'Material', 'Name']
2025-08-03 15:38:07 | INFO     | gui.main_window:log_message:1228 | 選擇圖片欄位: 無
2025-08-03 15:38:07 | INFO     | gui.main_window:log_message:1228 | 選擇 SEO 欄位: 無
2025-08-03 15:38:07 | INFO     | gui.main_window:log_message:1228 | 選擇產品名稱欄位: 無
2025-08-03 15:38:07 | INFO     | gui.main_window:log_message:1228 | 選擇產品名稱欄位: Product Type
2025-08-03 15:38:07 | INFO     | gui.main_window:log_message:1228 | 自動設定處理範圍: 1 到 34 行
2025-08-03 15:38:07 | INFO     | gui.main_window:log_message:1228 | 資料載入成功: 34 行, 7 欄
2025-08-03 15:38:07 | INFO     | gui.main_window:log_message:1228 | 警告: 部分欄位有缺失值
2025-08-03 15:38:09 | INFO     | gui.main_window:log_message:1228 | 選擇圖片欄位: Image Ref
2025-08-03 15:38:14 | INFO     | gui.main_window:log_message:1228 | 選擇產品名稱欄位: Name
2025-08-03 15:38:21 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ✅圖片理解 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 15:38:22 | INFO     | gui.main_window:log_message:1228 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ✅圖片理解 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 15:38:27 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-03 15:38:27 | INFO     | gui.main_window:log_message:1228 | 排除欄位: 無
2025-08-03 15:38:27 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image Ref
2025-08-03 15:38:27 | INFO     | gui.main_window:log_message:1228 | 開始處理 1 個項目...
2025-08-03 15:38:27 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 0 到 0 (共 1 項)
2025-08-03 15:38:27 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o-mini, Reviewer AI: openai-gpt4o-mini
2025-08-03 15:38:27 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/1 (行 0)
2025-08-03 15:38:27 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['方便', '即時', '高品質', '健康', '專業']
2025-08-03 15:38:27 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Screenshot 2025-08-03 140919' 找到 1 張圖片
2025-08-03 15:38:46 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1059 input + 517 output = 1576 tokens, 成本: $0.000000
2025-08-03 15:38:46 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1576,0.0
2025-08-03 15:38:46 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1576, 時間: 19.32s
2025-08-03 15:38:51 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1076 input + 182 output = 1258 tokens, 成本: $0.000000
2025-08-03 15:38:51 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1258,0.0
2025-08-03 15:38:51 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1258, 時間: 4.57s
2025-08-03 15:38:51 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: SATURA Dining Table (23.90s)
2025-08-03 15:38:51 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 1, 失敗 0
2025-08-03 15:38:51 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-03 15:38:51 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-03 15:38:51 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-03 15:38:51 | INFO     | gui.main_window:log_message:1228 | 處理完成！成功: 1, 失敗: 0, 總 Token: 2,834, 預估費用: $0.000000
2025-08-03 15:38:51 | INFO     | gui.main_window:log_message:1228 | 使用現有名稱: SATURA Dining Table
2025-08-03 15:38:51 | INFO     | gui.main_window:log_message:1228 | ✅ 載入了 1 個現有的 HTML 結果
2025-08-03 15:38:51 | INFO     | gui.main_window:log_message:1228 | 自動顯示 SATURA Dining Table 的預覽
2025-08-03 15:39:01 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer Prompt: furniture
2025-08-03 15:39:02 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-03 15:39:02 | INFO     | gui.main_window:log_message:1228 | 排除欄位: 無
2025-08-03 15:39:02 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image Ref
2025-08-03 15:39:02 | INFO     | gui.main_window:log_message:1228 | 開始處理 1 個項目...
2025-08-03 15:39:02 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 0 到 0 (共 1 項)
2025-08-03 15:39:02 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o-mini, Reviewer AI: openai-gpt4o-mini
2025-08-03 15:39:02 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/1 (行 0)
2025-08-03 15:39:02 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 1)
2025-08-03 15:39:02 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '健康', '品質', '方便', '即時']
2025-08-03 15:39:02 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Screenshot 2025-08-03 140919' 找到 1 張圖片
2025-08-03 15:39:26 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 2264 input + 576 output = 2840 tokens, 成本: $0.000000
2025-08-03 15:39:26 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2840,0.0
2025-08-03 15:39:26 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 2840, 時間: 24.16s
2025-08-03 15:39:31 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1891 input + 179 output = 2070 tokens, 成本: $0.000000
2025-08-03 15:39:31 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2070,0.0
2025-08-03 15:39:31 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 2070, 時間: 5.50s
2025-08-03 15:39:31 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: SATURA Dining Table (29.67s)
2025-08-03 15:39:31 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 1, 失敗 0
2025-08-03 15:39:31 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-03 15:39:31 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-03 15:39:31 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-03 15:39:31 | INFO     | gui.main_window:log_message:1228 | 處理完成！成功: 1, 失敗: 0, 總 Token: 4,910, 預估費用: $0.000000
2025-08-03 15:39:31 | INFO     | gui.main_window:log_message:1228 | 使用現有名稱: SATURA Dining Table
2025-08-03 15:39:31 | INFO     | gui.main_window:log_message:1228 | ✅ 載入了 1 個現有的 HTML 結果
2025-08-03 15:39:31 | INFO     | gui.main_window:log_message:1228 | 自動顯示 SATURA Dining Table 的預覽
2025-08-03 15:39:41 | INFO     | gui.main_window:log_message:1228 | ✅ 所有結果已重置
2025-08-03 15:39:52 | INFO     | gui.main_window:log_message:1228 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 15:39:54 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-03 15:39:54 | INFO     | gui.main_window:log_message:1228 | 排除欄位: 無
2025-08-03 15:39:54 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image Ref
2025-08-03 15:39:54 | INFO     | gui.main_window:log_message:1228 | 開始處理 1 個項目...
2025-08-03 15:39:54 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 0 到 0 (共 1 項)
2025-08-03 15:39:54 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-03 15:39:54 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/1 (行 0)
2025-08-03 15:39:54 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['方便', '即時', '高品質', '健康', '專業']
2025-08-03 15:39:54 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Screenshot 2025-08-03 140919' 找到 1 張圖片
2025-08-03 15:40:10 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1521 input + 479 output = 2000 tokens, 成本: $0.000000
2025-08-03 15:40:10 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2000,0.0
2025-08-03 15:40:10 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 2000, 時間: 16.64s
2025-08-03 15:40:16 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1043 input + 204 output = 1247 tokens, 成本: $0.000000
2025-08-03 15:40:16 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1247,0.0
2025-08-03 15:40:16 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 1247, 時間: 5.60s
2025-08-03 15:40:16 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: SATURA Dining Table (22.25s)
2025-08-03 15:40:16 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 1, 失敗 0
2025-08-03 15:40:16 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-03 15:40:16 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-03 15:40:16 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-03 15:40:16 | INFO     | gui.main_window:log_message:1228 | 處理完成！成功: 1, 失敗: 0, 總 Token: 3,247, 預估費用: $0.000000
2025-08-03 15:40:16 | INFO     | gui.main_window:log_message:1228 | 使用現有名稱: SATURA Dining Table
2025-08-03 15:40:16 | INFO     | gui.main_window:log_message:1228 | ✅ 載入了 1 個現有的 HTML 結果
2025-08-03 15:40:16 | INFO     | gui.main_window:log_message:1228 | 自動顯示 SATURA Dining Table 的預覽
2025-08-03 15:42:46 | INFO     | gui.main_window:log_message:1228 | 選擇圖片欄位: Product Image
2025-08-03 15:42:47 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-03 15:42:47 | INFO     | gui.main_window:log_message:1228 | 排除欄位: 無
2025-08-03 15:42:47 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Product Image
2025-08-03 15:42:47 | INFO     | gui.main_window:log_message:1228 | 開始處理 1 個項目...
2025-08-03 15:42:47 | INFO     | core.processing_engine:process_batch:379 | 開始批次處理: 行 0 到 0 (共 1 項)
2025-08-03 15:42:47 | INFO     | core.processing_engine:process_batch:380 | Writer AI: openai-gpt4o, Reviewer AI: openai-gpt4o-mini
2025-08-03 15:42:47 | INFO     | core.processing_engine:process_batch:383 | 處理項目 1/1 (行 0)
2025-08-03 15:42:47 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 3)
2025-08-03 15:42:47 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '健康', '品質', '風格', '方便']
2025-08-03 15:43:01 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1827 input + 507 output = 2334 tokens, 成本: $0.000000
2025-08-03 15:43:01 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2334,0.0
2025-08-03 15:43:01 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 2334, 時間: 13.31s
2025-08-03 15:43:04 | INFO     | core.ai_models:generate_text:119 | OpenAI 使用記錄: 1800 input + 204 output = 2004 tokens, 成本: $0.000000
2025-08-03 15:43:04 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2004,0.0
2025-08-03 15:43:04 | INFO     | core.ai_models:generate_text:418 | AI 生成完成 - 模型: openai-gpt4o, Token: 2004, 時間: 3.23s
2025-08-03 15:43:04 | INFO     | core.processing_engine:process_batch:398 | 項目處理成功: SATURA Dining Table (16.55s)
2025-08-03 15:43:04 | INFO     | core.processing_engine:process_batch:402 | 批次處理完成: 成功 1, 失敗 0
2025-08-03 15:43:04 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-03 15:43:04 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-03 15:43:04 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-03 15:43:04 | INFO     | gui.main_window:log_message:1228 | 處理完成！成功: 1, 失敗: 0, 總 Token: 4,338, 預估費用: $0.000000
2025-08-03 15:43:04 | INFO     | gui.main_window:log_message:1228 | 使用現有名稱: SATURA Dining Table
2025-08-03 15:43:04 | INFO     | gui.main_window:log_message:1228 | ✅ 載入了 1 個現有的 HTML 結果
2025-08-03 15:43:04 | INFO     | gui.main_window:log_message:1228 | 自動顯示 SATURA Dining Table 的預覽
2025-08-03 15:43:25 | INFO     | gui.main_window:log_message:1228 | 選擇圖片欄位: Image Ref
2025-08-03 15:45:58 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:45:58 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:45:58 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:45:58 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:45:58 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:45:58 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:51:32 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-03 15:51:32 | INFO     | __main__:main:35 | 配置載入完成
2025-08-03 15:51:32 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-03 15:51:32 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-03 15:51:32 | INFO     | core.ai_models:setup_models:326 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-03 15:51:33 | INFO     | core.ai_models:setup_models:354 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-03 15:51:33 | INFO     | core.ai_models:setup_models:382 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-03 15:51:33 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-03 15:51:33 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-03 15:51:33 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-03 15:51:33 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-03 15:51:33 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-03 15:51:33 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-03 15:51:33 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-03 15:51:33 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-03 15:51:33 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-03 15:51:33 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-03 15:51:34 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 15:51:34 | INFO     | gui.main_window:log_message:1236 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-03 15:51:34 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 15:51:34 | INFO     | gui.main_window:log_message:1236 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 15:51:34 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer Prompt: default
2025-08-03 15:51:34 | INFO     | gui.main_window:log_message:1236 | 選擇 Reviewer Prompt: standard
2025-08-03 15:51:35 | INFO     | gui.main_window:log_message:1236 | 選擇工作表: Sheet1
2025-08-03 15:51:35 | INFO     | gui.main_window:log_message:1236 | ✅ 設定已載入
2025-08-03 15:51:35 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-03 15:51:35 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-03 15:51:40 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Final Data.xlsx
2025-08-03 15:51:40 | INFO     | core.data_processor:load_excel:46 | 成功載入 34 行資料，7 個欄位
2025-08-03 15:51:40 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['Product Type', 'Serial Number', 'Image Ref', 'Product Image', 'Specifications / Dimensions', 'Material', 'Name']
2025-08-03 15:51:40 | INFO     | gui.main_window:log_message:1236 | 選擇圖片欄位: 無
2025-08-03 15:51:40 | INFO     | gui.main_window:log_message:1236 | 選擇 SEO 欄位: 無
2025-08-03 15:51:40 | INFO     | gui.main_window:log_message:1236 | 選擇產品名稱欄位: 無
2025-08-03 15:51:40 | INFO     | gui.main_window:log_message:1236 | 選擇產品名稱欄位: Product Type
2025-08-03 15:51:40 | INFO     | gui.main_window:log_message:1236 | 自動設定處理範圍: 1 到 34 行
2025-08-03 15:51:40 | INFO     | gui.main_window:log_message:1236 | 資料載入成功: 34 行, 7 欄
2025-08-03 15:51:40 | INFO     | gui.main_window:log_message:1236 | 警告: 部分欄位有缺失值
2025-08-03 15:51:48 | INFO     | gui.main_window:log_message:1236 | 選擇圖片欄位: Image Ref
2025-08-03 15:51:50 | INFO     | gui.main_window:log_message:1236 | 選擇產品名稱欄位: Name
2025-08-03 15:51:52 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer Prompt: furniture
2025-08-03 15:51:56 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ✅圖片理解 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 15:51:57 | INFO     | gui.main_window:log_message:1236 | 選擇 Reviewer AI 模型: GPT-4o mini | OpenAI | ✅圖片理解 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 15:52:02 | INFO     | gui.main_window:log_message:1236 | ✅ 圖片處理器已啟用: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture
2025-08-03 15:52:02 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-03 15:52:02 | INFO     | gui.main_window:log_message:1236 | 排除欄位: 無
2025-08-03 15:52:02 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image Ref
2025-08-03 15:52:02 | INFO     | gui.main_window:log_message:1236 | 開始處理 1 個項目...
2025-08-03 15:52:02 | INFO     | core.processing_engine:process_batch:389 | 開始批次處理: 行 1 到 1 (共 1 項)
2025-08-03 15:52:02 | INFO     | core.processing_engine:process_batch:390 | Writer AI: openai-gpt4o-mini, Reviewer AI: openai-gpt4o-mini
2025-08-03 15:52:02 | INFO     | core.processing_engine:process_batch:393 | 處理項目 1/1 (行 1)
2025-08-03 15:52:02 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '推薦', '完整', '有效', '保養']
2025-08-03 15:52:02 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 1: enable_images=True, image_processor=True, filename='0005_22-1_Auto-generated_with_background'
2025-08-03 15:52:02 | DEBUG    | core.data_processor:find_images:327 | 檔名 '0005_22-1_Auto-generated_with_background' 找到 1 張圖片
2025-08-03 15:52:02 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '0005_22-1_Auto-generated_with_background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\0005_22-1_Auto-generated_with_background.jpg']
2025-08-03 15:52:02 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 15:52:02 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 15:52:02 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 15:52:02 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\0005_22-1_Auto-generated_with_background.jpg
2025-08-03 15:52:02 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: 0005_22-1_Auto-generated_with_background.jpg
2025-08-03 15:52:28 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1869 input + 525 output = 2394 tokens, 成本: $0.000000
2025-08-03 15:52:28 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2394,0.0
2025-08-03 15:52:28 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2394, 時間: 25.64s
2025-08-03 15:52:28 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 0 images
2025-08-03 15:52:35 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1091 input + 263 output = 1354 tokens, 成本: $0.000000
2025-08-03 15:52:35 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1354,0.0
2025-08-03 15:52:35 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 1354, 時間: 7.26s
2025-08-03 15:52:35 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: ARCTON Extendable Dining Table  (32.92s)
2025-08-03 15:52:35 | INFO     | core.processing_engine:process_batch:412 | 批次處理完成: 成功 1, 失敗 0
2025-08-03 15:52:35 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-03 15:52:35 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-03 15:52:35 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-03 15:52:35 | INFO     | gui.main_window:log_message:1236 | 處理完成！成功: 1, 失敗: 0, 總 Token: 3,748, 預估費用: $0.000000
2025-08-03 15:52:35 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: ARCTON Extendable Dining Table 
2025-08-03 15:52:35 | INFO     | gui.main_window:log_message:1236 | ✅ 載入了 1 個現有的 HTML 結果
2025-08-03 15:52:35 | INFO     | gui.main_window:log_message:1236 | 自動顯示 ARCTON Extendable Dining Table  的預覽
2025-08-03 15:55:06 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:55:06 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:55:06 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:55:06 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:55:06 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:55:06 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 15:57:22 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-03 15:57:22 | INFO     | __main__:main:35 | 配置載入完成
2025-08-03 15:57:22 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-03 15:57:22 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-03 15:57:22 | INFO     | core.ai_models:setup_models:326 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-03 15:57:23 | INFO     | core.ai_models:setup_models:354 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-03 15:57:23 | INFO     | core.ai_models:setup_models:382 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-03 15:57:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-03 15:57:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-03 15:57:23 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-03 15:57:23 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-03 15:57:23 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-03 15:57:23 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-03 15:57:23 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-03 15:57:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-03 15:57:23 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-03 15:57:23 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-03 15:57:24 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 15:57:24 | INFO     | gui.main_window:log_message:1236 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-03 15:57:24 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 15:57:24 | INFO     | gui.main_window:log_message:1236 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 15:57:24 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer Prompt: default
2025-08-03 15:57:24 | INFO     | gui.main_window:log_message:1236 | 選擇 Reviewer Prompt: standard
2025-08-03 15:57:25 | INFO     | gui.main_window:log_message:1236 | 選擇工作表: Sheet1
2025-08-03 15:57:25 | INFO     | gui.main_window:log_message:1236 | ✅ 設定已載入
2025-08-03 15:57:25 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-03 15:57:25 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-03 15:57:27 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Final Data.xlsx
2025-08-03 15:57:28 | INFO     | core.data_processor:load_excel:46 | 成功載入 34 行資料，7 個欄位
2025-08-03 15:57:28 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['Product Type', 'Name', 'Product Image', 'Serial Number', 'Image Ref', 'Specifications / Dimensions', 'Material']
2025-08-03 15:57:28 | INFO     | gui.main_window:log_message:1236 | 選擇圖片欄位: 無
2025-08-03 15:57:28 | INFO     | gui.main_window:log_message:1236 | 選擇 SEO 欄位: 無
2025-08-03 15:57:28 | INFO     | gui.main_window:log_message:1236 | 選擇產品名稱欄位: 無
2025-08-03 15:57:28 | INFO     | gui.main_window:log_message:1236 | 選擇產品名稱欄位: Product Type
2025-08-03 15:57:28 | INFO     | gui.main_window:log_message:1236 | 自動設定處理範圍: 1 到 34 行
2025-08-03 15:57:28 | INFO     | gui.main_window:log_message:1236 | 資料載入成功: 34 行, 7 欄
2025-08-03 15:57:28 | INFO     | gui.main_window:log_message:1236 | 警告: 部分欄位有缺失值
2025-08-03 15:57:32 | INFO     | gui.main_window:log_message:1236 | 選擇圖片欄位: Image Ref
2025-08-03 15:57:35 | INFO     | gui.main_window:log_message:1236 | 選擇產品名稱欄位: Name
2025-08-03 15:57:37 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ✅圖片理解 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 15:57:47 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer Prompt: furniture
2025-08-03 15:57:57 | INFO     | gui.main_window:log_message:1236 | ✅ 圖片處理器已啟用: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture
2025-08-03 15:57:57 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-03 15:57:57 | INFO     | gui.main_window:log_message:1236 | 排除欄位: 無
2025-08-03 15:57:57 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image Ref
2025-08-03 15:57:57 | INFO     | gui.main_window:log_message:1236 | 開始處理 2 個項目...
2025-08-03 15:57:57 | INFO     | core.processing_engine:process_batch:389 | 開始批次處理: 行 1 到 2 (共 2 項)
2025-08-03 15:57:57 | INFO     | core.processing_engine:process_batch:390 | Writer AI: openai-gpt4o-mini, Reviewer AI: anthropic-sonnet4
2025-08-03 15:57:57 | INFO     | core.processing_engine:process_batch:393 | 處理項目 1/2 (行 1)
2025-08-03 15:57:57 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '保養', '專業', '經典', '簡單']
2025-08-03 15:57:57 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 1: enable_images=True, image_processor=True, filename='0005_22-1_Auto-generated_with_background'
2025-08-03 15:57:57 | DEBUG    | core.data_processor:find_images:327 | 檔名 '0005_22-1_Auto-generated_with_background' 找到 1 張圖片
2025-08-03 15:57:57 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '0005_22-1_Auto-generated_with_background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\0005_22-1_Auto-generated_with_background.jpg']
2025-08-03 15:57:57 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 15:57:57 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 15:57:57 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 15:57:57 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\0005_22-1_Auto-generated_with_background.jpg
2025-08-03 15:57:57 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: 0005_22-1_Auto-generated_with_background.jpg
2025-08-03 15:58:19 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1869 input + 542 output = 2411 tokens, 成本: $0.000000
2025-08-03 15:58:19 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2411,0.0
2025-08-03 15:58:19 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2411, 時間: 22.28s
2025-08-03 15:58:27 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1369 input + 388 output = 1757 tokens, 成本: $0.000000
2025-08-03 15:58:27 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1757,0.0
2025-08-03 15:58:27 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1757, 時間: 7.92s
2025-08-03 15:58:27 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: ARCTON Extendable Dining Table  (30.21s)
2025-08-03 15:58:27 | INFO     | core.processing_engine:process_batch:393 | 處理項目 2/2 (行 2)
2025-08-03 15:58:27 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '保養', '專業', '經典', '簡單']
2025-08-03 15:58:27 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 2: enable_images=True, image_processor=True, filename='0000_24-2_Auto-generatedwithbackground'
2025-08-03 15:58:27 | DEBUG    | core.data_processor:find_images:327 | 檔名 '0000_24-2_Auto-generatedwithbackground' 找到 1 張圖片
2025-08-03 15:58:27 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '0000_24-2_Auto-generatedwithbackground': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\0000_24-2_Auto-generatedwithbackground.jpg']
2025-08-03 15:58:27 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 15:58:27 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 15:58:27 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 15:58:27 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\0000_24-2_Auto-generatedwithbackground.jpg
2025-08-03 15:58:27 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: 0000_24-2_Auto-generatedwithbackground.jpg
2025-08-03 15:58:50 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1879 input + 527 output = 2406 tokens, 成本: $0.000000
2025-08-03 15:58:50 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2406,0.0
2025-08-03 15:58:50 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2406, 時間: 22.71s
2025-08-03 15:58:58 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1338 input + 398 output = 1736 tokens, 成本: $0.000000
2025-08-03 15:58:58 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1736,0.0
2025-08-03 15:58:58 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1736, 時間: 8.16s
2025-08-03 15:58:58 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: NOVA 140 Dining Table – White Sintered Stone & Car (30.87s)
2025-08-03 15:58:58 | INFO     | core.processing_engine:process_batch:412 | 批次處理完成: 成功 2, 失敗 0
2025-08-03 15:58:58 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-03 15:58:58 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-03 15:58:58 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-03 15:58:58 | INFO     | gui.main_window:log_message:1236 | 處理完成！成功: 2, 失敗: 0, 總 Token: 8,310, 預估費用: $0.000000
2025-08-03 15:58:58 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: ARCTON Extendable Dining Table 
2025-08-03 15:58:58 | INFO     | gui.main_window:log_message:1236 | ✅ 載入了 2 個現有的 HTML 結果
2025-08-03 15:58:58 | INFO     | gui.main_window:log_message:1236 | 自動顯示 ARCTON Extendable Dining Table  的預覽
2025-08-03 16:00:02 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: NOVA 140 Dining Table – White Sintered Stone & Carbon Steel
2025-08-03 16:00:57 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 16:00:57 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 16:00:57 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 16:00:57 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 16:00:57 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 16:00:57 | INFO     | core.settings_manager:save_settings:77 | 設定已儲存: user_settings.json
2025-08-03 16:01:06 | INFO     | __main__:main:30 | === AI Description Editor 啟動 ===
2025-08-03 16:01:06 | INFO     | __main__:main:35 | 配置載入完成
2025-08-03 16:01:06 | INFO     | core.settings_manager:load_settings:61 | 設定已載入: user_settings.json
2025-08-03 16:01:06 | WARNING  | core.seo_manager:load_seo_files:33 | SEO 目錄不存在
2025-08-03 16:01:06 | INFO     | core.ai_models:setup_models:326 | OpenAI 模型系列已設定 (GPT-4o, GPT-4o mini)
2025-08-03 16:01:07 | INFO     | core.ai_models:setup_models:354 | Anthropic Claude 系列已設定 (Opus 4, Sonnet 4)
2025-08-03 16:01:07 | INFO     | core.ai_models:setup_models:382 | Google Gemini 系列已設定 (2.5 Pro, 2.5 Flash)
2025-08-03 16:01:07 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: default
2025-08-03 16:01:07 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: furniture
2025-08-03 16:01:07 | DEBUG    | core.prompt_manager:load_writer_prompts:42 | 載入 Writer Prompt: pharmacy
2025-08-03 16:01:07 | INFO     | core.prompt_manager:load_writer_prompts:47 | 載入 3 個 Writer Prompt
2025-08-03 16:01:07 | DEBUG    | core.prompt_manager:load_reviewer_prompts:63 | 載入 Reviewer Prompt: standard
2025-08-03 16:01:07 | INFO     | core.prompt_manager:load_reviewer_prompts:68 | 載入 1 個 Reviewer Prompt
2025-08-03 16:01:07 | INFO     | core.keyword_manager:load_general_keywords:42 | 載入 44 個通用關鍵字
2025-08-03 16:01:07 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Furniture' 的 25 個關鍵字
2025-08-03 16:01:07 | INFO     | core.keyword_manager:load_category_keywords:67 | 載入分類 'Immunity' 的 24 個關鍵字
2025-08-03 16:01:07 | INFO     | gui.main_window:init_components:163 | 核心組件初始化完成
2025-08-03 16:01:08 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer AI 模型: GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 16:01:08 | INFO     | gui.main_window:log_message:1236 | 選擇 Reviewer AI 模型: 🚫 無需審查
2025-08-03 16:01:08 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 16:01:08 | INFO     | gui.main_window:log_message:1236 | 選擇 Reviewer AI 模型: Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer
2025-08-03 16:01:08 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer Prompt: default
2025-08-03 16:01:08 | INFO     | gui.main_window:log_message:1236 | 選擇 Reviewer Prompt: standard
2025-08-03 16:01:08 | INFO     | gui.main_window:log_message:1236 | 選擇工作表: Sheet1
2025-08-03 16:01:08 | INFO     | gui.main_window:log_message:1236 | ✅ 設定已載入
2025-08-03 16:01:08 | INFO     | gui.main_window:__init__:150 | 主視窗初始化完成
2025-08-03 16:01:08 | INFO     | __main__:main:50 | GUI 介面已啟動
2025-08-03 16:01:21 | INFO     | core.data_processor:load_excel:37 | 載入 Excel 檔案: C:/Users/<USER>/OneDrive/trizenith/AI_Description_Editor/Final Data.xlsx
2025-08-03 16:01:21 | INFO     | core.data_processor:load_excel:46 | 成功載入 34 行資料，7 個欄位
2025-08-03 16:01:21 | DEBUG    | core.data_processor:load_excel:47 | 欄位名稱: ['Product Type', 'Name', 'Product Image', 'Serial Number', 'Image Ref', 'Specifications / Dimensions', 'Material']
2025-08-03 16:01:21 | INFO     | gui.main_window:log_message:1236 | 選擇圖片欄位: 無
2025-08-03 16:01:21 | INFO     | gui.main_window:log_message:1236 | 選擇 SEO 欄位: 無
2025-08-03 16:01:21 | INFO     | gui.main_window:log_message:1236 | 選擇產品名稱欄位: 無
2025-08-03 16:01:21 | INFO     | gui.main_window:log_message:1236 | 選擇產品名稱欄位: Product Type
2025-08-03 16:01:21 | INFO     | gui.main_window:log_message:1236 | 自動設定處理範圍: 1 到 34 行
2025-08-03 16:01:21 | INFO     | gui.main_window:log_message:1236 | 資料載入成功: 34 行, 7 欄
2025-08-03 16:01:21 | INFO     | gui.main_window:log_message:1236 | 警告: 部分欄位有缺失值
2025-08-03 16:01:25 | INFO     | gui.main_window:log_message:1236 | 選擇圖片欄位: Product Image
2025-08-03 16:01:29 | INFO     | gui.main_window:log_message:1236 | 選擇產品名稱欄位: Name
2025-08-03 16:01:32 | INFO     | gui.main_window:log_message:1236 | 選擇圖片欄位: Image Ref
2025-08-03 16:01:39 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer AI 模型: GPT-4o mini | OpenAI | ✅圖片理解 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer
2025-08-03 16:01:40 | INFO     | gui.main_window:log_message:1236 | 選擇 Writer Prompt: furniture
2025-08-03 16:01:50 | INFO     | gui.main_window:log_message:1236 | ✅ 圖片處理器已啟用: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture
2025-08-03 16:01:50 | INFO     | core.data_processor:set_excluded_columns:86 | 設定排除欄位: []
2025-08-03 16:01:50 | INFO     | gui.main_window:log_message:1236 | 排除欄位: 無
2025-08-03 16:01:50 | INFO     | core.data_processor:set_image_column:96 | 設定圖片欄位: Image Ref
2025-08-03 16:01:50 | INFO     | gui.main_window:log_message:1236 | 開始處理 34 個項目...
2025-08-03 16:01:50 | INFO     | core.processing_engine:process_batch:389 | 開始批次處理: 行 0 到 33 (共 34 項)
2025-08-03 16:01:50 | INFO     | core.processing_engine:process_batch:390 | Writer AI: openai-gpt4o-mini, Reviewer AI: anthropic-sonnet4
2025-08-03 16:01:50 | INFO     | core.processing_engine:process_batch:393 | 處理項目 1/34 (行 0)
2025-08-03 16:01:50 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: Dining Table
2025-08-03 16:01:50 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['低敏', '多功能', '無添加', '豐富', '安全']
2025-08-03 16:01:50 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 0: enable_images=True, image_processor=True, filename='Screenshot 2025-08-03 140919'
2025-08-03 16:01:50 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Screenshot 2025-08-03 140919' 找到 1 張圖片
2025-08-03 16:01:50 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'Screenshot 2025-08-03 140919': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\Screenshot 2025-08-03 140919.png']
2025-08-03 16:01:50 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:01:50 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:01:50 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:01:50 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\Screenshot 2025-08-03 140919.png
2025-08-03 16:01:50 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: Screenshot 2025-08-03 140919.png
2025-08-03 16:02:02 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1512 input + 481 output = 1993 tokens, 成本: $0.000000
2025-08-03 16:02:02 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,1993,0.0
2025-08-03 16:02:02 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 1993, 時間: 11.30s
2025-08-03 16:02:10 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1273 input + 375 output = 1648 tokens, 成本: $0.000000
2025-08-03 16:02:10 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1648,0.0
2025-08-03 16:02:10 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1648, 時間: 7.99s
2025-08-03 16:02:10 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Dining Table (19.30s)
2025-08-03 16:02:10 | INFO     | core.processing_engine:process_batch:393 | 處理項目 2/34 (行 1)
2025-08-03 16:02:10 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['可持續', '低敏', '多功能', '無添加', '豐富']
2025-08-03 16:02:10 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 1: enable_images=True, image_processor=True, filename='0005_22-1_Auto-generated_with_background'
2025-08-03 16:02:10 | DEBUG    | core.data_processor:find_images:327 | 檔名 '0005_22-1_Auto-generated_with_background' 找到 1 張圖片
2025-08-03 16:02:10 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '0005_22-1_Auto-generated_with_background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\0005_22-1_Auto-generated_with_background.jpg']
2025-08-03 16:02:10 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:02:10 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:02:10 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:02:10 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\0005_22-1_Auto-generated_with_background.jpg
2025-08-03 16:02:10 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: 0005_22-1_Auto-generated_with_background.jpg
2025-08-03 16:02:40 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1869 input + 558 output = 2427 tokens, 成本: $0.000000
2025-08-03 16:02:40 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2427,0.0
2025-08-03 16:02:40 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2427, 時間: 30.01s
2025-08-03 16:02:47 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1374 input + 401 output = 1775 tokens, 成本: $0.000000
2025-08-03 16:02:47 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1775,0.0
2025-08-03 16:02:47 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1775, 時間: 7.72s
2025-08-03 16:02:47 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: ARCTON Extendable Dining Table  (37.74s)
2025-08-03 16:02:47 | INFO     | core.processing_engine:process_batch:393 | 處理項目 3/34 (行 2)
2025-08-03 16:02:47 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['低敏', '多功能', '無添加', '豐富', '安全']
2025-08-03 16:02:47 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 2: enable_images=True, image_processor=True, filename='0000_24-2_Auto-generatedwithbackground'
2025-08-03 16:02:47 | DEBUG    | core.data_processor:find_images:327 | 檔名 '0000_24-2_Auto-generatedwithbackground' 找到 1 張圖片
2025-08-03 16:02:47 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '0000_24-2_Auto-generatedwithbackground': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\0000_24-2_Auto-generatedwithbackground.jpg']
2025-08-03 16:02:47 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:02:47 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:02:47 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:02:47 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\0000_24-2_Auto-generatedwithbackground.jpg
2025-08-03 16:02:47 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: 0000_24-2_Auto-generatedwithbackground.jpg
2025-08-03 16:03:12 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1879 input + 560 output = 2439 tokens, 成本: $0.000000
2025-08-03 16:03:12 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2439,0.0
2025-08-03 16:03:12 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2439, 時間: 24.37s
2025-08-03 16:03:20 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1385 input + 400 output = 1785 tokens, 成本: $0.000000
2025-08-03 16:03:20 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1785,0.0
2025-08-03 16:03:20 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1785, 時間: 8.27s
2025-08-03 16:03:20 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: NOVA 140 Dining Table – White Sintered Stone & Car (32.64s)
2025-08-03 16:03:20 | INFO     | core.processing_engine:process_batch:393 | 處理項目 4/34 (行 3)
2025-08-03 16:03:20 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '低敏', '多功能', '無添加', '豐富']
2025-08-03 16:03:20 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 3: enable_images=True, image_processor=True, filename='0001_DSC02309-Copy_Auto-generated with background'
2025-08-03 16:03:20 | DEBUG    | core.data_processor:find_images:327 | 檔名 '0001_DSC02309-Copy_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:03:20 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '0001_DSC02309-Copy_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\0001_DSC02309-Copy_Auto-generated with background.jpg']
2025-08-03 16:03:20 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:03:20 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:03:20 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:03:20 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\0001_DSC02309-Copy_Auto-generated with background.jpg
2025-08-03 16:03:20 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: 0001_DSC02309-Copy_Auto-generated with background.jpg
2025-08-03 16:03:57 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1860 input + 778 output = 2638 tokens, 成本: $0.000000
2025-08-03 16:03:57 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2638,0.0
2025-08-03 16:03:57 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2638, 時間: 36.55s
2025-08-03 16:04:05 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1661 input + 404 output = 2065 tokens, 成本: $0.000000
2025-08-03 16:04:05 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,2065,0.0
2025-08-03 16:04:05 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 2065, 時間: 8.24s
2025-08-03 16:04:05 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Solace Dining Chair (44.80s)
2025-08-03 16:04:05 | INFO     | core.processing_engine:process_batch:393 | 處理項目 5/34 (行 4)
2025-08-03 16:04:05 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['低敏', '多功能', '無添加', '豐富', '安全']
2025-08-03 16:04:05 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 4: enable_images=True, image_processor=True, filename='Chairs_0023_9-2_Auto-generated with background'
2025-08-03 16:04:05 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Chairs_0023_9-2_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:04:05 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'Chairs_0023_9-2_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\Chairs_0023_9-2_Auto-generated with background.jpg']
2025-08-03 16:04:05 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:04:05 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:04:05 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:04:05 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\Chairs_0023_9-2_Auto-generated with background.jpg
2025-08-03 16:04:05 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: Chairs_0023_9-2_Auto-generated with background.jpg
2025-08-03 16:04:22 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1827 input + 398 output = 2225 tokens, 成本: $0.000000
2025-08-03 16:04:22 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2225,0.0
2025-08-03 16:04:22 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2225, 時間: 17.03s
2025-08-03 16:04:30 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1113 input + 427 output = 1540 tokens, 成本: $0.000000
2025-08-03 16:04:30 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1540,0.0
2025-08-03 16:04:30 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1540, 時間: 8.32s
2025-08-03 16:04:30 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Vista Dining Chair  (25.35s)
2025-08-03 16:04:30 | INFO     | core.processing_engine:process_batch:393 | 處理項目 6/34 (行 5)
2025-08-03 16:04:30 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['低敏', '多功能', '無添加', '豐富', '安全']
2025-08-03 16:04:30 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 5: enable_images=True, image_processor=True, filename='Chairs_0006_DSC02316-_Auto-generated with background'
2025-08-03 16:04:30 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Chairs_0006_DSC02316-_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:04:30 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'Chairs_0006_DSC02316-_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\Chairs_0006_DSC02316-_Auto-generated with background.jpg']
2025-08-03 16:04:30 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:04:30 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:04:30 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:04:30 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\Chairs_0006_DSC02316-_Auto-generated with background.jpg
2025-08-03 16:04:30 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: Chairs_0006_DSC02316-_Auto-generated with background.jpg
2025-08-03 16:04:54 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1842 input + 530 output = 2372 tokens, 成本: $0.000000
2025-08-03 16:04:54 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2372,0.0
2025-08-03 16:04:54 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2372, 時間: 23.60s
2025-08-03 16:05:02 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1321 input + 427 output = 1748 tokens, 成本: $0.000000
2025-08-03 16:05:02 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1748,0.0
2025-08-03 16:05:02 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1748, 時間: 8.03s
2025-08-03 16:05:02 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Arco Dining Chair (31.65s)
2025-08-03 16:05:02 | INFO     | core.processing_engine:process_batch:393 | 處理項目 7/34 (行 6)
2025-08-03 16:05:02 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['低敏', '多功能', '無添加', '豐富', '安全']
2025-08-03 16:05:02 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 6: enable_images=True, image_processor=True, filename='Chairs_0022_1-2_Auto-generated with background'
2025-08-03 16:05:02 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Chairs_0022_1-2_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:05:02 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'Chairs_0022_1-2_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\Chairs_0022_1-2_Auto-generated with background.jpg']
2025-08-03 16:05:02 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:05:02 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:05:02 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:05:02 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\Chairs_0022_1-2_Auto-generated with background.jpg
2025-08-03 16:05:02 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: Chairs_0022_1-2_Auto-generated with background.jpg
2025-08-03 16:05:21 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1823 input + 386 output = 2209 tokens, 成本: $0.000000
2025-08-03 16:05:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2209,0.0
2025-08-03 16:05:21 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2209, 時間: 19.38s
2025-08-03 16:05:30 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1106 input + 426 output = 1532 tokens, 成本: $0.000000
2025-08-03 16:05:30 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1532,0.0
2025-08-03 16:05:30 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1532, 時間: 8.38s
2025-08-03 16:05:30 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Alba Dining Chair (27.76s)
2025-08-03 16:05:30 | INFO     | core.processing_engine:process_batch:393 | 處理項目 8/34 (行 7)
2025-08-03 16:05:30 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['低敏', '多功能', '無添加', '豐富', '安全']
2025-08-03 16:05:30 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 7: enable_images=True, image_processor=True, filename='Chairs_0008_4-2_Auto-generated with background'
2025-08-03 16:05:30 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Chairs_0008_4-2_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:05:30 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'Chairs_0008_4-2_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\Chairs_0008_4-2_Auto-generated with background.jpg']
2025-08-03 16:05:30 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:05:30 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:05:30 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:05:30 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\Chairs_0008_4-2_Auto-generated with background.jpg
2025-08-03 16:05:30 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: Chairs_0008_4-2_Auto-generated with background.jpg
2025-08-03 16:05:51 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1835 input + 520 output = 2355 tokens, 成本: $0.000000
2025-08-03 16:05:51 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2355,0.0
2025-08-03 16:05:51 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2355, 時間: 21.65s
2025-08-03 16:05:59 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1304 input + 382 output = 1686 tokens, 成本: $0.000000
2025-08-03 16:05:59 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1686,0.0
2025-08-03 16:05:59 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1686, 時間: 8.10s
2025-08-03 16:05:59 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Hana Armchair  (29.77s)
2025-08-03 16:05:59 | INFO     | core.processing_engine:process_batch:393 | 處理項目 9/34 (行 8)
2025-08-03 16:05:59 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '低敏', '多功能', '無添加', '豐富']
2025-08-03 16:05:59 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 8: enable_images=True, image_processor=True, filename='Chairs_0033_7-2_Auto-generated with background'
2025-08-03 16:05:59 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Chairs_0033_7-2_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:05:59 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'Chairs_0033_7-2_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\Chairs_0033_7-2_Auto-generated with background.jpg']
2025-08-03 16:05:59 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:05:59 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:05:59 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:05:59 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\Chairs_0033_7-2_Auto-generated with background.jpg
2025-08-03 16:05:59 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: Chairs_0033_7-2_Auto-generated with background.jpg
2025-08-03 16:06:20 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1876 input + 592 output = 2468 tokens, 成本: $0.000000
2025-08-03 16:06:20 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2468,0.0
2025-08-03 16:06:20 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2468, 時間: 20.77s
2025-08-03 16:06:29 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1437 input + 454 output = 1891 tokens, 成本: $0.000000
2025-08-03 16:06:29 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1891,0.0
2025-08-03 16:06:29 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1891, 時間: 9.10s
2025-08-03 16:06:29 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Arta Armchair (29.89s)
2025-08-03 16:06:29 | INFO     | core.processing_engine:process_batch:393 | 處理項目 10/34 (行 9)
2025-08-03 16:06:29 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '低敏', '多功能', '無添加', '豐富']
2025-08-03 16:06:29 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 9: enable_images=True, image_processor=True, filename='_0016_DSC02329_Auto-generated with background'
2025-08-03 16:06:29 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0016_DSC02329_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:06:29 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0016_DSC02329_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0016_DSC02329_Auto-generated with background.jpg']
2025-08-03 16:06:29 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:06:29 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:06:29 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:06:29 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0016_DSC02329_Auto-generated with background.jpg
2025-08-03 16:06:29 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0016_DSC02329_Auto-generated with background.jpg
2025-08-03 16:06:42 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1860 input + 415 output = 2275 tokens, 成本: $0.000000
2025-08-03 16:06:42 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2275,0.0
2025-08-03 16:06:42 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2275, 時間: 13.23s
2025-08-03 16:06:51 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1179 input + 418 output = 1597 tokens, 成本: $0.000000
2025-08-03 16:06:51 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1597,0.0
2025-08-03 16:06:51 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1597, 時間: 8.36s
2025-08-03 16:06:51 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Savanna Armchair (21.60s)
2025-08-03 16:06:51 | INFO     | core.processing_engine:process_batch:393 | 處理項目 11/34 (行 10)
2025-08-03 16:06:51 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['低敏', '多功能', '無添加', '豐富', '安全']
2025-08-03 16:06:51 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 10: enable_images=True, image_processor=True, filename='Chairs_0003_13-2_Auto-generated with background'
2025-08-03 16:06:51 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Chairs_0003_13-2_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:06:51 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'Chairs_0003_13-2_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\Chairs_0003_13-2_Auto-generated with background.jpg']
2025-08-03 16:06:51 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:06:51 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:06:51 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:06:51 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\Chairs_0003_13-2_Auto-generated with background.jpg
2025-08-03 16:06:51 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: Chairs_0003_13-2_Auto-generated with background.jpg
2025-08-03 16:07:28 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1845 input + 540 output = 2385 tokens, 成本: $0.000000
2025-08-03 16:07:28 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2385,0.0
2025-08-03 16:07:28 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2385, 時間: 37.56s
2025-08-03 16:07:37 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1341 input + 442 output = 1783 tokens, 成本: $0.000000
2025-08-03 16:07:37 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1783,0.0
2025-08-03 16:07:37 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1783, 時間: 9.11s
2025-08-03 16:07:37 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Noir Curve Chair (46.68s)
2025-08-03 16:07:37 | INFO     | core.processing_engine:process_batch:393 | 處理項目 12/34 (行 11)
2025-08-03 16:07:37 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['低敏', '多功能', '無添加', '豐富', '安全']
2025-08-03 16:07:37 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 11: enable_images=True, image_processor=True, filename='Chairs_0003_5-2_Auto-generated with background'
2025-08-03 16:07:38 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Chairs_0003_5-2_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:07:38 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'Chairs_0003_5-2_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\Chairs_0003_5-2_Auto-generated with background.jpg']
2025-08-03 16:07:38 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:07:38 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:07:38 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:07:38 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\Chairs_0003_5-2_Auto-generated with background.jpg
2025-08-03 16:07:38 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: Chairs_0003_5-2_Auto-generated with background.jpg
2025-08-03 16:08:12 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1847 input + 545 output = 2392 tokens, 成本: $0.000000
2025-08-03 16:08:12 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2392,0.0
2025-08-03 16:08:12 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2392, 時間: 34.67s
2025-08-03 16:08:21 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1335 input + 448 output = 1783 tokens, 成本: $0.000000
2025-08-03 16:08:21 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1783,0.0
2025-08-03 16:08:21 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1783, 時間: 8.42s
2025-08-03 16:08:21 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Shella Armchair (43.12s)
2025-08-03 16:08:21 | INFO     | core.processing_engine:process_batch:393 | 處理項目 13/34 (行 12)
2025-08-03 16:08:21 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '低敏', '多功能', '無添加', '豐富']
2025-08-03 16:08:21 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 12: enable_images=True, image_processor=True, filename='Chairs_0038_6-2_Auto-generated with background'
2025-08-03 16:08:21 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Chairs_0038_6-2_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:08:21 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'Chairs_0038_6-2_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\Chairs_0038_6-2_Auto-generated with background.jpg']
2025-08-03 16:08:21 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:08:21 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:08:21 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:08:21 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\Chairs_0038_6-2_Auto-generated with background.jpg
2025-08-03 16:08:21 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: Chairs_0038_6-2_Auto-generated with background.jpg
2025-08-03 16:08:50 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1866 input + 837 output = 2703 tokens, 成本: $0.000000
2025-08-03 16:08:50 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2703,0.0
2025-08-03 16:08:50 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2703, 時間: 29.73s
2025-08-03 16:09:00 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1727 input + 434 output = 2161 tokens, 成本: $0.000000
2025-08-03 16:09:00 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,2161,0.0
2025-08-03 16:09:00 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 2161, 時間: 9.48s
2025-08-03 16:09:00 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Archer Armchair (39.22s)
2025-08-03 16:09:00 | INFO     | core.processing_engine:process_batch:393 | 處理項目 14/34 (行 13)
2025-08-03 16:09:00 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['低敏', '多功能', '無添加', '豐富', '安全']
2025-08-03 16:09:00 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 13: enable_images=True, image_processor=True, filename='Chairs_0018_10-2_Auto-generated with background'
2025-08-03 16:09:00 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Chairs_0018_10-2_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:09:00 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'Chairs_0018_10-2_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\Chairs_0018_10-2_Auto-generated with background.jpg']
2025-08-03 16:09:00 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:09:00 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:09:00 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:09:00 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\Chairs_0018_10-2_Auto-generated with background.jpg
2025-08-03 16:09:00 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: Chairs_0018_10-2_Auto-generated with background.jpg
2025-08-03 16:09:15 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1831 input + 300 output = 2131 tokens, 成本: $0.000000
2025-08-03 16:09:15 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2131,0.0
2025-08-03 16:09:15 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2131, 時間: 15.56s
2025-08-03 16:09:24 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1023 input + 453 output = 1476 tokens, 成本: $0.000000
2025-08-03 16:09:24 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1476,0.0
2025-08-03 16:09:24 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1476, 時間: 8.45s
2025-08-03 16:09:24 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Hiro Dining Chair (24.03s)
2025-08-03 16:09:24 | INFO     | core.processing_engine:process_batch:393 | 處理項目 15/34 (行 14)
2025-08-03 16:09:24 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 1)
2025-08-03 16:09:24 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['天然', '高品質', '進口', '低敏', '多功能']
2025-08-03 16:09:24 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 14: enable_images=True, image_processor=True, filename='_0011_18-3_Auto-generated with background'
2025-08-03 16:09:24 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0011_18-3_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:09:24 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0011_18-3_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0011_18-3_Auto-generated with background.jpg']
2025-08-03 16:09:24 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:09:24 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:09:24 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:09:24 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0011_18-3_Auto-generated with background.jpg
2025-08-03 16:09:24 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0011_18-3_Auto-generated with background.jpg
2025-08-03 16:09:51 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1896 input + 596 output = 2492 tokens, 成本: $0.000000
2025-08-03 16:09:51 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2492,0.0
2025-08-03 16:09:51 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2492, 時間: 26.70s
2025-08-03 16:09:59 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1461 input + 406 output = 1867 tokens, 成本: $0.000000
2025-08-03 16:09:59 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1867,0.0
2025-08-03 16:09:59 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1867, 時間: 8.68s
2025-08-03 16:09:59 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Valora Living Set     TV Stand  (35.39s)
2025-08-03 16:09:59 | INFO     | core.processing_engine:process_batch:393 | 處理項目 16/34 (行 15)
2025-08-03 16:09:59 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Immunity (評分: 1)
2025-08-03 16:09:59 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['天然', '高品質', '進口', '低敏', '多功能']
2025-08-03 16:09:59 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 15: enable_images=True, image_processor=True, filename='_0018_16-3_Auto-generated with background'
2025-08-03 16:09:59 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0018_16-3_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:09:59 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0018_16-3_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0018_16-3_Auto-generated with background.jpg']
2025-08-03 16:09:59 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:09:59 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:09:59 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:09:59 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0018_16-3_Auto-generated with background.jpg
2025-08-03 16:09:59 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0018_16-3_Auto-generated with background.jpg
2025-08-03 16:10:20 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1895 input + 594 output = 2489 tokens, 成本: $0.000000
2025-08-03 16:10:20 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2489,0.0
2025-08-03 16:10:20 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2489, 時間: 20.76s
2025-08-03 16:10:29 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1450 input + 386 output = 1836 tokens, 成本: $0.000000
2025-08-03 16:10:29 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1836,0.0
2025-08-03 16:10:29 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1836, 時間: 8.63s
2025-08-03 16:10:29 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Valora Living Set coffee table (29.42s)
2025-08-03 16:10:29 | INFO     | core.processing_engine:process_batch:393 | 處理項目 17/34 (行 16)
2025-08-03 16:10:29 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: 地柜
2025-08-03 16:10:29 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['無添加', '高品質', '低敏', '多功能', '豐富']
2025-08-03 16:10:29 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 16: enable_images=True, image_processor=True, filename='_0021_15-3_Auto-generated with background'
2025-08-03 16:10:29 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0021_15-3_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:10:29 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0021_15-3_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0021_15-3_Auto-generated with background.jpg']
2025-08-03 16:10:29 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:10:29 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:10:29 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:10:29 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0021_15-3_Auto-generated with background.jpg
2025-08-03 16:10:29 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0021_15-3_Auto-generated with background.jpg
2025-08-03 16:10:46 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1883 input + 590 output = 2473 tokens, 成本: $0.000000
2025-08-03 16:10:46 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2473,0.0
2025-08-03 16:10:46 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2473, 時間: 17.69s
2025-08-03 16:10:56 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1442 input + 424 output = 1866 tokens, 成本: $0.000000
2025-08-03 16:10:56 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1866,0.0
2025-08-03 16:10:56 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1866, 時間: 9.36s
2025-08-03 16:10:56 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: 地柜 (27.06s)
2025-08-03 16:10:56 | INFO     | core.processing_engine:process_batch:393 | 處理項目 18/34 (行 17)
2025-08-03 16:10:56 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: 茶几
2025-08-03 16:10:56 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['無添加', '高品質', '低敏', '多功能', '豐富']
2025-08-03 16:10:56 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 17: enable_images=True, image_processor=True, filename='_0008_19-3_Auto-generated with background'
2025-08-03 16:10:56 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0008_19-3_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:10:56 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0008_19-3_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0008_19-3_Auto-generated with background.jpg']
2025-08-03 16:10:56 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:10:56 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:10:56 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:10:56 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0008_19-3_Auto-generated with background.jpg
2025-08-03 16:10:56 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0008_19-3_Auto-generated with background.jpg
2025-08-03 16:11:26 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1885 input + 585 output = 2470 tokens, 成本: $0.000000
2025-08-03 16:11:26 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2470,0.0
2025-08-03 16:11:26 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2470, 時間: 30.24s
2025-08-03 16:11:34 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1427 input + 416 output = 1843 tokens, 成本: $0.000000
2025-08-03 16:11:34 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1843,0.0
2025-08-03 16:11:34 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1843, 時間: 8.31s
2025-08-03 16:11:34 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: 茶几 (38.56s)
2025-08-03 16:11:34 | INFO     | core.processing_engine:process_batch:393 | 處理項目 19/34 (行 18)
2025-08-03 16:11:34 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '低敏', '多功能', '無添加', '豐富']
2025-08-03 16:11:34 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 18: enable_images=True, image_processor=True, filename='_0011_18-3_Auto-generated with background'
2025-08-03 16:11:34 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0011_18-3_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:11:34 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0011_18-3_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0011_18-3_Auto-generated with background.jpg']
2025-08-03 16:11:34 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:11:34 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:11:34 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:11:34 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0011_18-3_Auto-generated with background.jpg
2025-08-03 16:11:34 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0011_18-3_Auto-generated with background.jpg
2025-08-03 16:11:59 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1915 input + 576 output = 2491 tokens, 成本: $0.000000
2025-08-03 16:11:59 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2491,0.0
2025-08-03 16:11:59 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2491, 時間: 25.15s
2025-08-03 16:12:08 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1458 input + 414 output = 1872 tokens, 成本: $0.000000
2025-08-03 16:12:08 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1872,0.0
2025-08-03 16:12:08 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1872, 時間: 8.30s
2025-08-03 16:12:08 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Zyra Living Set   -  Coffee Table (33.46s)
2025-08-03 16:12:08 | INFO     | core.processing_engine:process_batch:393 | 處理項目 20/34 (行 19)
2025-08-03 16:12:08 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['高品質', '低敏', '多功能', '無添加', '豐富']
2025-08-03 16:12:08 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 19: enable_images=True, image_processor=True, filename='_0024_14-2_Auto-generated with background'
2025-08-03 16:12:08 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0024_14-2_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:12:08 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0024_14-2_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0024_14-2_Auto-generated with background.jpg']
2025-08-03 16:12:08 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:12:08 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:12:08 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:12:08 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0024_14-2_Auto-generated with background.jpg
2025-08-03 16:12:08 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0024_14-2_Auto-generated with background.jpg
2025-08-03 16:12:23 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1916 input + 596 output = 2512 tokens, 成本: $0.000000
2025-08-03 16:12:23 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2512,0.0
2025-08-03 16:12:23 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2512, 時間: 15.35s
2025-08-03 16:12:31 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1495 input + 429 output = 1924 tokens, 成本: $0.000000
2025-08-03 16:12:31 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1924,0.0
2025-08-03 16:12:31 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1924, 時間: 8.32s
2025-08-03 16:12:31 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Zyra Living Set   -  Coffee Table (23.70s)
2025-08-03 16:12:31 | INFO     | core.processing_engine:process_batch:393 | 處理項目 21/34 (行 20)
2025-08-03 16:12:31 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['低敏', '多功能', '無添加', '豐富', '安全']
2025-08-03 16:12:31 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 20: enable_images=True, image_processor=True, filename='_0000_21-5_Auto-generated with background'
2025-08-03 16:12:31 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0000_21-5_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:12:31 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0000_21-5_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0000_21-5_Auto-generated with background.jpg']
2025-08-03 16:12:31 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:12:31 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:12:31 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:12:31 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0000_21-5_Auto-generated with background.jpg
2025-08-03 16:12:32 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0000_21-5_Auto-generated with background.jpg
2025-08-03 16:13:27 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1891 input + 663 output = 2554 tokens, 成本: $0.000000
2025-08-03 16:13:27 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2554,0.0
2025-08-03 16:13:27 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2554, 時間: 55.91s
2025-08-03 16:13:36 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1554 input + 440 output = 1994 tokens, 成本: $0.000000
2025-08-03 16:13:36 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1994,0.0
2025-08-03 16:13:36 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1994, 時間: 8.98s
2025-08-03 16:13:36 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Vero TV Stand (64.92s)
2025-08-03 16:13:36 | INFO     | core.processing_engine:process_batch:393 | 處理項目 22/34 (行 21)
2025-08-03 16:13:36 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['低敏', '多功能', '無添加', '豐富', '安全']
2025-08-03 16:13:36 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 21: enable_images=True, image_processor=True, filename='_0015_17-3_Auto-generated with background'
2025-08-03 16:13:36 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0015_17-3_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:13:36 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0015_17-3_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0015_17-3_Auto-generated with background.jpg']
2025-08-03 16:13:36 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:13:36 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:13:36 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:13:36 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0015_17-3_Auto-generated with background.jpg
2025-08-03 16:13:36 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0015_17-3_Auto-generated with background.jpg
2025-08-03 16:14:26 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1884 input + 860 output = 2744 tokens, 成本: $0.000000
2025-08-03 16:14:26 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2744,0.0
2025-08-03 16:14:26 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2744, 時間: 49.88s
2025-08-03 16:14:36 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1785 input + 443 output = 2228 tokens, 成本: $0.000000
2025-08-03 16:14:36 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,2228,0.0
2025-08-03 16:14:36 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 2228, 時間: 9.31s
2025-08-03 16:14:36 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Vero Coffee Table (59.20s)
2025-08-03 16:14:36 | INFO     | core.processing_engine:process_batch:393 | 處理項目 23/34 (行 22)
2025-08-03 16:14:36 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: Sofa
2025-08-03 16:14:36 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 1)
2025-08-03 16:14:36 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['尺寸', '全方位', '低敏', '多功能', '無添加']
2025-08-03 16:14:36 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 22: enable_images=True, image_processor=True, filename='Furniture13.12.20248656'
2025-08-03 16:14:36 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Furniture13.12.20248656' 找到 1 張圖片
2025-08-03 16:14:36 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'Furniture13.12.20248656': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\Furniture13.12.20248656.jpg']
2025-08-03 16:14:36 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:14:36 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:14:36 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:14:36 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\Furniture13.12.20248656.jpg
2025-08-03 16:14:36 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: Furniture13.12.20248656.jpg
2025-08-03 16:15:01 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 2194 input + 711 output = 2905 tokens, 成本: $0.000000
2025-08-03 16:15:01 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2905,0.0
2025-08-03 16:15:01 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2905, 時間: 25.06s
2025-08-03 16:15:10 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1570 input + 451 output = 2021 tokens, 成本: $0.000000
2025-08-03 16:15:10 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,2021,0.0
2025-08-03 16:15:10 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 2021, 時間: 8.88s
2025-08-03 16:15:10 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Sofa (33.95s)
2025-08-03 16:15:10 | INFO     | core.processing_engine:process_batch:393 | 處理項目 24/34 (行 23)
2025-08-03 16:15:10 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: Sofa
2025-08-03 16:15:10 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 1)
2025-08-03 16:15:10 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['尺寸', '全方位', '低敏', '多功能', '無添加']
2025-08-03 16:15:10 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 23: enable_images=True, image_processor=True, filename='_0009_36-1_Auto-generated with background'
2025-08-03 16:15:10 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0009_36-1_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:15:10 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0009_36-1_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0009_36-1_Auto-generated with background.jpg']
2025-08-03 16:15:10 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:15:10 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:15:10 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:15:10 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0009_36-1_Auto-generated with background.jpg
2025-08-03 16:15:10 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0009_36-1_Auto-generated with background.jpg
2025-08-03 16:15:41 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 2002 input + 788 output = 2790 tokens, 成本: $0.000000
2025-08-03 16:15:41 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2790,0.0
2025-08-03 16:15:41 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2790, 時間: 31.35s
2025-08-03 16:15:49 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1872 input + 397 output = 2269 tokens, 成本: $0.000000
2025-08-03 16:15:49 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,2269,0.0
2025-08-03 16:15:49 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 2269, 時間: 8.44s
2025-08-03 16:15:49 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Sofa (39.81s)
2025-08-03 16:15:49 | INFO     | core.processing_engine:process_batch:393 | 處理項目 25/34 (行 24)
2025-08-03 16:15:49 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: Sofa
2025-08-03 16:15:49 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['全方位', '低敏', '多功能', '無添加', '豐富']
2025-08-03 16:15:49 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 24: enable_images=True, image_processor=True, filename='93'
2025-08-03 16:15:49 | DEBUG    | core.data_processor:find_images:327 | 檔名 '93' 找到 1 張圖片
2025-08-03 16:15:49 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '93': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\93.jpg']
2025-08-03 16:15:49 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:15:49 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:15:49 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:15:49 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\93.jpg
2025-08-03 16:15:49 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: 93.jpg
2025-08-03 16:16:38 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1923 input + 1148 output = 3071 tokens, 成本: $0.000000
2025-08-03 16:16:38 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,3071,0.0
2025-08-03 16:16:38 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 3071, 時間: 48.99s
2025-08-03 16:16:48 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 2249 input + 417 output = 2666 tokens, 成本: $0.000000
2025-08-03 16:16:48 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,2666,0.0
2025-08-03 16:16:48 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 2666, 時間: 9.49s
2025-08-03 16:16:48 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Sofa (58.50s)
2025-08-03 16:16:48 | INFO     | core.processing_engine:process_batch:393 | 處理項目 26/34 (行 25)
2025-08-03 16:16:48 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: Sofa
2025-08-03 16:16:48 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 1)
2025-08-03 16:16:48 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['尺寸', '全方位', '低敏', '多功能', '無添加']
2025-08-03 16:16:48 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 25: enable_images=True, image_processor=True, filename='_0017_35-1_Auto-generated with background'
2025-08-03 16:16:48 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0017_35-1_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:16:48 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0017_35-1_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0017_35-1_Auto-generated with background.jpg']
2025-08-03 16:16:48 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:16:48 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:16:48 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:16:48 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0017_35-1_Auto-generated with background.jpg
2025-08-03 16:16:48 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0017_35-1_Auto-generated with background.jpg
2025-08-03 16:17:18 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1962 input + 774 output = 2736 tokens, 成本: $0.000000
2025-08-03 16:17:18 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2736,0.0
2025-08-03 16:17:18 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2736, 時間: 30.15s
2025-08-03 16:17:27 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1793 input + 403 output = 2196 tokens, 成本: $0.000000
2025-08-03 16:17:27 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,2196,0.0
2025-08-03 16:17:27 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 2196, 時間: 8.95s
2025-08-03 16:17:27 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Sofa (39.11s)
2025-08-03 16:17:27 | INFO     | core.processing_engine:process_batch:393 | 處理項目 27/34 (行 26)
2025-08-03 16:17:27 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: Sofa
2025-08-03 16:17:27 | DEBUG    | core.keyword_manager:_infer_category:166 | 推斷產品分類: Furniture (評分: 1)
2025-08-03 16:17:27 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['尺寸', '全方位', '低敏', '多功能', '無添加']
2025-08-03 16:17:27 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 26: enable_images=True, image_processor=True, filename='_0039_29-1_Auto-generated with background'
2025-08-03 16:17:27 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0039_29-1_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:17:27 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0039_29-1_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0039_29-1_Auto-generated with background.jpg']
2025-08-03 16:17:27 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:17:27 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:17:27 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:17:27 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0039_29-1_Auto-generated with background.jpg
2025-08-03 16:17:27 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0039_29-1_Auto-generated with background.jpg
2025-08-03 16:17:57 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1959 input + 783 output = 2742 tokens, 成本: $0.000000
2025-08-03 16:17:57 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2742,0.0
2025-08-03 16:17:57 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2742, 時間: 30.37s
2025-08-03 16:18:06 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1809 input + 429 output = 2238 tokens, 成本: $0.000000
2025-08-03 16:18:06 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,2238,0.0
2025-08-03 16:18:06 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 2238, 時間: 8.87s
2025-08-03 16:18:06 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Sofa (39.26s)
2025-08-03 16:18:06 | INFO     | core.processing_engine:process_batch:393 | 處理項目 28/34 (行 27)
2025-08-03 16:18:06 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: Sofa
2025-08-03 16:18:06 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['全方位', '低敏', '多功能', '無添加', '豐富']
2025-08-03 16:18:06 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 27: enable_images=True, image_processor=True, filename='Screenshot 2025-08-03 140919'
2025-08-03 16:18:06 | DEBUG    | core.data_processor:find_images:327 | 檔名 'Screenshot 2025-08-03 140919' 找到 1 張圖片
2025-08-03 16:18:06 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'Screenshot 2025-08-03 140919': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\Screenshot 2025-08-03 140919.png']
2025-08-03 16:18:06 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:18:06 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:18:06 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:18:06 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\Screenshot 2025-08-03 140919.png
2025-08-03 16:18:06 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: Screenshot 2025-08-03 140919.png
2025-08-03 16:18:43 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1599 input + 1142 output = 2741 tokens, 成本: $0.000000
2025-08-03 16:18:43 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2741,0.0
2025-08-03 16:18:43 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2741, 時間: 36.73s
2025-08-03 16:18:52 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 2256 input + 408 output = 2664 tokens, 成本: $0.000000
2025-08-03 16:18:52 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,2664,0.0
2025-08-03 16:18:52 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 2664, 時間: 8.54s
2025-08-03 16:18:52 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Sofa (45.28s)
2025-08-03 16:18:52 | INFO     | core.processing_engine:process_batch:393 | 處理項目 29/34 (行 28)
2025-08-03 16:18:52 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: Sofa
2025-08-03 16:18:52 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['全方位', '低敏', '多功能', '無添加', '豐富']
2025-08-03 16:18:52 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 28: enable_images=True, image_processor=True, filename='_0022_34-1_Auto-generated with background'
2025-08-03 16:18:52 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0022_34-1_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:18:52 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0022_34-1_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0022_34-1_Auto-generated with background.jpg']
2025-08-03 16:18:52 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:18:52 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:18:52 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:18:52 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0022_34-1_Auto-generated with background.jpg
2025-08-03 16:18:52 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0022_34-1_Auto-generated with background.jpg
2025-08-03 16:19:25 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1986 input + 783 output = 2769 tokens, 成本: $0.000000
2025-08-03 16:19:25 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2769,0.0
2025-08-03 16:19:25 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2769, 時間: 33.60s
2025-08-03 16:19:34 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1850 input + 430 output = 2280 tokens, 成本: $0.000000
2025-08-03 16:19:34 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,2280,0.0
2025-08-03 16:19:34 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 2280, 時間: 9.10s
2025-08-03 16:19:34 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Sofa (42.72s)
2025-08-03 16:19:34 | INFO     | core.processing_engine:process_batch:393 | 處理項目 30/34 (行 29)
2025-08-03 16:19:34 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: Sofa
2025-08-03 16:19:34 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['低敏', '多功能', '無添加', '豐富', '安全']
2025-08-03 16:19:34 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 29: enable_images=True, image_processor=True, filename='_0027_33-1_Auto-generated with background'
2025-08-03 16:19:34 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0027_33-1_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:19:34 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0027_33-1_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0027_33-1_Auto-generated with background.jpg']
2025-08-03 16:19:34 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:19:34 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:19:34 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:19:34 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0027_33-1_Auto-generated with background.jpg
2025-08-03 16:19:34 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0027_33-1_Auto-generated with background.jpg
2025-08-03 16:20:04 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1912 input + 832 output = 2744 tokens, 成本: $0.000000
2025-08-03 16:20:04 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2744,0.0
2025-08-03 16:20:04 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2744, 時間: 29.61s
2025-08-03 16:20:12 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1781 input + 395 output = 2176 tokens, 成本: $0.000000
2025-08-03 16:20:12 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,2176,0.0
2025-08-03 16:20:12 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 2176, 時間: 8.48s
2025-08-03 16:20:12 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: Sofa (38.11s)
2025-08-03 16:20:12 | INFO     | core.processing_engine:process_batch:393 | 處理項目 31/34 (行 30)
2025-08-03 16:20:12 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: JM09-1A
2025-08-03 16:20:12 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['安全', '高品質', '可持續', '全方位', '低敏']
2025-08-03 16:20:12 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 30: enable_images=True, image_processor=True, filename='_0008_27-2_Auto-generated with background'
2025-08-03 16:20:12 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0008_27-2_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:20:12 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0008_27-2_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0008_27-2_Auto-generated with background.jpg']
2025-08-03 16:20:12 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:20:12 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:20:12 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:20:12 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0008_27-2_Auto-generated with background.jpg
2025-08-03 16:20:12 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0008_27-2_Auto-generated with background.jpg
2025-08-03 16:20:36 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1906 input + 812 output = 2718 tokens, 成本: $0.000000
2025-08-03 16:20:36 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2718,0.0
2025-08-03 16:20:36 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2718, 時間: 24.01s
2025-08-03 16:20:45 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1733 input + 415 output = 2148 tokens, 成本: $0.000000
2025-08-03 16:20:45 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,2148,0.0
2025-08-03 16:20:45 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 2148, 時間: 8.19s
2025-08-03 16:20:45 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: JM09-1A (32.22s)
2025-08-03 16:20:45 | INFO     | core.processing_engine:process_batch:393 | 處理項目 32/34 (行 31)
2025-08-03 16:20:45 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: JM03
2025-08-03 16:20:45 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['多功能', '可持續', '持久', '無添加', '高品質']
2025-08-03 16:20:45 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 31: enable_images=True, image_processor=True, filename='_0004_28-1_Auto-generated with background'
2025-08-03 16:20:45 | DEBUG    | core.data_processor:find_images:327 | 檔名 '_0004_28-1_Auto-generated with background' 找到 1 張圖片
2025-08-03 16:20:45 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '_0004_28-1_Auto-generated with background': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\_0004_28-1_Auto-generated with background.jpg']
2025-08-03 16:20:45 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:20:45 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:20:45 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:20:45 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\_0004_28-1_Auto-generated with background.jpg
2025-08-03 16:20:45 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: _0004_28-1_Auto-generated with background.jpg
2025-08-03 16:21:11 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1952 input + 622 output = 2574 tokens, 成本: $0.000000
2025-08-03 16:21:11 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2574,0.0
2025-08-03 16:21:11 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2574, 時間: 26.77s
2025-08-03 16:21:20 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1578 input + 402 output = 1980 tokens, 成本: $0.000000
2025-08-03 16:21:20 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1980,0.0
2025-08-03 16:21:20 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1980, 時間: 8.36s
2025-08-03 16:21:20 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: JM03 (35.15s)
2025-08-03 16:21:20 | INFO     | core.processing_engine:process_batch:393 | 處理項目 33/34 (行 32)
2025-08-03 16:21:20 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: 903-2A
2025-08-03 16:21:20 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['多功能', '可持續', '低敏', '無添加', '豐富']
2025-08-03 16:21:20 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 32: enable_images=True, image_processor=True, filename='7'
2025-08-03 16:21:20 | DEBUG    | core.data_processor:find_images:327 | 檔名 '7' 找到 1 張圖片
2025-08-03 16:21:20 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for '7': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\7.jpg']
2025-08-03 16:21:20 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:21:20 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:21:20 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:21:20 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\7.jpg
2025-08-03 16:21:20 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: 7.jpg
2025-08-03 16:21:46 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1904 input + 588 output = 2492 tokens, 成本: $0.000000
2025-08-03 16:21:46 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2492,0.0
2025-08-03 16:21:46 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2492, 時間: 26.42s
2025-08-03 16:21:55 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1449 input + 436 output = 1885 tokens, 成本: $0.000000
2025-08-03 16:21:55 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1885,0.0
2025-08-03 16:21:55 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1885, 時間: 8.61s
2025-08-03 16:21:55 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: 903-2A (35.04s)
2025-08-03 16:21:55 | INFO     | core.processing_engine:process_batch:393 | 處理項目 34/34 (行 33)
2025-08-03 16:21:55 | INFO     | core.processing_engine:_generate_product_name:265 | AI 生成產品名稱: JM10-2A
2025-08-03 16:21:55 | DEBUG    | core.keyword_manager:get_keywords_for_product:135 | 為產品選擇關鍵字: ['全方位', '多功能', '安全', '可持續', '方便']
2025-08-03 16:21:55 | INFO     | core.processing_engine:process_single_item:116 | 🖼️ Image processing - Row 33: enable_images=True, image_processor=True, filename='office chair 2'
2025-08-03 16:21:55 | DEBUG    | core.data_processor:find_images:327 | 檔名 'office chair 2' 找到 1 張圖片
2025-08-03 16:21:55 | INFO     | core.processing_engine:process_single_item:119 | 🖼️ Found 1 images for 'office chair 2': ['C:\\Users\\<USER>\\OneDrive\\trizenith\\AI_Description_Editor\\New Product Picture\\office chair 2.png']
2025-08-03 16:21:55 | INFO     | core.processing_engine:_run_writer_stage:304 | 🤖 Calling AI model 'openai-gpt4o' with 1 images
2025-08-03 16:21:55 | INFO     | core.ai_models:generate_text:81 | 🤖 OpenAI gpt-4o - Received 1 images
2025-08-03 16:21:55 | INFO     | core.ai_models:generate_text:87 | 🖼️ Processing 1 images for OpenAI vision
2025-08-03 16:21:55 | INFO     | core.ai_models:generate_text:91 | 🖼️ Encoding image: C:\Users\<USER>\OneDrive\trizenith\AI_Description_Editor\New Product Picture\office chair 2.png
2025-08-03 16:21:55 | INFO     | core.ai_models:generate_text:100 | ✅ Image encoded successfully: office chair 2.png
2025-08-03 16:22:08 | INFO     | core.ai_models:generate_text:127 | OpenAI 使用記錄: 1890 input + 263 output = 2153 tokens, 成本: $0.000000
2025-08-03 16:22:08 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,openai-gpt4o:gpt-4o,2153,0.0
2025-08-03 16:22:08 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: openai-gpt4o, Token: 2153, 時間: 12.76s
2025-08-03 16:22:14 | INFO     | core.ai_models:generate_text:203 | Anthropic 使用記錄: 1032 input + 366 output = 1398 tokens, 成本: $0.000000
2025-08-03 16:22:14 | INFO     | utils.logger:log_api_usage:96 | API_USAGE,anthropic-sonnet4:claude-3-5-sonnet-20241022,1398,0.0
2025-08-03 16:22:14 | INFO     | core.ai_models:generate_text:426 | AI 生成完成 - 模型: anthropic-sonnet4, Token: 1398, 時間: 6.91s
2025-08-03 16:22:14 | INFO     | core.processing_engine:process_batch:408 | 項目處理成功: JM10-2A (19.68s)
2025-08-03 16:22:14 | INFO     | core.processing_engine:process_batch:412 | 批次處理完成: 成功 34, 失敗 0
2025-08-03 16:22:14 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: HTML Output
2025-08-03 16:22:14 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Used Keywords
2025-08-03 16:22:14 | INFO     | core.data_processor:add_output_column:210 | 新增欄位: Review Notes
2025-08-03 16:22:14 | INFO     | gui.main_window:log_message:1236 | 處理完成！成功: 34, 失敗: 0, 總 Token: 151,493, 預估費用: $0.000000
2025-08-03 16:22:15 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 16:22:15 | INFO     | gui.main_window:log_message:1236 | ✅ 載入了 34 個現有的 HTML 結果
2025-08-03 16:22:15 | INFO     | gui.main_window:log_message:1236 | 自動顯示 Dining Table 的預覽
2025-08-03 17:32:35 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: ARCTON Extendable Dining Table 
2025-08-03 17:32:43 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: NOVA 140 Dining Table – White Sintered Stone & Carbon Steel
2025-08-03 17:32:43 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Solace Dining Chair
2025-08-03 17:32:46 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Vista Dining Chair 
2025-08-03 17:34:43 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Arco Dining Chair
2025-08-03 17:35:08 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Vista Dining Chair 
2025-08-03 17:35:09 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Solace Dining Chair
2025-08-03 17:35:09 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: NOVA 140 Dining Table – White Sintered Stone & Carbon Steel
2025-08-03 17:35:10 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Solace Dining Chair
2025-08-03 17:36:06 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Vista Dining Chair 
2025-08-03 17:36:08 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Arco Dining Chair
2025-08-03 17:36:37 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Alba Dining Chair
2025-08-03 17:36:51 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Hana Armchair 
2025-08-03 17:37:04 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Arta Armchair
2025-08-03 17:37:33 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Savanna Armchair
2025-08-03 17:37:34 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Noir Curve Chair
2025-08-03 17:37:35 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Shella Armchair
2025-08-03 17:37:36 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Archer Armchair
2025-08-03 17:37:37 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Hiro Dining Chair
2025-08-03 17:37:37 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Valora Living Set     TV Stand 
2025-08-03 17:37:38 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Valora Living Set coffee table
2025-08-03 17:37:39 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:37:42 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:37:42 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Zyra Living Set   -  Coffee Table
2025-08-03 17:37:43 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Zyra Living Set   -  Coffee Table
2025-08-03 17:37:43 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Vero TV Stand
2025-08-03 17:37:45 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: Vero Coffee Table
2025-08-03 17:37:46 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:37:48 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:37:49 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:38:01 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:38:03 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:38:06 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:38:07 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:38:08 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:38:09 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:38:10 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:38:12 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
2025-08-03 17:38:13 | INFO     | gui.main_window:log_message:1236 | 使用現有名稱: nan
