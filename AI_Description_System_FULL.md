
# 🧠 AI 商品描述優化系統 – 完整說明文件 (Markdown)

## 🎯 系統目標

建立一套圖形化 AI 工具，結合 Claude 與 ChatGPT，協助產出符合 SEO、結構化、圖片輔助理解的 HTML 商品描述，專為藥妝/傢俱電商設計。支援多模型、多風格、多關鍵字策略。

---

## ✅ 系統功能模組總覽

### 1️⃣ Excel 檔案處理
- 支援 `.xlsx` 檔案
- 使用者可選取要處理的列範圍與描述欄位
- 自動識別並排除參考欄位（如 ID、SKU）

### 2️⃣ 多欄位內容整合
- 除排除欄外，所有欄位都會融合進描述
- AI 會自動理解欄位語意並自然編寫內容

### 3️⃣ 圖片資料讀取（可選開啟）
- 啟動時詢問是否使用圖片輔助描述
- 使用者可選擇 `Product Picture/` 資料夾
- 系統會根據圖片欄位對應檔名自動尋找圖檔
- 多張圖片支援（多角度）
- 圖片**僅作為 AI 參考，不會輸出進 HTML**

### 4️⃣ SEO 關鍵字嵌入
- 關鍵字來源：
  - `categories/[分類]/keywords.txt`
  - `categories/_general/keywords.txt`
- 若缺關鍵字檔，系統會自動 fallback 或跳過
- 僅嵌入少量、自然、語義通順的字詞

### 5️⃣ 固定 HTML 輸出結構

```html
<h1>產品名稱</h1>

<h2>Description</h2>
  <h3>Product Overview</h3>
  <h3>Main Benefits</h3>

<h2>Ingredients</h2>
  <h3>Active Ingredients</h3>
  <h3>Free From / Allergy Info</h3>

<h2>How to Use</h2>
  <h3>Dosage</h3>
  <h3>Usage Warnings</h3>

<h2>Additional Information</h2>
  <h3>Miscellaneous</h3>
```

---

## 👁 HTML 與圖片預覽 GUI（PyQt）
- 支援 Excel 資料選擇與匯入
- 支援圖片資料夾選擇（可預覽）
- 顯示單筆商品描述與圖像的 HTML 預覽畫面
- 提供 AI 模型選擇、Prompt Profile 選擇、起始/結束列控制

---

## ✍️ AI 編輯與審核

### Prompt Profile 設定
- 每筆資料會經過兩次處理：
  1. **Writer Prompt**：用於產出 HTML 描述（支援多風格）
  2. **Reviewer Prompt**：用於審查輸出（15 字內精簡評論）
- Prompt 可從 `prompts/writer/` 與 `prompts/reviewer/` 中動態選擇
- 可自訂與新增多組 `.txt` 檔案

---

## 🔁 Review-Only Redo 功能
- 可載入已完成的 Excel 檔
- 自動判斷需修正的行（依照 Reviewer 回饋 ⚠️ 🛠️ ❌）
- 執行第二次編輯，產出新欄位 `Review Notes (v2)`

---

## 📂 建議資料夾結構

```
project/
├─ main.py
├─ gui/
│  └─ app_ui.py
├─ prompts/
│  ├─ writer/
│  │   └─ default.txt
│  ├─ reviewer/
│  │   └─ standard.txt
│  └─ profiles/
├─ categories/
│  ├─ _general/
│  │   └─ keywords.txt
│  ├─ Immunity/
│  │   └─ keywords.txt
├─ Product Picture/
│  ├─ product1.jpg
│  ├─ product1_view2.jpg
├─ config/
│  └─ ai_keys.env
├─ logs/
│  └─ usage_stats.csv
```

---

## 📤 輸出結果欄位（範例）

| Title | Category | Description | HTML Output | Used Keywords | Review Notes | Review Notes (v2) |
|-------|----------|-------------|--------------|----------------|---------------|--------------------|
| Swisse C | Immunity | 原始描述 | `<h1>...` | Vitamin C, Immunity | ⚠️ Add dosage info | ✅ Passed |

---

## 🧠 AI 模型與彈性設計
- 支援 ChatGPT, Claude, Gemini, GPT-4o...
- 可替換為本地 LLM（如 LM Studio）架構可擴充
- 所有模組皆為可獨立執行單元（自動化、批次處理）

---

