#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main Window for AI Description Editor
AI 商品描述編輯器主視窗
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QComboBox, QSpinBox, QCheckBox, QTextEdit,
    QFileDialog, QMessageBox, QProgressBar, QTabWidget, QSplitter,
    QGroupBox, QListWidget, QListWidgetItem, QTableWidget, QTableWidgetItem,
    QHeaderView, QFrame, QScrollArea, QLineEdit, QDialog, QDialogButtonBox,
    QMenuBar, QAction, QApplication, QStyleFactory
)
from PyQt5.QtCore import Qt, Q<PERSON>hread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QColor, QIcon
try:
    from PyQt5.QtWebEngineWidgets import QWebEngineView
    HAS_WEBENGINE = True
except ImportError:
    QWebEngineView = None
    HAS_WEBENGINE = False

from loguru import logger

# 導入核心模組
from core.data_processor import ExcelProcessor, ImageProcessor
from core.ai_models import AIModelManager
from core.prompt_manager import PromptManager
from core.keyword_manager import KeywordManager
from core.html_generator import HTMLGenerator
from core.processing_engine import ProcessingEngine, ProcessingResult
from core.review_engine import ReviewEngine
from core.cost_calculator import CostCalculator
from core.settings_manager import SettingsManager
from core.seo_manager import SEOManager
from core.i18n import I18n


class ProcessingThread(QThread):
    """處理執行緒"""

    # 信號定義
    progress_updated = pyqtSignal(int, int)  # 當前進度, 總數
    item_processed = pyqtSignal(object)  # ProcessingResult
    processing_finished = pyqtSignal(list)  # List[ProcessingResult]
    error_occurred = pyqtSignal(str)  # 錯誤訊息

    def __init__(self, processing_engine: ProcessingEngine, config: Dict[str, Any]):
        super().__init__()
        self.processing_engine = processing_engine
        self.config = config
        self.is_cancelled = False

    def setup_processing(
        self,
        start_row: int,
        end_row: int,
        writer_ai: str,
        writer_prompt: str,
        reviewer_prompt: str,
        reviewer_ai: str = None,
        category: str = None,
        max_keywords: int = 5,
        enable_images: bool = False
    ):
        """設定處理參數"""
        self.start_row = start_row
        self.end_row = end_row
        self.writer_ai = writer_ai
        self.reviewer_ai = reviewer_ai or writer_ai  # 如果沒有指定，使用 writer_ai
        self.writer_prompt = writer_prompt
        self.reviewer_prompt = reviewer_prompt
        self.category = category
        self.max_keywords = max_keywords
        self.enable_images = enable_images

    def run(self):
        """執行處理"""
        try:
            def progress_callback(current, total):
                if not self.is_cancelled:
                    self.progress_updated.emit(current, total)

            results = self.processing_engine.process_batch(
                self.start_row,
                self.end_row,
                self.writer_ai,
                self.writer_prompt,
                self.reviewer_prompt,
                self.category,
                self.max_keywords,
                self.enable_images,
                progress_callback,
                reviewer_ai=self.reviewer_ai
            )

            if not self.is_cancelled:
                self.processing_finished.emit(results)

        except Exception as e:
            self.error_occurred.emit(str(e))

    def cancel(self):
        """取消處理"""
        self.is_cancelled = True


class MainWindow(QMainWindow):
    """主視窗類別"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.excel_processor = None
        self.image_processor = None
        self.ai_manager = None
        self.prompt_manager = None
        self.keyword_manager = None
        self.html_generator = None
        self.processing_engine = None
        self.review_engine = None
        self.processing_thread = None
        self.processing_results = []
        self.cost_calculator = CostCalculator()

        # 新增管理器
        self.settings_manager = SettingsManager()
        self.seo_manager = SEOManager()
        self.i18n = I18n(self.settings_manager.get_language())

        # 預覽相關
        self.current_preview_index = 0
        self.processed_rows = []  # 儲存已處理的行索引
        self.current_html_index = 0
        self.html_results = []  # 儲存 HTML 結果
        self.current_preview_language = "zh"  # 預設中文預覽

        self.init_components()
        self.init_ui()
        self.setup_connections()
        self.load_saved_settings()

        logger.info("主視窗初始化完成")
    
    def init_components(self):
        """初始化核心組件"""
        try:
            # 初始化各個管理器
            self.ai_manager = AIModelManager(self.config, self.cost_calculator)
            self.prompt_manager = PromptManager(self.config.get('paths.prompts_dir'))
            self.keyword_manager = KeywordManager(self.config.get('paths.categories_dir'))
            self.html_generator = HTMLGenerator(self.config.get('html_template'))
            
            # 圖片處理器稍後初始化（需要用戶選擇目錄）
            
            logger.info("核心組件初始化完成")
            
        except Exception as e:
            logger.error(f"核心組件初始化失敗: {e}")
            QMessageBox.critical(self, "錯誤", f"系統初始化失敗：{e}")
    
    def init_ui(self):
        """初始化使用者介面"""
        self.setWindowTitle(self.i18n.t("main_window_title"))

        # 設定合適的視窗大小，避免溢出
        self.setGeometry(100, 100, 1400, 900)  # 設定合理的預設大小

        # 從設定載入視窗幾何（如果存在且合理）
        try:
            geometry = self.settings_manager.get("window_geometry")
            if geometry and geometry.get("width", 0) <= 1920 and geometry.get("height", 0) <= 1080:
                self.setGeometry(geometry["x"], geometry["y"], geometry["width"], geometry["height"])
        except:
            pass  # 使用預設大小
        
        # 設定字體
        font = QFont(self.config.get('gui.font_family', 'Microsoft YaHei'))
        font.setPointSize(self.config.get('gui.font_size', 10))
        self.setFont(font)
        
        # 建立中央小工具
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主要佈局 - 使用 QSplitter 實現可調整大小的面板
        main_splitter = QSplitter(Qt.Horizontal)
        main_splitter.setChildrenCollapsible(False)  # 防止面板完全收縮

        # 創建三個面板
        left_panel = self.create_control_panel_widget()
        center_panel = self.create_preview_panel_widget()
        right_panel = self.create_right_panel_widget()

        # 添加到分割器
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(center_panel)
        main_splitter.addWidget(right_panel)

        # 設定初始比例 (左:中:右 = 2:3:1.5)
        main_splitter.setSizes([400, 600, 300])
        main_splitter.setStretchFactor(0, 0)  # 左側面板不拉伸
        main_splitter.setStretchFactor(1, 1)  # 中間面板可拉伸
        main_splitter.setStretchFactor(2, 0)  # 右側面板不拉伸

        # 設定最小寬度
        left_panel.setMinimumWidth(350)
        center_panel.setMinimumWidth(400)
        right_panel.setMinimumWidth(240)

        # 將分割器添加到主佈局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_splitter)
        
        # 建立狀態列
        self.create_status_bar()

        # 建立選單列
        self.create_menu_bar()



    def create_right_panel_widget(self):
        """建立右側按鈕和狀態面板"""
        right_widget = QWidget()
        right_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-left: 2px solid #dee2e6;
                padding: 10px;
            }
        """)

        right_layout = QVBoxLayout(right_widget)
        right_layout.setSpacing(10)
        right_layout.setContentsMargins(15, 15, 15, 15)

        # 標題
        title_label = QLabel("控制面板")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px 0;
                border-bottom: 2px solid #3498db;
                margin-bottom: 10px;
            }
        """)
        right_layout.addWidget(title_label)

        # 按鈕樣式 - 專業且易讀，確保所有狀態都清晰可見
        button_style = """
            QPushButton {
                font-weight: 600;
                padding: 14px 12px;
                margin: 4px 0;
                border-radius: 8px;
                border: 2px solid #34495e;
                min-height: 40px;
                font-size: 13px;
                font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
                text-align: center;
                color: #000000 !important;
                letter-spacing: 0.5px;
            }
            QPushButton:hover {
                border: 2px solid #2c3e50;
                box-shadow: 0 3px 10px rgba(0,0,0,0.2);
                transform: translateY(-1px);
                color: #000000 !important;
            }
            QPushButton:pressed {
                border: 2px solid #1a252f;
                box-shadow: 0 1px 3px rgba(0,0,0,0.3);
                transform: translateY(1px);
                color: #000000 !important;
            }
            QPushButton:disabled {
                background-color: #95a5a6 !important;
                color: #000000 !important;
                border: 2px solid #7f8c8d !important;
                box-shadow: none;
                opacity: 0.7;
            }
        """

        # 載入資料按鈕
        self.load_data_btn = QPushButton("📁 " + self.i18n.t("load_data"))
        self.load_data_btn.setStyleSheet(button_style + "background-color: #2980b9;")
        right_layout.addWidget(self.load_data_btn)

        # 開始處理按鈕
        self.start_processing_btn = QPushButton("🚀 " + self.i18n.t("start_processing"))
        self.start_processing_btn.setStyleSheet(button_style + "background-color: #27ae60;")
        self.start_processing_btn.setEnabled(False)
        right_layout.addWidget(self.start_processing_btn)

        # Review-Only 按鈕
        self.review_only_btn = QPushButton("🔍 " + self.i18n.t("review_only"))
        self.review_only_btn.setStyleSheet(button_style + "background-color: #e67e22;")
        self.review_only_btn.setEnabled(False)
        right_layout.addWidget(self.review_only_btn)

        # 基於評論重做按鈕 (新功能)
        self.redo_based_on_review_btn = QPushButton("🔄 基於評論重做")
        self.redo_based_on_review_btn.setStyleSheet(button_style + "background-color: #9b59b6;")
        self.redo_based_on_review_btn.setEnabled(False)
        right_layout.addWidget(self.redo_based_on_review_btn)

        # 匯出結果按鈕
        self.export_btn = QPushButton("💾 " + self.i18n.t("export_results"))
        self.export_btn.setStyleSheet(button_style + "background-color: #8e44ad;")
        self.export_btn.setEnabled(False)
        right_layout.addWidget(self.export_btn)

        # 重置結果按鈕
        self.reset_results_btn = QPushButton("🗑️ " + self.i18n.t("reset_results"))
        self.reset_results_btn.setStyleSheet(button_style + "background-color: #c0392b;")
        self.reset_results_btn.setEnabled(False)
        right_layout.addWidget(self.reset_results_btn)

        # 分隔線
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("QFrame { color: #bdc3c7; margin: 10px 0; }")
        right_layout.addWidget(separator)

        # 狀態標籤
        status_title = QLabel("狀態資訊")
        status_title.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px 0;
            }
        """)
        right_layout.addWidget(status_title)

        # 狀態文字區域 - 擴展到底部
        self.status_text = QTextEdit()
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px;
                font-size: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        self.status_text.setReadOnly(True)
        right_layout.addWidget(self.status_text)

        # 不添加彈性空間，讓狀態區域擴展到底部

        return right_widget
    
    def create_control_panel_widget(self):
        """建立左側控制面板"""
        control_widget = QWidget()
        control_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-right: 1px solid #dee2e6;
            }
        """)
        control_layout = QVBoxLayout(control_widget)
        
        # 檔案選擇區域
        file_group = QGroupBox(self.i18n.t("file_settings"))
        file_layout = QGridLayout(file_group)

        # Excel 檔案選擇
        file_layout.addWidget(QLabel(self.i18n.t("excel_file")), 0, 0)
        self.excel_file_label = QLabel(self.i18n.t("no_file_selected"))
        self.excel_file_label.setStyleSheet("QLabel { border: 1px solid gray; padding: 5px; }")
        file_layout.addWidget(self.excel_file_label, 0, 1)

        self.select_excel_btn = QPushButton(self.i18n.t("select_excel"))
        file_layout.addWidget(self.select_excel_btn, 0, 2)

        # 工作表選擇
        file_layout.addWidget(QLabel(self.i18n.t("worksheet")), 1, 0)
        self.sheet_combo = QComboBox()
        file_layout.addWidget(self.sheet_combo, 1, 1, 1, 2)

        # 圖片資料夾選擇
        file_layout.addWidget(QLabel(self.i18n.t("image_folder")), 2, 0)
        self.image_dir_label = QLabel(self.i18n.t("no_folder_selected"))
        self.image_dir_label.setStyleSheet("QLabel { border: 1px solid gray; padding: 5px; }")
        file_layout.addWidget(self.image_dir_label, 2, 1)

        self.select_image_dir_btn = QPushButton(self.i18n.t("select_folder"))
        file_layout.addWidget(self.select_image_dir_btn, 2, 2)

        # SEO 資料夾選擇
        file_layout.addWidget(QLabel(self.i18n.t("seo_folder")), 3, 0)
        self.seo_dir_label = QLabel(self.i18n.t("no_folder_selected"))
        self.seo_dir_label.setStyleSheet("QLabel { border: 1px solid gray; padding: 5px; }")
        file_layout.addWidget(self.seo_dir_label, 3, 1)

        self.select_seo_dir_btn = QPushButton(self.i18n.t("select_folder"))
        file_layout.addWidget(self.select_seo_dir_btn, 3, 2)

        self.enable_images_cb = QCheckBox(self.i18n.t("enable_images"))
        file_layout.addWidget(self.enable_images_cb, 4, 0, 1, 3)
        
        control_layout.addWidget(file_group)
        
        # 欄位設定區域
        column_group = QGroupBox(self.i18n.t("column_settings"))
        column_layout = QVBoxLayout(column_group)

        column_layout.addWidget(QLabel(self.i18n.t("excluded_columns")))
        self.excluded_columns_list = QListWidget()
        self.excluded_columns_list.setMaximumHeight(100)
        column_layout.addWidget(self.excluded_columns_list)

        column_layout.addWidget(QLabel(self.i18n.t("image_column")))
        self.image_column_combo = QComboBox()
        column_layout.addWidget(self.image_column_combo)

        column_layout.addWidget(QLabel(self.i18n.t("seo_column")))
        self.seo_column_combo = QComboBox()
        column_layout.addWidget(self.seo_column_combo)

        # 產品名稱欄位選擇
        column_layout.addWidget(QLabel(self.i18n.t("product_name_column")))
        self.product_name_column_combo = QComboBox()
        column_layout.addWidget(self.product_name_column_combo)
        
        control_layout.addWidget(column_group)
        
        # AI 設定區域
        ai_group = QGroupBox(self.i18n.t("ai_settings"))
        ai_layout = QGridLayout(ai_group)

        ai_layout.addWidget(QLabel(self.i18n.t("writer_ai_model")), 0, 0)
        self.writer_ai_combo = QComboBox()
        ai_layout.addWidget(self.writer_ai_combo, 0, 1)

        ai_layout.addWidget(QLabel(self.i18n.t("writer_prompt")), 1, 0)
        self.writer_prompt_combo = QComboBox()
        ai_layout.addWidget(self.writer_prompt_combo, 1, 1)

        ai_layout.addWidget(QLabel(self.i18n.t("reviewer_ai_model")), 2, 0)
        self.reviewer_ai_combo = QComboBox()
        ai_layout.addWidget(self.reviewer_ai_combo, 2, 1)

        ai_layout.addWidget(QLabel(self.i18n.t("reviewer_prompt")), 3, 0)
        self.reviewer_prompt_combo = QComboBox()
        ai_layout.addWidget(self.reviewer_prompt_combo, 3, 1)

        # SEO 預覽按鈕
        self.preview_seo_btn = QPushButton(self.i18n.t("preview_seo"))
        self.preview_seo_btn.setStyleSheet("QPushButton { background-color: #f39c12; color: #000000 !important; font-weight: bold; padding: 5px; }")
        ai_layout.addWidget(self.preview_seo_btn, 4, 0, 1, 2)
        
        control_layout.addWidget(ai_group)
        
        # 處理範圍設定
        range_group = QGroupBox(self.i18n.t("processing_range"))
        range_layout = QGridLayout(range_group)

        range_layout.addWidget(QLabel(self.i18n.t("start_row")), 0, 0)
        self.start_row_spin = QSpinBox()
        self.start_row_spin.setMinimum(1)
        self.start_row_spin.setMaximum(10000)
        self.start_row_spin.setValue(1)
        range_layout.addWidget(self.start_row_spin, 0, 1)

        range_layout.addWidget(QLabel(self.i18n.t("end_row")), 1, 0)
        self.end_row_spin = QSpinBox()
        self.end_row_spin.setMinimum(1)
        self.end_row_spin.setMaximum(10000)
        self.end_row_spin.setValue(10000)  # 預設為最大值
        range_layout.addWidget(self.end_row_spin, 1, 1)
        
        control_layout.addWidget(range_group)
        
        # 進度條（時尚顏色）
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #3498db;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                color: white;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:0.5 #2ecc71, stop:1 #f39c12);
                border-radius: 3px;
            }
        """)
        control_layout.addWidget(self.progress_bar)
        
        # 狀態資訊已移至右側面板
        return control_widget
    
    def create_preview_panel_widget(self):
        """建立中間預覽面板"""
        preview_widget = QWidget()
        preview_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border-left: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
            }
        """)
        preview_layout = QVBoxLayout(preview_widget)
        
        # 建立標籤頁
        self.tab_widget = QTabWidget()

        # HTML 預覽標籤頁 - 使用分割視窗
        html_preview_widget = QWidget()
        html_preview_layout = QVBoxLayout(html_preview_widget)

        # 預覽控制區域
        control_layout = QHBoxLayout()

        # 語言選擇
        control_layout.addWidget(QLabel(self.i18n.t("preview_language")))
        self.preview_language_combo = QComboBox()
        self.preview_language_combo.addItem(self.i18n.t("chinese_preview"), "zh")
        self.preview_language_combo.addItem(self.i18n.t("english_preview"), "en")
        self.preview_language_combo.currentTextChanged.connect(self.update_html_preview_language)
        control_layout.addWidget(self.preview_language_combo)

        control_layout.addStretch()

        # 導航按鈕
        self.html_prev_btn = QPushButton(self.i18n.t("previous"))
        self.html_prev_btn.setMaximumWidth(80)
        self.html_prev_btn.clicked.connect(self.show_previous_html)
        control_layout.addWidget(self.html_prev_btn)

        self.html_current_label = QLabel(self.i18n.t("current_item", 0, 0))
        self.html_current_label.setAlignment(Qt.AlignCenter)
        self.html_current_label.setMinimumWidth(120)
        control_layout.addWidget(self.html_current_label)

        self.html_next_btn = QPushButton(self.i18n.t("next"))
        self.html_next_btn.setMaximumWidth(80)
        self.html_next_btn.clicked.connect(self.show_next_html)
        control_layout.addWidget(self.html_next_btn)

        html_preview_layout.addLayout(control_layout)

        # 建立垂直分割器
        html_splitter = QSplitter(Qt.Vertical)

        # 上方：HTML 預覽
        if HAS_WEBENGINE:
            self.html_preview = QWebEngineView()
        else:
            self.html_preview = QTextEdit()
            self.html_preview.setReadOnly(True)
            self.html_preview.setAcceptRichText(True)

        html_splitter.addWidget(self.html_preview)

        # 下方：Reviewer 結果
        reviewer_group = QGroupBox("📝 Reviewer 評估結果")
        reviewer_layout = QVBoxLayout(reviewer_group)

        self.reviewer_result = QTextEdit()
        self.reviewer_result.setReadOnly(True)
        self.reviewer_result.setMaximumHeight(150)
        self.reviewer_result.setPlaceholderText("Reviewer 的評估結果將顯示在這裡...")
        reviewer_layout.addWidget(self.reviewer_result)

        html_splitter.addWidget(reviewer_group)
        html_splitter.setSizes([400, 150])  # 設定比例

        html_preview_layout.addWidget(html_splitter)
        self.tab_widget.addTab(html_preview_widget, "HTML 預覽")

        # HTML 原始碼標籤頁
        self.html_source = QTextEdit()
        self.html_source.setReadOnly(True)
        self.html_source.setFont(QFont("Consolas", 9))  # 使用等寬字體
        self.tab_widget.addTab(self.html_source, "HTML 原始碼")

        # 資料表格標籤頁
        self.data_table = QTableWidget()
        self.tab_widget.addTab(self.data_table, "資料表格")

        # 圖片預覽標籤頁
        self.image_preview_widget = self.create_image_preview_widget()
        self.tab_widget.addTab(self.image_preview_widget, "圖片預覽")

        # 成本計算標籤頁
        self.cost_calculator_widget = self.create_cost_calculator_widget()
        self.tab_widget.addTab(self.cost_calculator_widget, "成本計算")

        # Prompt 管理標籤頁
        self.prompt_manager_widget = self.create_prompt_manager_widget()
        self.tab_widget.addTab(self.prompt_manager_widget, "Prompt 管理")
        
        preview_layout.addWidget(self.tab_widget)
        return preview_widget
    
    def create_image_preview_widget(self):
        """建立圖片預覽小工具"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 圖片顯示區域
        scroll_area = QScrollArea()
        self.image_container = QWidget()
        self.image_layout = QVBoxLayout(self.image_container)
        
        scroll_area.setWidget(self.image_container)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        return widget

    def create_cost_calculator_widget(self):
        """建立成本計算小工具"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 標題
        title_label = QLabel("💰 AI 使用成本計算")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(title_label)

        # 成本統計表格
        self.cost_table = QTableWidget()
        self.cost_table.setColumnCount(6)
        self.cost_table.setHorizontalHeaderLabels([
            "AI 模型", "使用次數", "總 Token", "輸入 Token", "輸出 Token", "預估費用 (USD)"
        ])
        self.cost_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.cost_table)

        # 總計資訊
        summary_group = QGroupBox("📊 總計")
        summary_layout = QGridLayout(summary_group)

        self.total_requests_label = QLabel("總請求次數: 0")
        self.total_tokens_label = QLabel("總 Token 使用: 0")
        self.total_cost_label = QLabel("總預估費用: $0.00")

        summary_layout.addWidget(self.total_requests_label, 0, 0)
        summary_layout.addWidget(self.total_tokens_label, 0, 1)
        summary_layout.addWidget(self.total_cost_label, 0, 2)

        layout.addWidget(summary_group)

        # 按鈕區域
        button_layout = QHBoxLayout()

        # 重置按鈕
        reset_btn = QPushButton("🔄 重置統計")
        reset_btn.clicked.connect(self.reset_cost_statistics)
        button_layout.addWidget(reset_btn)

        # 刷新按鈕
        refresh_btn = QPushButton("🔄 刷新顯示")
        refresh_btn.clicked.connect(self.update_cost_statistics)
        button_layout.addWidget(refresh_btn)

        layout.addLayout(button_layout)

        # 初始化顯示
        self.update_cost_statistics()

        return widget

    def create_prompt_manager_widget(self):
        """建立 Prompt 管理小工具"""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        # 左側：Prompt 列表
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # Writer Prompts
        writer_group = QGroupBox("✍️ Writer Prompts")
        writer_layout = QVBoxLayout(writer_group)

        self.writer_prompt_list = QListWidget()
        writer_layout.addWidget(self.writer_prompt_list)

        writer_btn_layout = QHBoxLayout()
        self.new_writer_btn = QPushButton("新增")
        self.edit_writer_btn = QPushButton("編輯")
        self.delete_writer_btn = QPushButton("刪除")

        writer_btn_layout.addWidget(self.new_writer_btn)
        writer_btn_layout.addWidget(self.edit_writer_btn)
        writer_btn_layout.addWidget(self.delete_writer_btn)
        writer_layout.addLayout(writer_btn_layout)

        left_layout.addWidget(writer_group)

        # Reviewer Prompts
        reviewer_group = QGroupBox("🔍 Reviewer Prompts")
        reviewer_layout = QVBoxLayout(reviewer_group)

        self.reviewer_prompt_list = QListWidget()
        reviewer_layout.addWidget(self.reviewer_prompt_list)

        reviewer_btn_layout = QHBoxLayout()
        self.new_reviewer_btn = QPushButton("新增")
        self.edit_reviewer_btn = QPushButton("編輯")
        self.delete_reviewer_btn = QPushButton("刪除")

        reviewer_btn_layout.addWidget(self.new_reviewer_btn)
        reviewer_btn_layout.addWidget(self.edit_reviewer_btn)
        reviewer_btn_layout.addWidget(self.delete_reviewer_btn)
        reviewer_layout.addLayout(reviewer_btn_layout)

        left_layout.addWidget(reviewer_group)
        layout.addWidget(left_panel)

        # 右側：Prompt 編輯器
        right_panel = QGroupBox("📝 Prompt 編輯器")
        right_layout = QVBoxLayout(right_panel)

        # 名稱輸入
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("名稱:"))
        self.prompt_name_edit = QLineEdit()
        name_layout.addWidget(self.prompt_name_edit)
        right_layout.addLayout(name_layout)

        # 內容編輯
        self.prompt_content_edit = QTextEdit()
        self.prompt_content_edit.setPlaceholderText("在此輸入 Prompt 內容...")
        right_layout.addWidget(self.prompt_content_edit)

        # 儲存按鈕
        save_btn = QPushButton("💾 儲存 Prompt")
        save_btn.clicked.connect(self.save_prompt)
        right_layout.addWidget(save_btn)

        layout.addWidget(right_panel)

        # 設定比例
        layout.setStretch(0, 1)
        layout.setStretch(1, 2)

        return widget

    def create_status_bar(self):
        """建立狀態列"""
        self.statusBar().showMessage("就緒")

    def create_menu_bar(self):
        """建立選單列"""
        menubar = self.menuBar()

        # 檔案選單
        file_menu = menubar.addMenu(self.i18n.t("file_menu"))

        # 設定選單
        settings_menu = menubar.addMenu(self.i18n.t("settings_menu"))

        # 語言選單
        language_menu = settings_menu.addMenu(self.i18n.t("language_menu"))

        # 中文選項
        chinese_action = QAction("繁體中文", self)
        chinese_action.triggered.connect(lambda: self.change_language("zh_TW"))
        language_menu.addAction(chinese_action)

        # 英文選項
        english_action = QAction("English", self)
        english_action.triggered.connect(lambda: self.change_language("en_US"))
        language_menu.addAction(english_action)

        settings_menu.addSeparator()

        # API 金鑰設定
        api_keys_action = QAction(self.i18n.t("api_keys_menu"), self)
        api_keys_action.triggered.connect(self.show_api_keys_dialog)
        settings_menu.addAction(api_keys_action)

        # 說明選單
        help_menu = menubar.addMenu(self.i18n.t("help_menu"))

    def setup_connections(self):
        """設定信號連接"""
        # 檔案選擇按鈕
        self.select_excel_btn.clicked.connect(self.select_excel_file)
        self.select_image_dir_btn.clicked.connect(self.select_image_directory)
        self.select_seo_dir_btn.clicked.connect(self.select_seo_directory)

        # 控制按鈕
        self.load_data_btn.clicked.connect(self.load_data)
        self.start_processing_btn.clicked.connect(self.start_processing)
        self.review_only_btn.clicked.connect(self.start_review_only)
        self.redo_based_on_review_btn.clicked.connect(self.redo_based_on_review)
        self.export_btn.clicked.connect(self.export_results)
        self.reset_results_btn.clicked.connect(self.reset_results)
        self.preview_seo_btn.clicked.connect(self.preview_seo_files)

        # 下拉選單變更
        self.sheet_combo.currentTextChanged.connect(self.on_sheet_changed)
        self.writer_ai_combo.currentTextChanged.connect(self.on_writer_ai_changed)
        self.reviewer_ai_combo.currentTextChanged.connect(self.on_reviewer_ai_changed)
        self.writer_prompt_combo.currentTextChanged.connect(self.on_writer_prompt_changed)
        self.reviewer_prompt_combo.currentTextChanged.connect(self.on_reviewer_prompt_changed)

        # 欄位選擇變更
        self.image_column_combo.currentTextChanged.connect(self.on_image_column_changed)
        self.seo_column_combo.currentTextChanged.connect(self.on_seo_column_changed)
        self.product_name_column_combo.currentTextChanged.connect(self.on_product_name_column_changed)

        # Prompt 管理按鈕
        self.new_writer_btn.clicked.connect(self.new_writer_prompt)
        self.edit_writer_btn.clicked.connect(self.edit_writer_prompt)
        self.delete_writer_btn.clicked.connect(self.delete_writer_prompt)
        self.new_reviewer_btn.clicked.connect(self.new_reviewer_prompt)
        self.edit_reviewer_btn.clicked.connect(self.edit_reviewer_prompt)
        self.delete_reviewer_btn.clicked.connect(self.delete_reviewer_prompt)

        # Prompt 列表選擇
        self.writer_prompt_list.currentItemChanged.connect(self.on_writer_prompt_selected)
        self.reviewer_prompt_list.currentItemChanged.connect(self.on_reviewer_prompt_selected)

        # 載入可用選項
        self.load_available_options()

    def load_available_options(self):
        """載入可用選項到下拉選單"""
        # 載入 AI 模型（詳細模型資訊和推薦）
        if self.ai_manager:
            models = self.ai_manager.get_available_models()

            # 詳細模型資訊表 (更新版本)
            model_info = {
                'openai-gpt4o': {
                    'display': 'GPT-4o | OpenAI | ✅最佳圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer',
                    'short': 'GPT-4o (最佳圖片理解)',
                    'roles': ['Editor', 'Reviewer']
                },
                'openai-gpt4o-mini': {
                    'display': 'GPT-4o mini | OpenAI | ❌僅文本 | 💰最低成本 | 🌟🌟🌟～🌟🌟🌟🌟精緻度 | Editor/Reviewer',
                    'short': 'GPT-4o mini (最經濟)',
                    'roles': ['Editor', 'Reviewer']
                },
                'anthropic-opus4': {
                    'display': 'Claude Opus 4 | Anthropic | ✅精準圖片 | 💰💰💰高成本 | 🌟🌟🌟🌟🌟精緻度 | Editor（高品質需求）',
                    'short': 'Claude Opus 4 (最高品質)',
                    'roles': ['Editor']
                },
                'anthropic-sonnet4': {
                    'display': 'Claude Sonnet 4 | Anthropic | ✅圖片理解 | 💰💰中成本 | 🌟🌟🌟🌟精緻度 | Editor+Reviewer',
                    'short': 'Claude Sonnet 4 (平衡)',
                    'roles': ['Editor', 'Reviewer']
                },
                'google-pro25': {
                    'display': 'Gemini 2.5 Pro | Google | ✅強大圖片 | 💰💰中成本 | 🌟🌟🌟🌟🌟精緻度 | Editor+Reviewer',
                    'short': 'Gemini 2.5 Pro (免費額度)',
                    'roles': ['Editor', 'Reviewer']
                },
                'google-flash25': {
                    'display': 'Gemini 2.5 Flash | Google | ✅圖片理解 | 💰經濟 | 🌟🌟🌟精緻度 | Reviewer快速審查',
                    'short': 'Gemini 2.5 Flash (快速)',
                    'roles': ['Reviewer']
                }
            }

            # 為 Writer 添加適合的模型
            writer_models = []
            reviewer_models = []

            for model_key, info in model_info.items():
                if any(model in model_key for model in models):
                    if 'Editor' in info['roles']:
                        writer_models.append((info['display'], model_key))
                    if 'Reviewer' in info['roles']:
                        reviewer_models.append((info['display'], model_key))

            # 添加到下拉選單
            for display, model_key in writer_models:
                self.writer_ai_combo.addItem(display, model_key)

            # 添加 "無需審查" 選項
            self.reviewer_ai_combo.addItem("🚫 無需審查", "no_review")

            for display, model_key in reviewer_models:
                self.reviewer_ai_combo.addItem(display, model_key)

            # 設定預設選擇（推薦平衡的模型）
            if writer_models:
                # 優先選擇 Claude Sonnet 4 或 Gemini 2.5 Pro
                default_writer = next((m for m in writer_models if 'Sonnet 4' in m[0] or '2.5 Pro' in m[0]), writer_models[0])
                self.writer_ai_combo.setCurrentText(default_writer[0])

            if reviewer_models:
                default_reviewer = next((m for m in reviewer_models if 'Sonnet 4' in m[0] or '2.5 Pro' in m[0]), reviewer_models[0])
                self.reviewer_ai_combo.setCurrentText(default_reviewer[0])

        # 載入 Prompt
        if self.prompt_manager:
            self.refresh_prompt_lists()

    def select_excel_file(self):
        """選擇 Excel 檔案"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "選擇 Excel 檔案", "", "Excel Files (*.xlsx *.xls)"
        )

        if file_path:
            self.excel_file_label.setText(os.path.basename(file_path))
            self.excel_file_path = file_path

            # 載入工作表名稱
            if not self.excel_processor:
                self.excel_processor = ExcelProcessor()

            sheet_names = self.excel_processor.get_sheet_names(file_path)
            self.sheet_combo.clear()
            self.sheet_combo.addItems(sheet_names)

            self.log_message(f"已選擇 Excel 檔案: {os.path.basename(file_path)}")

    def select_image_directory(self):
        """選擇圖片目錄"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "選擇圖片資料夾", self.config.get('paths.pictures_dir', '')
        )

        if dir_path:
            self.image_dir_label.setText(os.path.basename(dir_path))
            self.image_dir_path = dir_path

            # 初始化圖片處理器
            self.image_processor = ImageProcessor(dir_path)

            # 驗證圖片目錄
            validation = self.image_processor.validate_images_directory()
            if validation['valid']:
                self.log_message(f"已選擇圖片資料夾: {os.path.basename(dir_path)} ({validation['total_images']} 張圖片)")
            else:
                self.log_message(f"圖片資料夾警告: {', '.join(validation['warnings'])}")

    def on_sheet_changed(self):
        """工作表變更事件"""
        if hasattr(self, 'excel_file_path') and self.sheet_combo.currentText():
            self.log_message(f"選擇工作表: {self.sheet_combo.currentText()}")
            # 保存工作表選擇
            self.settings_manager.set("last_worksheet", self.sheet_combo.currentText())

    def on_writer_ai_changed(self):
        """Writer AI 模型變更事件"""
        model_text = self.writer_ai_combo.currentText()
        model_data = self.writer_ai_combo.currentData()
        if model_text:
            self.log_message(f"選擇 Writer AI 模型: {model_text}")
            # 保存 AI 模型選擇
            self.settings_manager.set("writer_ai_display", model_text)
            if model_data:
                self.settings_manager.set("writer_ai_model", model_data)

    def on_reviewer_ai_changed(self):
        """Reviewer AI 模型變更事件"""
        model_text = self.reviewer_ai_combo.currentText()
        model_data = self.reviewer_ai_combo.currentData()
        if model_text:
            self.log_message(f"選擇 Reviewer AI 模型: {model_text}")
            # 保存 AI 模型選擇
            self.settings_manager.set("reviewer_ai_display", model_text)
            if model_data:
                self.settings_manager.set("reviewer_ai_model", model_data)

    def on_writer_prompt_changed(self):
        """Writer Prompt 變更事件"""
        prompt_text = self.writer_prompt_combo.currentText()
        if prompt_text:
            self.log_message(f"選擇 Writer Prompt: {prompt_text}")
            self.settings_manager.set("writer_prompt", prompt_text)

    def on_reviewer_prompt_changed(self):
        """Reviewer Prompt 變更事件"""
        prompt_text = self.reviewer_prompt_combo.currentText()
        if prompt_text:
            self.log_message(f"選擇 Reviewer Prompt: {prompt_text}")
            self.settings_manager.set("reviewer_prompt", prompt_text)

    def on_image_column_changed(self):
        """圖片欄位變更事件"""
        column_text = self.image_column_combo.currentText()
        if column_text:
            self.log_message(f"選擇圖片欄位: {column_text}")
            self.settings_manager.set("image_column", column_text)

    def on_seo_column_changed(self):
        """SEO 欄位變更事件"""
        column_text = self.seo_column_combo.currentText()
        if column_text:
            self.log_message(f"選擇 SEO 欄位: {column_text}")
            self.settings_manager.set("seo_column", column_text)

    def on_product_name_column_changed(self):
        """產品名稱欄位變更事件"""
        column_text = self.product_name_column_combo.currentText()
        if column_text:
            self.log_message(f"選擇產品名稱欄位: {column_text}")
            self.settings_manager.set("product_name_column", column_text)

    def load_data(self):
        """載入資料"""
        if not hasattr(self, 'excel_file_path'):
            QMessageBox.warning(self, "警告", "請先選擇 Excel 檔案")
            return

        if not self.excel_processor:
            self.excel_processor = ExcelProcessor()

        # 載入 Excel 資料
        sheet_name = self.sheet_combo.currentText() if self.sheet_combo.currentText() else None
        success = self.excel_processor.load_excel(self.excel_file_path, sheet_name)

        if not success:
            QMessageBox.critical(self, "錯誤", "載入 Excel 檔案失敗")
            return

        # 驗證資料
        validation = self.excel_processor.validate_data()
        if not validation['valid']:
            QMessageBox.critical(self, "錯誤", f"資料驗證失敗: {validation.get('message', '未知錯誤')}")
            return

        # 更新欄位選項
        self.update_column_options()

        # 更新資料表格
        self.update_data_table()

        # 自動設定處理範圍為全部資料
        total_rows = validation['total_rows']
        self.end_row_spin.setValue(total_rows)
        self.log_message(f"自動設定處理範圍: 1 到 {total_rows} 行")

        # 啟用處理按鈕
        self.start_processing_btn.setEnabled(True)

        self.log_message(f"資料載入成功: {validation['total_rows']} 行, {validation['total_columns']} 欄")

        if validation['warnings']:
            for warning in validation['warnings']:
                self.log_message(f"警告: {warning}")

    def update_column_options(self):
        """更新欄位選項"""
        if not self.excel_processor:
            return

        columns = self.excel_processor.get_columns()

        # 更新排除欄位列表 - 使用可勾選的項目
        self.excluded_columns_list.clear()
        for column in columns:
            item = QListWidgetItem(column)
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            item.setCheckState(Qt.Unchecked)
            # 預設排除常見的 ID 欄位
            if any(keyword in column.lower() for keyword in ['id', 'sku', 'code']):
                item.setCheckState(Qt.Checked)
            self.excluded_columns_list.addItem(item)

        # 更新圖片欄位下拉選單
        self.image_column_combo.clear()
        self.image_column_combo.addItem("無")
        self.image_column_combo.addItems(columns)

        # 更新 SEO 欄位下拉選單
        self.update_seo_column_options()

    def update_data_table(self):
        """更新資料表格顯示"""
        if not self.excel_processor or self.excel_processor.data is None:
            return

        data = self.excel_processor.data

        # 設定表格大小
        self.data_table.setRowCount(min(len(data), 100))  # 最多顯示 100 行
        self.data_table.setColumnCount(len(data.columns))

        # 設定標題
        self.data_table.setHorizontalHeaderLabels(list(data.columns))

        # 填入資料
        for row in range(min(len(data), 100)):
            for col in range(len(data.columns)):
                value = str(data.iloc[row, col]) if not data.iloc[row, col] is None else ""
                item = QTableWidgetItem(value)
                self.data_table.setItem(row, col, item)

        # 調整欄寬
        self.data_table.horizontalHeader().setStretchLastSection(True)
        self.data_table.resizeColumnsToContents()

        # 連接點擊事件
        self.data_table.cellClicked.connect(self.on_table_cell_clicked)

    def on_table_cell_clicked(self, row: int, column: int):
        """表格單元格點擊事件"""
        if not self.excel_processor or self.excel_processor.data is None:
            return

        # 檢查是否有 HTML Output 欄位
        html_content = ""
        reviewer_notes = ""

        if "HTML Output" in self.excel_processor.data.columns:
            html_col_index = self.excel_processor.data.columns.get_loc("HTML Output")
            if row < len(self.excel_processor.data):
                html_content = str(self.excel_processor.data.iloc[row, html_col_index] or "")

        # 檢查是否有 Review Notes 欄位
        if "Review Notes" in self.excel_processor.data.columns:
            review_col_index = self.excel_processor.data.columns.get_loc("Review Notes")
            if row < len(self.excel_processor.data):
                reviewer_notes = str(self.excel_processor.data.iloc[row, review_col_index] or "")

        if html_content.strip():
            self.show_html_preview(html_content, reviewer_notes)
            self.log_message(f"顯示第 {row + 1} 行的 HTML 預覽和 Reviewer 結果")
        else:
            self.log_message(f"第 {row + 1} 行沒有 HTML 內容，請先執行處理")

    def show_html_preview(self, html_content: str, reviewer_notes: str = ""):
        """顯示 HTML 預覽和 Reviewer 結果"""
        try:
            if HAS_WEBENGINE and hasattr(self.html_preview, 'setHtml'):
                # WebEngine 模式 - 顯示完整渲染的 HTML
                if self.html_generator:
                    full_html = self.html_generator.generate_preview_html(html_content)
                    self.html_preview.setHtml(full_html)
                else:
                    # 如果沒有 HTML 生成器，使用簡單的 HTML
                    simple_html = f"""
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <style>
                            body {{ font-family: Arial, sans-serif; padding: 20px; }}
                            h1 {{ color: #333; }}
                            h2 {{ color: #666; }}
                            h3 {{ color: #999; }}
                        </style>
                    </head>
                    <body>{html_content}</body>
                    </html>
                    """
                    self.html_preview.setHtml(simple_html)
            else:
                # QTextEdit 模式 - 使用 setHtml 來渲染 HTML 而不是顯示代碼
                if self.html_generator:
                    # 生成簡化的 HTML（去掉 DOCTYPE 和 html 標籤，只保留 body 內容和樣式）
                    styled_content = f"""
                    <style>
                        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; }}
                        h1 {{ color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
                        h2 {{ color: #34495e; border-left: 4px solid #3498db; padding-left: 15px; background-color: #f8f9fa; padding-top: 10px; padding-bottom: 10px; }}
                        h3 {{ color: #5a6c7d; border-bottom: 1px solid #ecf0f1; padding-bottom: 5px; }}
                        p {{ margin-bottom: 15px; }}
                        ul, ol {{ margin-bottom: 15px; }}
                        li {{ margin-bottom: 5px; }}
                    </style>
                    <div style="padding: 20px; background-color: white;">
                        <div style="background-color: #3498db; color: white; padding: 15px; margin: -20px -20px 20px -20px; text-align: center; font-weight: bold;">
                            📋 產品描述預覽
                        </div>
                        {html_content}
                    </div>
                    """
                    self.html_preview.setHtml(styled_content)
                else:
                    # 簡單的 HTML 渲染
                    self.html_preview.setHtml(html_content)

            # 更新 Reviewer 結果
            if reviewer_notes:
                self.reviewer_result.setPlainText(reviewer_notes)
            else:
                self.reviewer_result.setPlainText("尚未進行 Reviewer 評估")

            # 同時更新 HTML 原始碼標籤頁
            self.html_source.setPlainText(html_content)

            # 切換到 HTML 預覽標籤頁
            self.tab_widget.setCurrentIndex(0)

        except Exception as e:
            self.log_message(f"HTML 預覽顯示失敗: {e}")
            # 降級到 HTML 渲染（而不是純文字）
            try:
                self.html_preview.setHtml(html_content)
                self.html_source.setPlainText(html_content)
                self.reviewer_result.setPlainText(reviewer_notes or "顯示錯誤")
            except:
                self.html_preview.setPlainText(html_content)
                self.html_source.setPlainText(html_content)
                self.reviewer_result.setPlainText(reviewer_notes or "顯示錯誤")

    def log_message(self, message: str):
        """記錄訊息到狀態文字區域"""
        self.status_text.append(f"[{self.get_current_time()}] {message}")
        logger.info(message)

    def get_current_time(self):
        """取得當前時間字串"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")

    def start_processing(self):
        """開始處理"""
        # 驗證設定
        if not self._validate_processing_settings():
            return

        # 建立處理引擎
        if not self._create_processing_engine():
            return

        # 取得處理參數
        start_row = self.start_row_spin.value() - 1  # 轉換為 0-based 索引
        end_row = self.end_row_spin.value() - 1

        # 獲取 AI 模型鍵值，如果 currentData() 為空則從顯示名稱映射
        writer_ai_data = self.writer_ai_combo.currentData()
        reviewer_ai_data = self.reviewer_ai_combo.currentData()

        if writer_ai_data:
            writer_ai = writer_ai_data
        else:
            # 從顯示名稱映射到模型鍵值
            writer_ai_text = self.writer_ai_combo.currentText()
            writer_ai = self._map_display_name_to_key(writer_ai_text)

        if reviewer_ai_data:
            reviewer_ai = reviewer_ai_data
        else:
            # 從顯示名稱映射到模型鍵值
            reviewer_ai_text = self.reviewer_ai_combo.currentText()
            reviewer_ai = self._map_display_name_to_key(reviewer_ai_text)

        writer_prompt = self.writer_prompt_combo.currentText()
        reviewer_prompt = self.reviewer_prompt_combo.currentText()
        enable_images = self.enable_images_cb.isChecked()

        # 取得排除欄位
        excluded_columns = []
        for i in range(self.excluded_columns_list.count()):
            item = self.excluded_columns_list.item(i)
            if item.checkState() == Qt.Checked:
                excluded_columns.append(item.text())

        self.excel_processor.set_excluded_columns(excluded_columns)
        self.log_message(f"排除欄位: {', '.join(excluded_columns) if excluded_columns else '無'}")

        # 設定圖片欄位
        image_column = self.image_column_combo.currentText()
        if image_column != "無":
            self.excel_processor.set_image_column(image_column)

        # 建立並啟動處理執行緒
        self.processing_thread = ProcessingThread(self.processing_engine, self.config)
        self.processing_thread.setup_processing(
            start_row, end_row, writer_ai, writer_prompt, reviewer_prompt,
            reviewer_ai=reviewer_ai, enable_images=enable_images
        )

        # 連接信號
        self.processing_thread.progress_updated.connect(self.on_progress_updated)
        self.processing_thread.item_processed.connect(self.on_item_processed)
        self.processing_thread.processing_finished.connect(self.on_processing_finished)
        self.processing_thread.error_occurred.connect(self.on_processing_error)

        # 更新 UI 狀態
        self.start_processing_btn.setEnabled(False)
        self.start_processing_btn.setText("處理中...")
        self.progress_bar.setMaximum(end_row - start_row + 1)
        self.progress_bar.setValue(0)

        # 啟動處理
        self.processing_thread.start()
        self.log_message(f"開始處理 {end_row - start_row + 1} 個項目...")

    def _validate_processing_settings(self) -> bool:
        """驗證處理設定"""
        if not self.excel_processor or self.excel_processor.data is None:
            QMessageBox.warning(self, "警告", "請先載入 Excel 資料")
            return False

        if not self.writer_ai_combo.currentText():
            QMessageBox.warning(self, "警告", "請選擇 Writer AI 模型")
            return False

        if not self.reviewer_ai_combo.currentText():
            QMessageBox.warning(self, "警告", "請選擇 Reviewer AI 模型或選擇無需審查")
            return False

        if not self.writer_prompt_combo.currentText():
            QMessageBox.warning(self, "警告", "請選擇 Writer Prompt")
            return False

        if not self.reviewer_prompt_combo.currentText():
            QMessageBox.warning(self, "警告", "請選擇 Reviewer Prompt")
            return False

        start_row = self.start_row_spin.value()
        end_row = self.end_row_spin.value()
        max_rows = len(self.excel_processor.data)

        if start_row > end_row:
            QMessageBox.warning(self, "警告", "起始列不能大於結束列")
            return False

        if end_row > max_rows:
            QMessageBox.warning(self, "警告", f"結束列不能超過資料總行數 ({max_rows})")
            return False

        return True

    def _create_processing_engine(self) -> bool:
        """建立處理引擎"""
        try:
            self.processing_engine = ProcessingEngine(
                excel_processor=self.excel_processor,
                ai_manager=self.ai_manager,
                prompt_manager=self.prompt_manager,
                keyword_manager=self.keyword_manager,
                html_generator=self.html_generator,
                image_processor=self.image_processor if self.enable_images_cb.isChecked() else None
            )
            return True
        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"建立處理引擎失敗: {e}")
            return False

    def on_progress_updated(self, current: int, total: int):
        """處理進度更新"""
        self.progress_bar.setValue(current)
        self.statusBar().showMessage(f"處理進度: {current}/{total}")

    def on_item_processed(self, result: ProcessingResult):
        """單項處理完成"""
        if result.success:
            self.log_message(f"✅ {result.product_name} 處理完成 ({result.processing_time:.2f}s)")
            # 標記已處理的行為綠色
            self.mark_processed_row(result.row_index)

            # 添加到 HTML 結果列表
            html_result = {
                'html_content': result.html_output,
                'html_content_en': result.html_output,  # 暫時使用相同內容，可以後續擴展
                'reviewer_notes': result.review_notes,
                'product_name': result.product_name,
                'row_index': result.row_index
            }
            self.add_html_result(html_result)

            # 檢查產品名稱狀態
            if hasattr(result, 'existing_product_name'):
                self.log_product_name_status(result.existing_product_name, result.product_name)
        else:
            self.log_message(f"❌ 行 {result.row_index + 1} 處理失敗: {result.error_message}")

    def on_processing_finished(self, results: List[ProcessingResult]):
        """處理完成"""
        self.processing_results = results

        # 更新 Excel 資料
        self._update_excel_with_results(results)

        # 更新 UI 狀態
        self.start_processing_btn.setEnabled(True)
        self.start_processing_btn.setText(self.i18n.t("start_processing"))
        self.export_btn.setEnabled(True)
        self.review_only_btn.setEnabled(True)
        self.reset_results_btn.setEnabled(True)

        # 更新成本統計
        self.update_cost_statistics()

        # 顯示統計
        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful
        total_tokens = sum(r.tokens_used for r in results)

        # 顯示成本資訊
        summary = self.cost_calculator.get_usage_summary()
        total_cost = summary['total_cost']

        self.log_message(f"處理完成！成功: {successful}, 失敗: {failed}, 總 Token: {total_tokens:,}, 預估費用: ${total_cost:.6f}")
        self.statusBar().showMessage(f"處理完成 - 費用: ${total_cost:.6f}")

        # 更新資料表格
        self.update_data_table()

        # 自動顯示第一個成功的結果
        if results:
            first_success = next((r for r in results if r.success and r.html_output), None)
            if first_success:
                self.show_html_preview(first_success.html_output, first_success.review_notes)
                self.log_message(f"自動顯示 {first_success.product_name} 的預覽")

    def on_processing_error(self, error_message: str):
        """處理錯誤"""
        self.log_message(f"處理錯誤: {error_message}")
        QMessageBox.critical(self, "處理錯誤", error_message)

        # 恢復 UI 狀態
        self.start_processing_btn.setEnabled(True)
        self.start_processing_btn.setText("開始處理")

    def _update_excel_with_results(self, results: List[ProcessingResult]):
        """將處理結果更新到 Excel 資料"""
        if not self.excel_processor:
            return

        # 準備新欄位資料
        html_outputs = [""] * len(self.excel_processor.data)
        used_keywords = [""] * len(self.excel_processor.data)
        review_notes = [""] * len(self.excel_processor.data)

        for result in results:
            if result.row_index < len(html_outputs):
                html_outputs[result.row_index] = result.html_output
                used_keywords[result.row_index] = ", ".join(result.used_keywords)
                review_notes[result.row_index] = result.review_notes

        # 新增欄位到 Excel 資料
        self.excel_processor.add_output_column("HTML Output", html_outputs)
        self.excel_processor.add_output_column("Used Keywords", used_keywords)
        self.excel_processor.add_output_column("Review Notes", review_notes)

    def start_review_only(self):
        """開始 Review-Only 模式"""
        if not self.excel_processor or self.excel_processor.data is None:
            QMessageBox.warning(self, "警告", "請先載入 Excel 資料")
            return

        # 檢查是否有審查欄位
        if "Review Notes" not in self.excel_processor.data.columns:
            QMessageBox.warning(self, "警告", "找不到 'Review Notes' 欄位，請先進行完整處理")
            return

        # 建立審查引擎
        if not self.review_engine:
            self.review_engine = ReviewEngine(
                self.excel_processor,
                self.ai_manager,
                self.prompt_manager,
                self.html_generator
            )

        # 識別需要重新處理的項目
        review_items = self.review_engine.identify_review_items()

        if not review_items:
            QMessageBox.information(self, "資訊", "沒有找到需要重新處理的項目")
            return

        # 確認是否繼續
        reply = QMessageBox.question(
            self, "確認",
            f"找到 {len(review_items)} 個需要重新處理的項目，是否繼續？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 驗證 AI 設定
        if not self.ai_model_combo.currentText() or not self.writer_prompt_combo.currentText() or not self.reviewer_prompt_combo.currentText():
            QMessageBox.warning(self, "警告", "請選擇 AI 模型和 Prompt")
            return

        # 開始重新處理
        self._start_review_processing(review_items)

    def _start_review_processing(self, review_items: List[int]):
        """開始審查處理"""
        ai_model = self.ai_model_combo.currentText()
        writer_prompt = self.writer_prompt_combo.currentText()
        reviewer_prompt = self.reviewer_prompt_combo.currentText()

        # 更新 UI 狀態
        self.review_only_btn.setEnabled(False)
        self.review_only_btn.setText("重新處理中...")
        self.progress_bar.setMaximum(len(review_items))
        self.progress_bar.setValue(0)

        self.log_message(f"開始重新處理 {len(review_items)} 個項目...")

        # 執行重新處理（這裡簡化為同步處理，實際可以改為異步）
        results = []
        for i, row_index in enumerate(review_items):
            self.log_message(f"重新處理項目 {i + 1}/{len(review_items)} (行 {row_index + 1})")

            result = self.review_engine.re_process_item(
                row_index, ai_model, writer_prompt, reviewer_prompt
            )

            results.append(result)
            self.progress_bar.setValue(i + 1)

            if result.success:
                self.log_message(f"✅ {result.product_name} 重新處理完成")
            else:
                self.log_message(f"❌ 行 {row_index + 1} 重新處理失敗: {result.error_message}")

        # 更新 Excel 資料
        self.review_engine.update_excel_with_review_results(results)

        # 恢復 UI 狀態
        self.review_only_btn.setEnabled(True)
        self.review_only_btn.setText("Review-Only 模式")

        # 顯示結果
        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful

        self.log_message(f"重新處理完成！成功: {successful}, 失敗: {failed}")

        # 更新資料表格
        self.update_data_table()

        QMessageBox.information(self, "完成", f"重新處理完成！\n成功: {successful}\n失敗: {failed}")

    def redo_based_on_review(self):
        """基於評論重做 - 考慮評論結果重新生成內容"""
        if not self.excel_processor or self.excel_processor.data is None:
            QMessageBox.warning(self, "警告", "請先載入 Excel 資料")
            return

        # 檢查是否有評論欄位
        if "Review Notes" not in self.excel_processor.data.columns:
            QMessageBox.warning(self, "警告", "找不到 'Review Notes' 欄位，請先進行完整處理或審查")
            return

        # 檢查是否有評論內容
        review_data = self.excel_processor.data["Review Notes"].dropna()
        if review_data.empty:
            QMessageBox.warning(self, "警告", "沒有找到評論內容，請先進行審查")
            return

        # 驗證設定
        if not self.validate_settings():
            return

        # 確認是否繼續
        reply = QMessageBox.question(
            self, "確認",
            f"將基於現有評論重新生成內容，這將覆蓋現有的描述。\n找到 {len(review_data)} 個有評論的項目，是否繼續？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 獲取設定
        start_row = self.start_row_spin.value() - 1
        end_row = self.end_row_spin.value() - 1

        writer_ai_data = self.writer_ai_combo.currentData()
        reviewer_ai_data = self.reviewer_ai_combo.currentData()

        writer_ai = writer_ai_data if writer_ai_data else self._map_display_name_to_key(self.writer_ai_combo.currentText())
        reviewer_ai = reviewer_ai_data if reviewer_ai_data else self._map_display_name_to_key(self.reviewer_ai_combo.currentText())

        writer_prompt = self.writer_prompt_combo.currentText()
        reviewer_prompt = self.reviewer_prompt_combo.currentText()
        enable_images = self.enable_images_cb.isChecked()

        # 開始處理
        self.log_message("🔄 開始基於評論重做...")

        # 更新按鈕狀態
        self.redo_based_on_review_btn.setEnabled(False)
        self.redo_based_on_review_btn.setText("重做中...")

        # 創建處理引擎
        if not hasattr(self, 'processing_engine') or not self.processing_engine:
            from core.processing_engine import ProcessingEngine
            self.processing_engine = ProcessingEngine(
                self.excel_processor,
                self.ai_manager,
                self.prompt_manager,
                self.html_generator,
                self.image_processor if hasattr(self, 'image_processor') else None
            )

        # 執行基於評論的重做
        try:
            self.processing_engine.start_redo_with_review(
                start_row, end_row, writer_ai, reviewer_ai,
                writer_prompt, reviewer_prompt, enable_images
            )

            # 連接信號
            self.processing_engine.progress_updated.connect(self.progress_bar.setValue)
            self.processing_engine.status_updated.connect(self.log_message)
            self.processing_engine.finished.connect(self.on_redo_finished)

        except Exception as e:
            self.log_message(f"❌ 啟動重做失敗: {str(e)}")
            self.redo_based_on_review_btn.setEnabled(True)
            self.redo_based_on_review_btn.setText("🔄 基於評論重做")

    def on_redo_finished(self, results):
        """重做完成回調"""
        self.redo_based_on_review_btn.setEnabled(True)
        self.redo_based_on_review_btn.setText("🔄 基於評論重做")

        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful

        self.log_message(f"✅ 基於評論重做完成！成功: {successful}, 失敗: {failed}")

        # 更新資料表格
        self.update_data_table()

        # 啟用其他按鈕
        self.export_btn.setEnabled(True)
        self.reset_results_btn.setEnabled(True)

    def export_results(self):
        """匯出結果"""
        if not self.excel_processor:
            QMessageBox.warning(self, "警告", "沒有資料可匯出")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "匯出結果", "", "Excel Files (*.xlsx)"
        )

        if file_path:
            success = self.excel_processor.save_excel(file_path)
            if success:
                self.log_message(f"結果已匯出到: {file_path}")
                QMessageBox.information(self, "成功", "結果匯出成功！")
            else:
                QMessageBox.critical(self, "錯誤", "匯出失敗")

    def refresh_prompt_lists(self):
        """刷新 Prompt 列表"""
        if not self.prompt_manager:
            return

        # 刷新 Writer Prompts
        writer_prompts = self.prompt_manager.get_writer_prompts()
        self.writer_prompt_combo.clear()
        self.writer_prompt_combo.addItems(writer_prompts)

        self.writer_prompt_list.clear()
        self.writer_prompt_list.addItems(writer_prompts)

        # 刷新 Reviewer Prompts
        reviewer_prompts = self.prompt_manager.get_reviewer_prompts()
        self.reviewer_prompt_combo.clear()
        self.reviewer_prompt_combo.addItems(reviewer_prompts)

        self.reviewer_prompt_list.clear()
        self.reviewer_prompt_list.addItems(reviewer_prompts)

    def new_writer_prompt(self):
        """新增 Writer Prompt"""
        self.prompt_name_edit.clear()
        self.prompt_content_edit.clear()
        self.prompt_name_edit.setPlaceholderText("輸入新的 Writer Prompt 名稱")
        self.prompt_content_edit.setPlaceholderText("輸入 Writer Prompt 內容...")
        self.current_prompt_type = "writer"
        self.current_prompt_name = None

    def new_reviewer_prompt(self):
        """新增 Reviewer Prompt"""
        self.prompt_name_edit.clear()
        self.prompt_content_edit.clear()
        self.prompt_name_edit.setPlaceholderText("輸入新的 Reviewer Prompt 名稱")
        self.prompt_content_edit.setPlaceholderText("輸入 Reviewer Prompt 內容...")
        self.current_prompt_type = "reviewer"
        self.current_prompt_name = None

    def edit_writer_prompt(self):
        """編輯 Writer Prompt"""
        current_item = self.writer_prompt_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "請先選擇要編輯的 Writer Prompt")
            return

        prompt_name = current_item.text()
        prompt_content = self.prompt_manager.get_writer_prompt(prompt_name)

        self.prompt_name_edit.setText(prompt_name)
        self.prompt_content_edit.setPlainText(prompt_content or "")
        self.current_prompt_type = "writer"
        self.current_prompt_name = prompt_name

    def edit_reviewer_prompt(self):
        """編輯 Reviewer Prompt"""
        current_item = self.reviewer_prompt_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "請先選擇要編輯的 Reviewer Prompt")
            return

        prompt_name = current_item.text()
        prompt_content = self.prompt_manager.get_reviewer_prompt(prompt_name)

        self.prompt_name_edit.setText(prompt_name)
        self.prompt_content_edit.setPlainText(prompt_content or "")
        self.current_prompt_type = "reviewer"
        self.current_prompt_name = prompt_name

    def delete_writer_prompt(self):
        """刪除 Writer Prompt"""
        current_item = self.writer_prompt_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "請先選擇要刪除的 Writer Prompt")
            return

        prompt_name = current_item.text()
        reply = QMessageBox.question(
            self, "確認刪除",
            f"確定要刪除 Writer Prompt '{prompt_name}' 嗎？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 刪除檔案
            prompt_file = self.prompt_manager.prompts_dir / "writer" / f"{prompt_name}.txt"
            if prompt_file.exists():
                prompt_file.unlink()
                self.prompt_manager.reload_prompts()
                self.refresh_prompt_lists()
                self.log_message(f"已刪除 Writer Prompt: {prompt_name}")

    def delete_reviewer_prompt(self):
        """刪除 Reviewer Prompt"""
        current_item = self.reviewer_prompt_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "請先選擇要刪除的 Reviewer Prompt")
            return

        prompt_name = current_item.text()
        reply = QMessageBox.question(
            self, "確認刪除",
            f"確定要刪除 Reviewer Prompt '{prompt_name}' 嗎？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 刪除檔案
            prompt_file = self.prompt_manager.prompts_dir / "reviewer" / f"{prompt_name}.txt"
            if prompt_file.exists():
                prompt_file.unlink()
                self.prompt_manager.reload_prompts()
                self.refresh_prompt_lists()
                self.log_message(f"已刪除 Reviewer Prompt: {prompt_name}")

    def save_prompt(self):
        """儲存 Prompt"""
        name = self.prompt_name_edit.text().strip()
        content = self.prompt_content_edit.toPlainText().strip()

        if not name:
            QMessageBox.warning(self, "警告", "請輸入 Prompt 名稱")
            return

        if not content:
            QMessageBox.warning(self, "警告", "請輸入 Prompt 內容")
            return

        if not hasattr(self, 'current_prompt_type'):
            QMessageBox.warning(self, "警告", "請先選擇要新增或編輯的 Prompt 類型")
            return

        try:
            if self.current_prompt_type == "writer":
                success = self.prompt_manager.add_writer_prompt(name, content)
            else:
                success = self.prompt_manager.add_reviewer_prompt(name, content)

            if success:
                self.refresh_prompt_lists()
                self.log_message(f"已儲存 {self.current_prompt_type.title()} Prompt: {name}")
                QMessageBox.information(self, "成功", f"Prompt '{name}' 儲存成功！")
            else:
                QMessageBox.critical(self, "錯誤", "Prompt 儲存失敗")

        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"儲存失敗: {e}")

    def on_writer_prompt_selected(self, current, previous):
        """Writer Prompt 選擇事件"""
        if current:
            prompt_name = current.text()
            prompt_content = self.prompt_manager.get_writer_prompt(prompt_name)
            self.prompt_name_edit.setText(prompt_name)
            self.prompt_content_edit.setPlainText(prompt_content or "")
            self.current_prompt_type = "writer"
            self.current_prompt_name = prompt_name

    def on_reviewer_prompt_selected(self, current, previous):
        """Reviewer Prompt 選擇事件"""
        if current:
            prompt_name = current.text()
            prompt_content = self.prompt_manager.get_reviewer_prompt(prompt_name)
            self.prompt_name_edit.setText(prompt_name)
            self.prompt_content_edit.setPlainText(prompt_content or "")
            self.current_prompt_type = "reviewer"
            self.current_prompt_name = prompt_name

    def update_cost_statistics(self):
        """更新成本統計顯示"""
        breakdown = self.cost_calculator.get_cost_breakdown()
        summary = self.cost_calculator.get_usage_summary()

        # 更新表格
        self.cost_table.setRowCount(len(breakdown))

        for row, item in enumerate(breakdown):
            self.cost_table.setItem(row, 0, QTableWidgetItem(item['display_name']))
            self.cost_table.setItem(row, 1, QTableWidgetItem(str(item['requests'])))
            self.cost_table.setItem(row, 2, QTableWidgetItem(f"{item['total_tokens']:,}"))
            self.cost_table.setItem(row, 3, QTableWidgetItem(f"{item['input_tokens']:,}"))
            self.cost_table.setItem(row, 4, QTableWidgetItem(f"{item['output_tokens']:,}"))
            self.cost_table.setItem(row, 5, QTableWidgetItem(f"${item['cost']:.4f}"))

        # 更新總計
        self.total_requests_label.setText(f"總請求次數: {summary['total_requests']}")
        self.total_tokens_label.setText(f"總 Token 使用: {summary['total_tokens']:,}")
        self.total_cost_label.setText(f"總預估費用: ${summary['total_cost']:.4f}")

    def reset_cost_statistics(self):
        """重置成本統計"""
        self.cost_calculator.reset_statistics()
        self.update_cost_statistics()
        self.log_message("成本統計已重置")

    def _map_display_name_to_key(self, display_name: str) -> str:
        """
        將顯示名稱映射到模型鍵值

        Args:
            display_name: GUI 中顯示的模型名稱

        Returns:
            str: 對應的模型鍵值
        """
        # 模型顯示名稱到鍵值的映射（按長度排序，避免部分匹配問題）
        model_mapping = [
            ('GPT-4o mini', 'openai-gpt4o-mini'),  # 先匹配較長的名稱
            ('GPT-4o', 'openai-gpt4o'),
            ('Claude Opus 4', 'anthropic-opus4'),
            ('Claude Sonnet 4', 'anthropic-sonnet4'),
            ('Gemini 2.5 Pro', 'google-pro25'),
            ('Gemini 2.5 Flash', 'google-flash25')
        ]

        # 嘗試從顯示名稱中提取模型名稱（按順序匹配）
        for model_name, model_key in model_mapping:
            if model_name in display_name:
                return model_key

        # 如果沒有找到映射，嘗試從顯示名稱推斷
        if 'GPT-4o mini' in display_name or 'gpt-4o-mini' in display_name.lower():
            return 'openai-gpt4o-mini'
        elif 'GPT-4o' in display_name or 'gpt-4o' in display_name.lower():
            return 'openai-gpt4o'
        elif 'Claude Opus 4' in display_name or 'opus 4' in display_name.lower():
            return 'anthropic-opus4'
        elif 'Claude Sonnet 4' in display_name or 'sonnet 4' in display_name.lower():
            return 'anthropic-sonnet4'
        elif 'Gemini 2.5 Pro' in display_name or '2.5 pro' in display_name.lower():
            return 'google-pro25'
        elif 'Gemini 2.5 Flash' in display_name or '2.5 flash' in display_name.lower():
            return 'google-flash25'

        # 最後的備用方案，記錄警告
        self.log_message(f"⚠️ 無法映射模型名稱: {display_name}，使用預設值")
        return 'openai-gpt4o-mini'  # 預設為最經濟的模型

    # ==================== 新增的方法 ====================

    def select_seo_directory(self):
        """選擇 SEO 資料夾"""
        dir_path = QFileDialog.getExistingDirectory(
            self, self.i18n.t("seo_folder"), ""
        )

        if dir_path:
            self.seo_dir_label.setText(os.path.basename(dir_path))
            self.seo_manager.set_seo_directory(dir_path)

            # 驗證 SEO 目錄
            validation = self.seo_manager.validate_seo_directory(dir_path)
            if validation['valid']:
                self.log_message(f"已選擇 SEO 資料夾: {os.path.basename(dir_path)} ({validation['total_files']} 個檔案)")
                # 更新 SEO 欄位選項
                self.update_seo_column_options()
                # 保存設定
                self.settings_manager.update_file_settings(seo_dir=dir_path)
            else:
                self.log_message(f"SEO 資料夾警告: {validation['message']}")

    def update_seo_column_options(self):
        """更新 SEO 欄位選項"""
        if not self.excel_processor:
            return

        columns = self.excel_processor.get_columns()

        # 更新 SEO 類型欄位下拉選單
        self.seo_column_combo.clear()
        self.seo_column_combo.addItem("無")
        self.seo_column_combo.addItems(columns)

        # 更新產品名稱欄位下拉選單
        self.product_name_column_combo.clear()
        self.product_name_column_combo.addItem("無")
        self.product_name_column_combo.addItems(columns)

        # 自動選擇包含 "seo" 的欄位
        for column in columns:
            if 'seo' in column.lower():
                self.seo_column_combo.setCurrentText(column)
                break

        # 自動選擇包含 "name" 或 "名稱" 的欄位
        for column in columns:
            if any(keyword in column.lower() for keyword in ['name', '名稱', 'product', '產品']):
                self.product_name_column_combo.setCurrentText(column)
                break

    def preview_seo_files(self):
        """預覽 SEO 檔案"""
        if not self.seo_manager.seo_dir:
            QMessageBox.warning(self, self.i18n.t("warning"), "請先選擇 SEO 資料夾")
            return

        seo_types = self.seo_manager.get_available_seo_types()
        if not seo_types:
            QMessageBox.information(self, self.i18n.t("info"), "SEO 資料夾中沒有找到有效的檔案")
            return

        # 顯示 SEO 類型選擇對話框
        from PyQt5.QtWidgets import QInputDialog
        seo_type, ok = QInputDialog.getItem(
            self, "選擇 SEO 類型", "請選擇要預覽的 SEO 類型:", seo_types, 0, False
        )

        if ok and seo_type:
            seo_data = self.seo_manager.preview_seo_file(seo_type)

            # 顯示預覽對話框
            from gui.dialogs import SEOPreviewDialog
            dialog = SEOPreviewDialog(self, self.i18n, seo_data)
            dialog.exec_()

    def reset_results(self):
        """重置結果"""
        reply = QMessageBox.question(
            self, "確認", "確定要重置所有處理結果嗎？這將清除所有 HTML 輸出和評估結果。",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 清除處理結果
            self.processing_results = []
            self.processed_rows = []
            self.clear_html_results()

            # 清除 Excel 中的輸出欄位
            if self.excel_processor:
                # 移除輸出欄位
                columns_to_remove = ["HTML Output", "Used Keywords", "Review Notes"]
                for col in columns_to_remove:
                    if col in self.excel_processor.data.columns:
                        self.excel_processor.data.drop(columns=[col], inplace=True)

                # 重置表格顏色
                self.reset_table_colors()

                # 更新資料表格
                self.update_data_table()

            # 清除預覽
            self.html_preview.setHtml("<p>無內容</p>")
            self.html_source.clear()
            self.reviewer_result.clear()

            # 重置進度條
            self.progress_bar.setValue(0)

            # 重置按鈕狀態
            self.export_btn.setEnabled(False)
            self.reset_results_btn.setEnabled(False)
            self.review_only_btn.setEnabled(False)

            # 重置成本統計
            self.cost_calculator.reset_statistics()
            self.update_cost_statistics()

            self.log_message("✅ 所有結果已重置")

    def reset_table_colors(self):
        """重置表格顏色"""
        if not self.data_table:
            return

        # 移除所有行的背景顏色
        for row in range(self.data_table.rowCount()):
            for col in range(self.data_table.columnCount()):
                item = self.data_table.item(row, col)
                if item:
                    item.setBackground(Qt.white)

    def mark_processed_row(self, row_index: int):
        """標記已處理的行為綠色"""
        if not self.data_table or row_index >= self.data_table.rowCount():
            return

        # 將整行標記為淺綠色
        from PyQt5.QtGui import QColor
        green_color = QColor(144, 238, 144)  # 淺綠色

        for col in range(self.data_table.columnCount()):
            item = self.data_table.item(row_index, col)
            if item:
                item.setBackground(green_color)

        # 記錄已處理的行
        if row_index not in self.processed_rows:
            self.processed_rows.append(row_index)

    def change_language(self, language: str):
        """更改語言"""
        self.i18n.set_language(language)
        self.settings_manager.set_language(language)

        # 顯示重啟提示
        QMessageBox.information(
            self, "語言設定",
            "語言設定已更改，請重新啟動程式以應用新的語言設定。\n\nLanguage setting changed. Please restart the application to apply the new language."
        )

    def show_api_keys_dialog(self):
        """顯示 API 金鑰設定對話框"""
        from gui.dialogs import APIKeysDialog

        current_keys = {
            "openai": self.settings_manager.get_api_key("openai"),
            "anthropic": self.settings_manager.get_api_key("anthropic"),
            "google": self.settings_manager.get_api_key("google")
        }

        dialog = APIKeysDialog(self, self.i18n, current_keys)
        if dialog.exec_() == QDialog.Accepted:
            new_keys = dialog.get_api_keys()

            # 保存 API 金鑰
            for provider, key in new_keys.items():
                if key:  # 只保存非空的金鑰
                    self.settings_manager.update_api_key(provider, key)

            self.log_message("✅ API 金鑰已更新")

            # 重新初始化 AI 管理器
            try:
                self.ai_manager = AIModelManager(self.config, self.cost_calculator)
                self.load_available_options()
                self.log_message("✅ AI 模型已重新載入")
            except Exception as e:
                self.log_message(f"⚠️ AI 模型重新載入失敗: {e}")

    def load_saved_settings(self):
        """載入保存的設定"""
        try:
            # 載入檔案路徑
            excel_file = self.settings_manager.get("last_excel_file")
            if excel_file and os.path.exists(excel_file):
                self.excel_file_label.setText(os.path.basename(excel_file))
                self.excel_file_path = excel_file

            image_dir = self.settings_manager.get("last_image_dir")
            if image_dir and os.path.exists(image_dir):
                self.image_dir_label.setText(os.path.basename(image_dir))
                self.image_dir_path = image_dir
                self.image_processor = ImageProcessor(image_dir)

            seo_dir = self.settings_manager.get("last_seo_dir")
            if seo_dir and os.path.exists(seo_dir):
                self.seo_dir_label.setText(os.path.basename(seo_dir))
                self.seo_manager.set_seo_directory(seo_dir)

            # 載入 AI 設定
            writer_ai = self.settings_manager.get("writer_ai_model")
            if writer_ai:
                # 在模型載入後設定
                QTimer.singleShot(100, lambda: self.set_combo_by_data(self.writer_ai_combo, writer_ai))

            reviewer_ai = self.settings_manager.get("reviewer_ai_model")
            if reviewer_ai:
                QTimer.singleShot(100, lambda: self.set_combo_by_data(self.reviewer_ai_combo, reviewer_ai))

            # 載入其他設定
            excluded_columns = self.settings_manager.get("excluded_columns", [])
            enable_images = self.settings_manager.get("enable_images", False)
            self.enable_images_cb.setChecked(enable_images)

            start_row = self.settings_manager.get("start_row", 1)
            end_row = self.settings_manager.get("end_row", 10000)
            self.start_row_spin.setValue(start_row)
            self.end_row_spin.setValue(end_row)

            self.log_message("✅ 設定已載入")

        except Exception as e:
            self.log_message(f"⚠️ 載入設定失敗: {e}")

    def set_combo_by_data(self, combo: QComboBox, data_value: str):
        """根據 data 值設定下拉選單"""
        for i in range(combo.count()):
            if combo.itemData(i) == data_value:
                combo.setCurrentIndex(i)
                break

    def save_current_settings(self):
        """保存當前設定"""
        try:
            # 保存檔案路徑
            if hasattr(self, 'excel_file_path'):
                self.settings_manager.update_file_settings(excel_file=self.excel_file_path)

            if hasattr(self, 'image_dir_path'):
                self.settings_manager.update_file_settings(image_dir=self.image_dir_path)

            # 保存 AI 設定
            writer_ai = self.writer_ai_combo.currentData()
            reviewer_ai = self.reviewer_ai_combo.currentData()
            writer_prompt = self.writer_prompt_combo.currentText()
            reviewer_prompt = self.reviewer_prompt_combo.currentText()

            self.settings_manager.update_ai_settings(
                writer_ai=writer_ai,
                reviewer_ai=reviewer_ai,
                writer_prompt=writer_prompt,
                reviewer_prompt=reviewer_prompt
            )

            # 保存欄位設定
            excluded_columns = []
            for i in range(self.excluded_columns_list.count()):
                item = self.excluded_columns_list.item(i)
                if item.checkState() == Qt.Checked:
                    excluded_columns.append(item.text())

            image_column = self.image_column_combo.currentText()
            enable_images = self.enable_images_cb.isChecked()

            self.settings_manager.update_column_settings(
                excluded_columns=excluded_columns,
                image_column=image_column,
                enable_images=enable_images
            )

            # 保存範圍設定
            self.settings_manager.update_range_settings(
                start_row=self.start_row_spin.value(),
                end_row=self.end_row_spin.value()
            )

            # 保存視窗設定
            geometry = {
                "x": self.x(),
                "y": self.y(),
                "width": self.width(),
                "height": self.height()
            }
            self.settings_manager.update_window_settings(geometry=geometry)

        except Exception as e:
            self.log_message(f"⚠️ 保存設定失敗: {e}")

    def closeEvent(self, event):
        """視窗關閉事件"""
        # 保存當前設定
        self.save_current_settings()

        # 停止處理執行緒
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.cancel()
            self.processing_thread.wait(3000)  # 等待最多 3 秒

        event.accept()

    # ==================== HTML 預覽導航方法 ====================

    def update_html_navigation(self):
        """更新 HTML 導航狀態"""
        total = len(self.html_results)
        current = self.current_html_index + 1 if total > 0 else 0

        self.html_current_label.setText(self.i18n.t("current_item", current, total))
        self.html_prev_btn.setEnabled(self.current_html_index > 0)
        self.html_next_btn.setEnabled(self.current_html_index < total - 1)

    def show_previous_html(self):
        """顯示上一個 HTML"""
        if self.current_html_index > 0:
            self.current_html_index -= 1
            self.display_current_html()
            self.update_html_navigation()

    def show_next_html(self):
        """顯示下一個 HTML"""
        if self.current_html_index < len(self.html_results) - 1:
            self.current_html_index += 1
            self.display_current_html()
            self.update_html_navigation()

    def display_current_html(self):
        """顯示當前 HTML"""
        if not self.html_results or self.current_html_index >= len(self.html_results):
            return

        current_result = self.html_results[self.current_html_index]
        html_content = current_result.get('html_content', '')
        reviewer_notes = current_result.get('reviewer_notes', '')
        product_name = current_result.get('product_name', '')

        # 根據語言選擇顯示不同版本
        if self.current_preview_language == "en":
            # 如果有英文版本，顯示英文版本
            html_content = current_result.get('html_content_en', html_content)

        # 顯示產品名稱資訊
        if product_name:
            name_info = self.i18n.t("product_name_exists", product_name)
        else:
            name_info = self.i18n.t("product_name_created", "自動生成")

        self.log_message(name_info)

        # 顯示 HTML 和評估結果
        self.show_html_preview(html_content, reviewer_notes)

    def update_html_preview_language(self):
        """更新 HTML 預覽語言"""
        language_code = self.preview_language_combo.currentData()
        if language_code:
            self.current_preview_language = language_code
            self.display_current_html()

    def add_html_result(self, result_data):
        """添加 HTML 結果"""
        self.html_results.append(result_data)
        self.update_html_navigation()

    def clear_html_results(self):
        """清除 HTML 結果"""
        self.html_results = []
        self.current_html_index = 0
        self.update_html_navigation()

    # ==================== 產品名稱處理方法 ====================

    def get_product_name_from_data(self, row_data):
        """從資料中獲取產品名稱"""
        product_name_column = self.product_name_column_combo.currentText()

        if product_name_column == "無" or not product_name_column:
            return None

        try:
            if product_name_column in row_data:
                name = str(row_data[product_name_column]).strip()
                return name if name and name != 'nan' else None
        except Exception as e:
            self.log_message(f"⚠️ 獲取產品名稱失敗: {e}")

        return None

    def should_create_new_name(self, existing_name):
        """判斷是否需要創建新名稱"""
        if not existing_name:
            return True

        # 檢查是否為空值或無效值
        if existing_name.lower() in ['nan', 'null', 'none', '', '無', '空']:
            return True

        return False

    def log_product_name_status(self, existing_name, created_name=None):
        """記錄產品名稱狀態"""
        if existing_name and not self.should_create_new_name(existing_name):
            message = self.i18n.t("product_name_exists", existing_name)
        else:
            name_to_show = created_name or "自動生成"
            message = self.i18n.t("product_name_created", name_to_show)

        self.log_message(message)

    # ==================== 設定保存增強 ====================

    def save_current_settings(self):
        """保存當前設定（增強版）"""
        try:
            # 保存檔案路徑
            if hasattr(self, 'excel_file_path'):
                self.settings_manager.update_file_settings(excel_file=self.excel_file_path)

            if hasattr(self, 'image_dir_path'):
                self.settings_manager.update_file_settings(image_dir=self.image_dir_path)

            # 保存工作表選擇
            current_sheet = self.sheet_combo.currentText()
            if current_sheet:
                self.settings_manager.set("last_worksheet", current_sheet)

            # 保存 AI 設定
            writer_ai = self.writer_ai_combo.currentData()
            reviewer_ai = self.reviewer_ai_combo.currentData()
            writer_prompt = self.writer_prompt_combo.currentText()
            reviewer_prompt = self.reviewer_prompt_combo.currentText()

            self.settings_manager.update_ai_settings(
                writer_ai=writer_ai,
                reviewer_ai=reviewer_ai,
                writer_prompt=writer_prompt,
                reviewer_prompt=reviewer_prompt
            )

            # 保存 AI 模型的顯示文字（用於恢復選擇）
            self.settings_manager.set("writer_ai_display", self.writer_ai_combo.currentText())
            self.settings_manager.set("reviewer_ai_display", self.reviewer_ai_combo.currentText())

            # 保存欄位設定
            excluded_columns = []
            for i in range(self.excluded_columns_list.count()):
                item = self.excluded_columns_list.item(i)
                if item.checkState() == Qt.Checked:
                    excluded_columns.append(item.text())

            image_column = self.image_column_combo.currentText()
            seo_column = self.seo_column_combo.currentText()
            product_name_column = self.product_name_column_combo.currentText()
            enable_images = self.enable_images_cb.isChecked()

            self.settings_manager.update_column_settings(
                excluded_columns=excluded_columns,
                image_column=image_column,
                enable_images=enable_images
            )

            # 保存新的欄位設定
            self.settings_manager.set("seo_column", seo_column)
            self.settings_manager.set("product_name_column", product_name_column)
            self.settings_manager.set("preview_language", self.current_preview_language)

            # 保存範圍設定
            self.settings_manager.update_range_settings(
                start_row=self.start_row_spin.value(),
                end_row=self.end_row_spin.value()
            )

            # 保存視窗設定
            geometry = {
                "x": self.x(),
                "y": self.y(),
                "width": self.width(),
                "height": self.height()
            }
            self.settings_manager.update_window_settings(geometry=geometry)

        except Exception as e:
            self.log_message(f"⚠️ 保存設定失敗: {e}")

    def load_saved_settings(self):
        """載入保存的設定（增強版）"""
        try:
            # 載入檔案路徑
            excel_file = self.settings_manager.get("last_excel_file")
            if excel_file and os.path.exists(excel_file):
                self.excel_file_label.setText(os.path.basename(excel_file))
                self.excel_file_path = excel_file

                # 載入工作表名稱
                if not self.excel_processor:
                    self.excel_processor = ExcelProcessor()

                sheet_names = self.excel_processor.get_sheet_names(excel_file)
                self.sheet_combo.clear()
                self.sheet_combo.addItems(sheet_names)

                # 恢復上次選擇的工作表
                last_worksheet = self.settings_manager.get("last_worksheet")
                if last_worksheet and last_worksheet in sheet_names:
                    self.sheet_combo.setCurrentText(last_worksheet)

            image_dir = self.settings_manager.get("last_image_dir")
            if image_dir and os.path.exists(image_dir):
                self.image_dir_label.setText(os.path.basename(image_dir))
                self.image_dir_path = image_dir
                self.image_processor = ImageProcessor(image_dir)

            seo_dir = self.settings_manager.get("last_seo_dir")
            if seo_dir and os.path.exists(seo_dir):
                self.seo_dir_label.setText(os.path.basename(seo_dir))
                self.seo_manager.set_seo_directory(seo_dir)

            # 載入 AI 設定 - 使用顯示文字恢復選擇
            writer_ai_display = self.settings_manager.get("writer_ai_display")
            if writer_ai_display:
                QTimer.singleShot(200, lambda: self.set_combo_by_text(self.writer_ai_combo, writer_ai_display))

            reviewer_ai_display = self.settings_manager.get("reviewer_ai_display")
            if reviewer_ai_display:
                QTimer.singleShot(200, lambda: self.set_combo_by_text(self.reviewer_ai_combo, reviewer_ai_display))

            # 載入 Prompt 設定
            writer_prompt = self.settings_manager.get("writer_prompt")
            if writer_prompt:
                QTimer.singleShot(300, lambda: self.set_combo_by_text(self.writer_prompt_combo, writer_prompt))

            reviewer_prompt = self.settings_manager.get("reviewer_prompt")
            if reviewer_prompt:
                QTimer.singleShot(300, lambda: self.set_combo_by_text(self.reviewer_prompt_combo, reviewer_prompt))

            # 載入欄位設定
            seo_column = self.settings_manager.get("seo_column", "無")
            product_name_column = self.settings_manager.get("product_name_column", "無")
            image_column = self.settings_manager.get("image_column", "無")
            preview_language = self.settings_manager.get("preview_language", "zh")

            # 延遲設定欄位選擇（等待資料載入）
            QTimer.singleShot(400, lambda: self.restore_column_settings(seo_column, product_name_column, image_column))

            # 設定預覽語言
            self.current_preview_language = preview_language
            if hasattr(self, 'preview_language_combo'):
                if preview_language == "en":
                    self.preview_language_combo.setCurrentIndex(1)
                else:
                    self.preview_language_combo.setCurrentIndex(0)

            # 載入排除欄位設定
            excluded_columns = self.settings_manager.get("excluded_columns", [])
            QTimer.singleShot(500, lambda: self.restore_excluded_columns(excluded_columns))

            # 載入其他設定
            enable_images = self.settings_manager.get("enable_images", False)
            self.enable_images_cb.setChecked(enable_images)

            start_row = self.settings_manager.get("start_row", 1)
            end_row = self.settings_manager.get("end_row", 10000)
            self.start_row_spin.setValue(start_row)
            self.end_row_spin.setValue(end_row)

            self.log_message("✅ 設定已載入")

        except Exception as e:
            self.log_message(f"⚠️ 載入設定失敗: {e}")

    def set_combo_by_text(self, combo: QComboBox, text_value: str):
        """根據文字值設定下拉選單"""
        index = combo.findText(text_value)
        if index >= 0:
            combo.setCurrentIndex(index)

    def restore_column_settings(self, seo_column: str, product_name_column: str, image_column: str):
        """恢復欄位設定"""
        try:
            if seo_column != "無":
                self.set_combo_by_text(self.seo_column_combo, seo_column)

            if product_name_column != "無":
                self.set_combo_by_text(self.product_name_column_combo, product_name_column)

            if image_column != "無":
                self.set_combo_by_text(self.image_column_combo, image_column)
        except Exception as e:
            self.log_message(f"⚠️ 恢復欄位設定失敗: {e}")

    def restore_excluded_columns(self, excluded_columns: list):
        """恢復排除欄位設定"""
        try:
            for i in range(self.excluded_columns_list.count()):
                item = self.excluded_columns_list.item(i)
                if item.text() in excluded_columns:
                    item.setCheckState(Qt.Checked)
                else:
                    item.setCheckState(Qt.Unchecked)
        except Exception as e:
            self.log_message(f"⚠️ 恢復排除欄位設定失敗: {e}")
